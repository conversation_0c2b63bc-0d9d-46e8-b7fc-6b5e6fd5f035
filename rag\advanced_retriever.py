"""
Advanced retriever for WarpAI Enhanced
"""
import os
import re
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple, Union
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("advanced_retriever")

class Document:
    """A document for retrieval"""
    
    def __init__(self, content: str, metadata: Dict[str, Any] = None):
        """Initialize a document"""
        self.content = content
        self.metadata = metadata or {}
    
    def __str__(self) -> str:
        """String representation of the document"""
        return f"Document(metadata={self.metadata}, content={self.content[:50]}...)"

class Chunk:
    """A chunk of a document"""
    
    def __init__(self, content: str, metadata: Dict[str, Any] = None):
        """Initialize a chunk"""
        self.content = content
        self.metadata = metadata or {}
        self.embedding = None
    
    def __str__(self) -> str:
        """String representation of the chunk"""
        return f"Chunk(metadata={self.metadata}, content={self.content[:50]}...)"

class AdvancedRetriever:
    """Advanced retriever for WarpAI Enhanced"""
    
    def __init__(self, cache_dir: Optional[str] = None):
        """Initialize the advanced retriever"""
        # Set up cache directory
        if cache_dir is None:
            cache_dir = os.path.join(os.path.expanduser("~"), ".warpai", "cache")
        self.cache_dir = cache_dir
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Initialize document store
        self.documents: Dict[str, Document] = {}
        self.chunks: Dict[str, Chunk] = {}
        
        # Load cache
        self._load_cache()
    
    def _load_cache(self) -> None:
        """Load the cache"""
        cache_file = os.path.join(self.cache_dir, "documents.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r") as f:
                    data = json.load(f)
                
                # Load documents
                for doc_id, doc_data in data.get("documents", {}).items():
                    self.documents[doc_id] = Document(
                        content=doc_data["content"],
                        metadata=doc_data["metadata"]
                    )
                
                # Load chunks
                for chunk_id, chunk_data in data.get("chunks", {}).items():
                    self.chunks[chunk_id] = Chunk(
                        content=chunk_data["content"],
                        metadata=chunk_data["metadata"]
                    )
                
                logger.info(f"Loaded {len(self.documents)} documents and {len(self.chunks)} chunks from cache")
            except Exception as e:
                logger.error(f"Error loading cache: {e}")
    
    def _save_cache(self) -> None:
        """Save the cache"""
        cache_file = os.path.join(self.cache_dir, "documents.json")
        try:
            # Prepare data
            data = {
                "documents": {
                    doc_id: {
                        "content": doc.content,
                        "metadata": doc.metadata
                    } for doc_id, doc in self.documents.items()
                },
                "chunks": {
                    chunk_id: {
                        "content": chunk.content,
                        "metadata": chunk.metadata
                    } for chunk_id, chunk in self.chunks.items()
                }
            }
            
            # Save data
            with open(cache_file, "w") as f:
                json.dump(data, f)
            
            logger.info(f"Saved {len(self.documents)} documents and {len(self.chunks)} chunks to cache")
        except Exception as e:
            logger.error(f"Error saving cache: {e}")
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Add a document to the retriever"""
        # Create document ID
        doc_id = self._create_id(content)
        
        # Create document
        document = Document(content=content, metadata=metadata)
        
        # Add document to store
        self.documents[doc_id] = document
        
        # Chunk the document
        self._chunk_document(doc_id)
        
        # Save cache
        self._save_cache()
        
        return doc_id
    
    def _create_id(self, content: str) -> str:
        """Create an ID for a document or chunk"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _chunk_document(self, doc_id: str) -> None:
        """Chunk a document"""
        document = self.documents[doc_id]
        
        # Get document type
        doc_type = document.metadata.get("type", "text")
        
        # Chunk based on document type
        if doc_type == "code":
            chunks = self._chunk_code(document.content)
        else:
            chunks = self._chunk_text(document.content)
        
        # Add chunks to store
        for chunk_content in chunks:
            chunk_id = self._create_id(chunk_content)
            
            # Create chunk metadata
            chunk_metadata = document.metadata.copy()
            chunk_metadata["doc_id"] = doc_id
            
            # Create chunk
            chunk = Chunk(content=chunk_content, metadata=chunk_metadata)
            
            # Add chunk to store
            self.chunks[chunk_id] = chunk
    
    def _chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Chunk text into smaller pieces"""
        chunks = []
        
        # Split text into paragraphs
        paragraphs = re.split(r'\n\s*\n', text)
        
        current_chunk = ""
        for paragraph in paragraphs:
            # If adding this paragraph would exceed the chunk size, add the current chunk to the list
            if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                chunks.append(current_chunk)
                
                # Start a new chunk with overlap
                words = current_chunk.split()
                overlap_words = min(len(words), overlap // 5)  # Approximate words in overlap
                current_chunk = ' '.join(words[-overlap_words:]) + "\n\n"
            
            # Add the paragraph to the current chunk
            if current_chunk:
                current_chunk += paragraph + "\n\n"
            else:
                current_chunk = paragraph + "\n\n"
        
        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _chunk_code(self, code: str) -> List[str]:
        """Chunk code into smaller pieces"""
        chunks = []
        
        # Split code into functions/classes
        # This is a simple implementation that works for Python
        # For other languages, you would need to adapt this
        
        # Split by class or function definitions
        pattern = r'(class\s+\w+|def\s+\w+)'
        parts = re.split(pattern, code)
        
        current_chunk = ""
        for i in range(0, len(parts), 2):
            # Get the definition and the body
            definition = parts[i]
            body = parts[i+1] if i+1 < len(parts) else ""
            
            # Add to the current chunk
            current_chunk += definition + body
            
            # If the chunk is large enough, add it to the list
            if len(current_chunk) > 500:
                chunks.append(current_chunk)
                current_chunk = ""
        
        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)
        
        # If no chunks were created (e.g., no functions/classes), use the whole code
        if not chunks:
            chunks = [code]
        
        return chunks
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for chunks relevant to the query"""
        # Simple keyword search for now
        results = []
        
        for chunk_id, chunk in self.chunks.items():
            # Calculate a simple relevance score
            score = self._calculate_relevance(query, chunk.content)
            
            if score > 0:
                results.append({
                    "chunk_id": chunk_id,
                    "content": chunk.content,
                    "metadata": chunk.metadata,
                    "score": score
                })
        
        # Sort by relevance score
        results.sort(key=lambda x: x["score"], reverse=True)
        
        # Return top k results
        return results[:top_k]
    
    def _calculate_relevance(self, query: str, content: str) -> float:
        """Calculate the relevance of a chunk to a query"""
        # Simple keyword matching for now
        query_terms = query.lower().split()
        content_lower = content.lower()
        
        score = 0
        for term in query_terms:
            if term in content_lower:
                score += content_lower.count(term)
        
        return score
    
    def get_document(self, doc_id: str) -> Optional[Document]:
        """Get a document by ID"""
        return self.documents.get(doc_id)
    
    def get_chunk(self, chunk_id: str) -> Optional[Chunk]:
        """Get a chunk by ID"""
        return self.chunks.get(chunk_id)
    
    def clear(self) -> None:
        """Clear all documents and chunks"""
        self.documents.clear()
        self.chunks.clear()
        self._save_cache()
