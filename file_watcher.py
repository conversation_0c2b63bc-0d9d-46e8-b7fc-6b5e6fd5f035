"""
File watcher for WarpAI
"""
import os
import time
import threading
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
import hashlib

class FileWatcher:
    """Watch files for changes and trigger callbacks"""

    def __init__(self, root_dir: str = ".", patterns: List[str] = None, exclude_dirs: List[str] = None):
        """Initialize the file watcher"""
        self.root_dir = os.path.abspath(root_dir)
        self.patterns = patterns or ["*.py", "*.js", "*.html", "*.css", "*.ts", "*.jsx", "*.tsx", "*.md", "*.txt", "*.json", "*.xml", "*.yml", "*.yaml", "*.ini", "*.cfg", "*.conf", "*.sh", "*.bat", "*.ps1", "*.sql", "*.rb", "*.php", "*.java", "*.c", "*.cpp", "*.h", "*.hpp", "*.go", "*.rs", "*.swift", "*.kt", "*.scala", "*.groovy", "*.pl", "*.r", "*.m", "*.f", "*.f90", "*.hs", "*.lua", "*.jl", "*.clj", "*.ex", "*.exs", "*.erl", "*.elm", "*.ml", "*.fs", "*.cs", "*.vb", "*.pas", "*.asm", "*.s"]
        self.exclude_dirs = exclude_dirs or [".git", "node_modules", "venv", ".venv", "__pycache__", ".pytest_cache", ".warpai_cache", ".warpai_cache/web"]

        self.file_hashes = {}
        self.callbacks = []

        self.is_watching = False
        self.watch_thread = None

    def start(self, interval: float = 1.0) -> None:
        """Start watching files"""
        if self.is_watching:
            return

        self.is_watching = True
        self.watch_thread = threading.Thread(target=self._watch_loop, args=(interval,), daemon=True)
        self.watch_thread.start()

    def stop(self) -> None:
        """Stop watching files"""
        self.is_watching = False
        if self.watch_thread:
            self.watch_thread.join(timeout=1.0)
            self.watch_thread = None

    def add_callback(self, callback: Callable[[str, str], None]) -> None:
        """Add a callback to be called when a file changes"""
        self.callbacks.append(callback)

    def _watch_loop(self, interval: float) -> None:
        """Watch loop that checks for file changes"""
        # Initial scan
        self._scan_files()

        # Watch loop
        while self.is_watching:
            time.sleep(interval)
            self._check_for_changes()

    def _scan_files(self) -> None:
        """Scan files and store their hashes"""
        for root, dirs, files in os.walk(self.root_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]

            for file in files:
                # Check if file matches any pattern
                if self._matches_pattern(file):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.root_dir)

                    try:
                        file_hash = self._get_file_hash(file_path)
                        self.file_hashes[rel_path] = file_hash
                    except Exception as e:
                        print(f"Error scanning {file_path}: {e}")

    def _check_for_changes(self) -> None:
        """Check for file changes"""
        # Get current files
        current_files = set()
        for root, dirs, files in os.walk(self.root_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]

            for file in files:
                # Check if file matches any pattern
                if self._matches_pattern(file):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.root_dir)
                    current_files.add(rel_path)

                    try:
                        # Check if file is new or modified
                        file_hash = self._get_file_hash(file_path)

                        if rel_path not in self.file_hashes:
                            # New file
                            self.file_hashes[rel_path] = file_hash
                            self._trigger_callbacks(rel_path, "added")
                        elif self.file_hashes[rel_path] != file_hash:
                            # Modified file
                            self.file_hashes[rel_path] = file_hash
                            self._trigger_callbacks(rel_path, "modified")
                    except Exception as e:
                        print(f"Error checking {file_path}: {e}")

        # Check for deleted files
        deleted_files = set(self.file_hashes.keys()) - current_files
        for rel_path in deleted_files:
            del self.file_hashes[rel_path]
            self._trigger_callbacks(rel_path, "deleted")

    def _get_file_hash(self, file_path: str) -> str:
        """Get the hash of a file"""
        with open(file_path, "rb") as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash

    def _matches_pattern(self, filename: str) -> bool:
        """Check if a filename matches any pattern"""
        import fnmatch
        return any(fnmatch.fnmatch(filename, pattern) for pattern in self.patterns)

    def _trigger_callbacks(self, file_path: str, event_type: str) -> None:
        """Trigger callbacks for a file change"""
        for callback in self.callbacks:
            try:
                callback(file_path, event_type)
            except Exception as e:
                print(f"Error in callback for {file_path}: {e}")

    def get_file_content(self, file_path: str) -> Optional[str]:
        """Get the content of a file"""
        try:
            # Normalize the file path
            file_path = file_path.strip()
            abs_path = os.path.abspath(os.path.join(self.root_dir, file_path))

            # Check if the file exists
            if not os.path.isfile(abs_path):
                return None

            # Read the file content
            with open(abs_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return None

    def write_file(self, file_path: str, content: str) -> bool:
        """Write content to a file"""
        try:
            # Normalize the file path
            file_path = file_path.strip()
            abs_path = os.path.abspath(os.path.join(self.root_dir, file_path))

            # Create directory if it doesn't exist
            dir_path = os.path.dirname(abs_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # Write the content to the file
            with open(abs_path, "w", encoding="utf-8") as f:
                f.write(content)

            return True
        except Exception as e:
            print(f"Error writing {file_path}: {e}")
            return False
