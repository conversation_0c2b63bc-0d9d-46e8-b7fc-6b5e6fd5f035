"""
Advanced agent for WarpAI with RAG, multi-agent system, plugins, and self-improvement
Enhanced with 2025 AI Agent Architecture Standards
"""
from typing import Dict, List, Optional, Tuple, Union, Any, Callable, Awaitable
import os
import re
import threading
import time
import json
import logging
import asyncio
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from warpai.gemini_client import GeminiClient
from warpai.utils.command_executor import CommandExecutor
from warpai.utils.command_parser import CommandParser
from warpai.utils.context_manager import ContextManager
from warpai.utils.file_operations import FileOperations
from warpai.config.settings import settings
from warpai.config.prompts import SYSTEM_PROMPT, ADVANCED_SYSTEM_PROMPT

# Import new components
from warpai.rag.code_indexer import CodeIndexer
from warpai.rag.web_retriever import WebRetriever
from warpai.agents.agent_coordinator import AgentCoordinator
from warpai.file_watcher import FileWatcher
from warpai.plugins.plugin_manager import PluginManager
from warpai.nlp.processor import NLProcessor
from warpai.auto_execution.executor import CodeExecutor
from warpai.auto_execution.language_detector import LanguageDetector
from warpai.auto_execution.dependency_manager import DependencyManager
from warpai.auto_execution.error_handler import ErrorHandler
from warpai.intelligent_tools.tool_selector import IntelligentToolSelector
from warpai.intelligent_tools.iterative_analyzer import IterativeAnalyzer
from warpai.intelligent_tools.tool_registry import ToolRegistry

# Import modern function calling system
from warpai.core.function_calling import (
    ModernFunctionCaller, FunctionCall, FunctionResult,
    FunctionDefinition, FunctionParameter, ParameterType, FunctionCategory
)

# Set up logging
logger = logging.getLogger(__name__)

class AgentState(Enum):
    """Agent operational states"""
    IDLE = "idle"
    PROCESSING = "processing"
    EXECUTING = "executing"
    LEARNING = "learning"
    ERROR = "error"
    SHUTDOWN = "shutdown"

@dataclass
class AgentCapabilities:
    """Agent capabilities configuration"""
    rag_enabled: bool = True
    web_enabled: bool = True
    multi_agent_enabled: bool = True
    file_watcher_enabled: bool = True
    plugins_enabled: bool = True
    async_operations: bool = True
    autonomous_mode: bool = False
    learning_enabled: bool = True
    memory_persistence: bool = True

@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    uptime: float = 0.0
    last_activity: Optional[datetime] = None
    function_calls: Dict[str, int] = field(default_factory=dict)

class AdvancedAgent:
    """
    Advanced agent for WarpAI with RAG, multi-agent system, plugins, and self-improvement
    Enhanced with 2025 AI Agent Architecture Standards
    """

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None,
                 capabilities: Optional[AgentCapabilities] = None):
        """Initialize the advanced agent with modern architecture"""

        # Agent configuration
        self.capabilities = capabilities or AgentCapabilities()
        self.state = AgentState.IDLE
        self.metrics = AgentMetrics()
        self.start_time = datetime.now()

        # Core components
        self.gemini_client = GeminiClient(api_key=api_key, model=model)
        self.command_executor = CommandExecutor()
        self.command_parser = CommandParser()
        self.context_manager = ContextManager()
        self.file_operations = FileOperations()

        # Modern function calling system
        self.function_caller = ModernFunctionCaller()

        # Initialize components based on capabilities
        self.code_indexer = CodeIndexer() if self.capabilities.rag_enabled else None
        self.web_retriever = WebRetriever() if self.capabilities.web_enabled else None
        self.file_watcher = FileWatcher() if self.capabilities.file_watcher_enabled else None
        self.plugin_manager = PluginManager() if self.capabilities.plugins_enabled else None

        # Initialize NLP processor
        self.nlp_processor = NLProcessor()

        # Initialize auto-execution components
        self.code_executor = CodeExecutor()
        self.language_detector = LanguageDetector()
        self.dependency_manager = DependencyManager()
        self.error_handler = ErrorHandler()

        # Initialize ToolRegistry once
        self.tool_registry = ToolRegistry(api_key=api_key, model=model)

        # Initialize intelligent tools system
        self.tool_selector = IntelligentToolSelector(api_key=api_key, model=model, tool_registry=self.tool_registry)
        self.iterative_analyzer = IterativeAnalyzer(api_key=api_key, model=model, tool_registry=self.tool_registry)

        # Initialize AgentCoordinator
        self.agent_coordinator = AgentCoordinator(
            gemini_client=self.gemini_client,
            api_key=api_key,
            model=model
        ) if self.capabilities.multi_agent_enabled else None

        # Conversation state
        self.in_conversation = False
        self.last_command = None
        self.last_code_block = None
        self.in_file_editing_mode = False
        self.current_editing_file = None

        # Async task management
        self.background_tasks: List[asyncio.Task] = []
        self.task_queue = asyncio.Queue() if self.capabilities.async_operations else None

        # Initialize components
        self._initialize_components()

        # Register built-in functions
        self._register_builtin_functions()

        logger.info(f"Advanced agent initialized with capabilities: {self.capabilities}")

    def _initialize_components(self) -> None:
        """Initialize agent components based on capabilities"""
        if self.capabilities.rag_enabled:
            self._initialize_code_indexer()

        if self.capabilities.file_watcher_enabled:
            self._initialize_file_watcher()

        if self.capabilities.plugins_enabled:
            self._initialize_plugins()

        if self.capabilities.async_operations:
            self._initialize_async_components()

    def _initialize_async_components(self) -> None:
        """Initialize async components"""
        # Start background task processor
        if self.task_queue:
            task = asyncio.create_task(self._process_background_tasks())
            self.background_tasks.append(task)

    async def _process_background_tasks(self) -> None:
        """Process background tasks from the queue"""
        while True:
            try:
                task_func = await self.task_queue.get()
                if task_func is None:  # Shutdown signal
                    break
                await task_func()
                self.task_queue.task_done()
            except Exception as e:
                logger.error(f"Error processing background task: {e}")

    def _register_builtin_functions(self) -> None:
        """Register built-in functions with the modern function caller"""
        # File operations
        self.function_caller.register_function(
            FunctionDefinition(
                name="read_file",
                description="Read contents of a file",
                parameters={
                    "file_path": FunctionParameter(
                        type=ParameterType.STRING,
                        description="Path to the file to read",
                        required=True
                    ),
                    "encoding": FunctionParameter(
                        type=ParameterType.STRING,
                        description="File encoding (default: utf-8)",
                        required=False,
                        default="utf-8"
                    )
                },
                returns={"type": "object", "description": "File content and metadata"},
                category=FunctionCategory.FILE_OPERATIONS,
                tags=["file", "read", "content"]
            ),
            self._read_file_impl
        )

        # Add more built-in functions...
        logger.info("Registered built-in functions with modern function caller")

    async def _read_file_impl(self, file_path: str, encoding: str = "utf-8") -> Dict[str, Any]:
        """Implementation for read_file function"""
        try:
            content, status = self.file_operations.read_file(file_path)
            if status == 0:
                return {
                    "success": True,
                    "content": content,
                    "file_path": file_path,
                    "encoding": encoding,
                    "size": len(content)
                }
            else:
                return {
                    "success": False,
                    "error": content,
                    "file_path": file_path
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }

    async def process_input_async(self, user_input: str) -> str:
        """Async version of process_input with modern architecture"""
        start_time = time.time()
        self.state = AgentState.PROCESSING
        self.metrics.total_requests += 1
        self.metrics.last_activity = datetime.now()

        try:
            # Check for special commands
            if user_input.lower() == "exit":
                return "exit"
            elif user_input.lower() == "help":
                return "help"
            elif user_input.lower() == "clear":
                return "clear"
            elif user_input.lower() == "reset":
                self.context_manager.clear_history()
                self.gemini_client.reset_chat()
                return "Conversation history has been reset."
            elif user_input.lower() == "status":
                return self._get_agent_status()
            elif user_input.lower() == "metrics":
                return self._get_agent_metrics()

            # Process with modern function calling if applicable
            if self._is_function_call_request(user_input):
                return await self._process_function_call(user_input)

            # Add user input to context
            self.context_manager.add_to_history("user", user_input)

            # Process input through NLP pipeline
            nlp_results = self.nlp_processor.process(user_input)
            logger.debug(f"NLP processing results: {nlp_results}")

            # Determine processing strategy
            if nlp_results["is_natural_language"]:
                response = await self._process_natural_language_async(user_input, nlp_results)
            else:
                if settings.is_valid_shell_command(user_input):
                    response = await self._process_command_async(user_input)
                else:
                    response = await self._process_natural_language_async(user_input, nlp_results)

            # Add response to context
            self.context_manager.add_to_history("assistant", response)

            # Process code blocks and commands in the response
            response = self._process_code_and_commands(response)

            # Update metrics
            self.metrics.successful_requests += 1
            execution_time = time.time() - start_time
            self.metrics.average_response_time = (
                (self.metrics.average_response_time * (self.metrics.successful_requests - 1) + execution_time)
                / self.metrics.successful_requests
            )

            self.state = AgentState.IDLE
            return response

        except Exception as e:
            self.metrics.failed_requests += 1
            self.state = AgentState.ERROR
            logger.error(f"Error processing input: {e}")
            return f"Error processing request: {str(e)}"

    def process_input(self, user_input: str) -> str:
        """Process user input and return a response"""
        # Check for special commands
        if user_input.lower() == "exit":
            return "exit"
        elif user_input.lower() == "help":
            return "help"
        elif user_input.lower() == "clear":
            return "clear"
        elif user_input.lower() == "reset":
            self.context_manager.clear_history()
            self.gemini_client.reset_chat()
            return "Conversation history has been reset."
        elif user_input.lower() == "index":
            if self.enable_rag and self.code_indexer:
                threading.Thread(target=self.code_indexer.index_codebase, daemon=True).start()
                return "Re-indexing codebase in the background."
            else:
                return "RAG system is not enabled."

        # Check if we're in file editing mode
        if self.in_file_editing_mode:
            return self._handle_file_editing(user_input)

        # Check if it's a file editing command
        if user_input.startswith("edit "):
            file_path = user_input[5:].strip()
            return self._start_file_editing(file_path)

        # Check if it's a web search command
        if user_input.startswith("web ") and self.enable_web and self.web_retriever:
            query = user_input[4:].strip()
            return self._handle_web_search(query)

        # Check if it's a code search command
        if user_input.startswith("search ") and self.enable_rag and self.code_indexer:
            query = user_input[7:].strip()
            return self._handle_code_search(query)

        # Add user input to context
        self.context_manager.add_to_history("user", user_input)

        # Check if input is natural language or a command
        if self.command_parser.is_natural_language(user_input):
            # Process as natural language
            return self._process_natural_language(user_input)
        else:
            # Process as command
            return self._process_command(user_input)

    def _initialize_code_indexer(self) -> None:
        """Initialize the code indexer"""
        # Start indexing in a background thread
        threading.Thread(target=self.code_indexer.index_codebase, daemon=True).start()
        logger.info("Started code indexing in background thread")

    def _initialize_file_watcher(self) -> None:
        """Initialize the file watcher"""
        # Add callback for file changes
        self.file_watcher.add_callback(self._handle_file_change)

        # Start watching files
        self.file_watcher.start()
        logger.info("Started file watcher")

    def _initialize_plugins(self) -> None:
        """Initialize the plugin system"""
        if not self.plugin_manager:
            return

        # Discover available plugins
        self.plugin_manager.discover_plugins()

        # Initialize discovered plugins
        self.plugin_manager.initialize_plugins()

        # Log the loaded plugins
        plugins = self.plugin_manager.get_plugins()
        plugin_names = ", ".join(plugins.keys()) if plugins else "none"
        logger.info(f"Loaded plugins: {plugin_names}")

    def _handle_file_change(self, file_path: str, event_type: str) -> None:
        """Handle file changes"""
        # Skip cache files and hidden files
        if ".warpai_cache" in file_path or file_path.startswith("."):
            return

        # Re-index the codebase when files change
        if event_type in ["added", "modified", "deleted"] and self.enable_rag and self.code_indexer:
            logger.debug(f"File change detected: {event_type} {file_path}")
            threading.Thread(target=self.code_indexer.index_codebase, daemon=True).start()

        # Notify plugins about file changes
        if self.enable_plugins and self.plugin_manager and event_type == "modified":
            self.plugin_manager.execute_hook("after_save_file", file_path)

    def process_input(self, user_input: str) -> str:
        """Synchronous wrapper for async process_input"""
        if self.capabilities.async_operations:
            # Run async version
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, create a new task
                task = asyncio.create_task(self.process_input_async(user_input))
                return asyncio.run_coroutine_threadsafe(task, loop).result()
            else:
                return asyncio.run(self.process_input_async(user_input))
        else:
            # Fallback to synchronous processing
            return self._process_input_sync(user_input)

    def _process_input_sync(self, user_input: str) -> str:
        """Synchronous version of process_input for backward compatibility"""
        # Check for empty input
        if not user_input or user_input.strip() == "":
            return "Please enter a command or question."

        # Check for special commands
        if user_input.lower() == "exit":
            return "exit"
        elif user_input.lower() == "help":
            return "help"
        elif user_input.lower() == "clear":
            return "clear"
        elif user_input.lower() == "reset":
            self.context_manager.clear_history()
            self.gemini_client.reset_chat()
            return "Conversation history has been reset."
        elif user_input.lower() == "run" and self.last_command:
            # Execute the last suggested command
            output, exit_code = self.command_executor.execute_command(self.last_command)
            cmd = self.last_command
            self.last_command = None

            if exit_code == 0:
                return f"Command executed successfully:\n\n`{cmd}`\n\nOutput:\n{output}"
            else:
                return f"Command execution failed:\n\n`{cmd}`\n\nError:\n{output}"
        elif user_input.lower().startswith("save"):
            # Save the last code block to a file
            return self._save_last_code_block(user_input)

        # Add user input to context
        self.context_manager.add_to_history("user", user_input)

        # Process input through NLP pipeline
        nlp_results = self.nlp_processor.process(user_input)
        logger.debug(f"NLP processing results: {nlp_results}")

        # Check if input is natural language or a command
        if nlp_results["is_natural_language"]:
            # Process as natural language with enhanced understanding
            return self._process_natural_language(user_input, nlp_results)
        else:
            # Check if it's a valid shell command before processing
            if settings.is_valid_shell_command(user_input):
                # Process as command
                return self._process_command(user_input)
            else:
                # Process as natural language if it's not a valid command
                return self._process_natural_language(user_input, nlp_results)

    def _get_agent_status(self) -> str:
        """Get current agent status"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        return f"""
🤖 **WarpAI Agent Status**

**State:** {self.state.value.title()}
**Uptime:** {uptime:.1f} seconds
**Capabilities:**
  • RAG: {'✅' if self.capabilities.rag_enabled else '❌'}
  • Web Search: {'✅' if self.capabilities.web_enabled else '❌'}
  • Multi-Agent: {'✅' if self.capabilities.multi_agent_enabled else '❌'}
  • File Watcher: {'✅' if self.capabilities.file_watcher_enabled else '❌'}
  • Plugins: {'✅' if self.capabilities.plugins_enabled else '❌'}
  • Async Operations: {'✅' if self.capabilities.async_operations else '❌'}
  • Autonomous Mode: {'✅' if self.capabilities.autonomous_mode else '❌'}

**Active Components:**
  • Function Caller: {len(self.function_caller.functions)} functions registered
  • Background Tasks: {len(self.background_tasks)} active
  • Plugin Manager: {len(self.plugin_manager.get_plugins()) if self.plugin_manager else 0} plugins loaded
"""

    def _get_agent_metrics(self) -> str:
        """Get agent performance metrics"""
        success_rate = (self.metrics.successful_requests / max(self.metrics.total_requests, 1)) * 100
        return f"""
📊 **WarpAI Agent Metrics**

**Requests:**
  • Total: {self.metrics.total_requests}
  • Successful: {self.metrics.successful_requests}
  • Failed: {self.metrics.failed_requests}
  • Success Rate: {success_rate:.1f}%

**Performance:**
  • Average Response Time: {self.metrics.average_response_time:.3f}s
  • Last Activity: {self.metrics.last_activity.strftime('%Y-%m-%d %H:%M:%S') if self.metrics.last_activity else 'Never'}

**Function Calls:**
{chr(10).join(f'  • {func}: {count}' for func, count in self.metrics.function_calls.items())}
"""

    def _is_function_call_request(self, user_input: str) -> bool:
        """Check if the input is a function call request"""
        # Simple heuristic - can be enhanced with NLP
        function_indicators = [
            "call function", "execute function", "run function",
            "use tool", "invoke", "function:", "tool:"
        ]
        return any(indicator in user_input.lower() for indicator in function_indicators)

    async def _process_function_call(self, user_input: str) -> str:
        """Process a function call request"""
        try:
            # Parse function call from user input
            # This is a simplified implementation - can be enhanced
            if ":" in user_input:
                parts = user_input.split(":", 1)
                if len(parts) == 2:
                    func_name = parts[1].strip().split()[0]

                    # Create function call
                    call = FunctionCall(
                        name=func_name,
                        arguments={}  # Would need to parse arguments from input
                    )

                    # Execute function
                    result = await self.function_caller.call_function(call)

                    # Update metrics
                    if func_name not in self.metrics.function_calls:
                        self.metrics.function_calls[func_name] = 0
                    self.metrics.function_calls[func_name] += 1

                    if result.success:
                        return f"Function '{func_name}' executed successfully:\n{result.result}"
                    else:
                        return f"Function '{func_name}' failed: {result.error}"

            return "Could not parse function call. Use format: 'function: function_name'"

        except Exception as e:
            logger.error(f"Error processing function call: {e}")
            return f"Error processing function call: {str(e)}"

    async def _process_natural_language_async(self, user_input: str, nlp_results: Optional[Dict[str, Any]] = None) -> str:
        """Async version of natural language processing"""
        # This would be the async version of the existing _process_natural_language method
        # For now, delegate to the sync version
        return self._process_natural_language(user_input, nlp_results)

    async def _process_command_async(self, user_input: str) -> str:
        """Async version of command processing"""
        # This would be the async version of the existing _process_command method
        # For now, delegate to the sync version
        return self._process_command(user_input)

    def _process_natural_language(self, user_input: str, nlp_results: Optional[Dict[str, Any]] = None) -> str:
        """
        Process natural language input

        Args:
            user_input: The user input text
            nlp_results: Optional NLP processing results
        """
        # Use NLP results if available, otherwise process manually
        if nlp_results:
            # Check for recognized intents with high confidence
            if "intent" in nlp_results and nlp_results["intent"]["confidence"] > 0.7:
                intent = nlp_results["intent"]["intent"]
                entities = nlp_results["entities"]

                # Handle run file intent
                if intent == "run_file" and "file_path" in entities:
                    return self._run_file(entities["file_path"])

                # Handle install package intent
                elif intent == "install_package" and "package_name" in entities:
                    return self._install_package(entities["package_name"])

                # Handle create file intent
                elif intent == "create_file" and "file_path" in entities:
                    return self._start_file_editing(entities["file_path"])

                # Handle edit file intent
                elif intent == "edit_file" and "file_path" in entities:
                    return self._start_file_editing(entities["file_path"])

                # Handle search web intent
                elif intent == "search_web" and "query" in entities:
                    if self.enable_web and self.web_retriever:
                        return self._handle_web_search(entities["query"])

                # Handle git operation intent
                elif intent == "git_operation" and "operation" in entities:
                    return self._process_command(f"git {entities['operation']}")

            # If no high-confidence intent was recognized or handled, fall back to traditional methods

        # Fall back to traditional methods
        # Check for natural language run commands
        if self._is_run_file_request(user_input):
            file_path = self._extract_file_path(user_input)
            if file_path:
                return self._run_file(file_path)

        # Check for natural language install commands
        if self._is_install_package_request(user_input):
            package = self._extract_package_name(user_input)
            if package:
                return self._install_package(package)

        # Use intelligent tool selector and iterative analyzer for advanced processing
        context = {
            "history": self.context_manager.get_history(),
            "current_directory": self.command_executor.get_current_directory(),
        }

        # Add code context if RAG is enabled
        if self.enable_rag and self.code_indexer:
            code_results = self.code_indexer.search(user_input)
            if code_results:
                context["code_context"] = code_results

        # Check if this requires iterative analysis (complex tasks)
        if self._requires_iterative_analysis(user_input):
            # Start iterative analysis session
            session_id = self.iterative_analyzer.start_analysis(user_input, context)

            # Get initial status
            status = self.iterative_analyzer.get_session_status(session_id)

            if status.get("status") == "completed":
                response = f"Task completed successfully!\n\n{status.get('last_result', {}).get('result', '')}"
            elif status.get("status") == "in_progress":
                response = f"Working on your request... Progress: {status.get('progress', 0)*100:.1f}%\n\nCurrent step: {status.get('last_result', {}).get('tool', 'Processing')}"
            else:
                response = "Started working on your request. This may take multiple steps to complete."
        else:
            # Use intelligent tool selector for simpler tasks
            tool_selections = self.tool_selector.select_tools(user_input, context)

            if tool_selections:
                # Execute the best tool selection
                best_selection = tool_selections[0]
                response = self._execute_tool_selection(best_selection)
            else:
                # Fall back to multi-agent system or traditional AI response
                if self.enable_multi_agent and self.agent_coordinator:
                    result = self.agent_coordinator.process_request(user_input, context)
                    response = result.get("response", "")
                else:
                    # Generate a response from Gemini AI with enhanced context
                    enhanced_context = self._get_enhanced_context(user_input, nlp_results)

                    prompt = f"""
User input: {user_input}

{enhanced_context}
"""

                    response = self.gemini_client.generate_response(
                        prompt=prompt,
                        system_prompt=ADVANCED_SYSTEM_PROMPT
                    )

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        # Process code blocks and commands in the response
        response = self._process_code_and_commands(response)

        return response

    def _is_run_file_request(self, user_input: str) -> bool:
        """Check if the input is a request to run a file"""
        user_input_lower = user_input.lower()

        # Run file keywords
        run_keywords = [
            "run", "execute", "start", "launch", "open"
        ]

        file_keywords = [
            "file", "script", "program", "code", "app", "application"
        ]

        # Check for run keywords
        has_run_keyword = any(keyword in user_input_lower for keyword in run_keywords)

        # Check for file keywords or file extensions
        has_file_keyword = any(keyword in user_input_lower for keyword in file_keywords)
        has_file_extension = re.search(r"\.\w+", user_input_lower) is not None

        return has_run_keyword and (has_file_keyword or has_file_extension)

    def _extract_file_path(self, user_input: str) -> Optional[str]:
        """Extract a file path from user input"""
        # Look for file paths with extensions
        file_path_match = re.search(r"[\w\-./\\]+\.\w+", user_input)
        if file_path_match:
            return file_path_match.group(0)

        # Look for quoted strings that might be file paths
        quoted_match = re.search(r"[\"']([^\"']+)[\"']", user_input)
        if quoted_match:
            return quoted_match.group(1)

        # Look for words after run keywords
        run_keywords = ["run", "execute", "start", "launch", "open"]
        for keyword in run_keywords:
            if keyword in user_input.lower():
                parts = user_input.lower().split(keyword, 1)
                if len(parts) > 1:
                    # Get the first word after the keyword
                    words = parts[1].strip().split()
                    if words:
                        # Check if it's a file name with extension
                        if "." in words[0]:
                            return words[0]
                        # Check if it's a file name without extension
                        elif len(words) > 1 and "file" in words:
                            return words[0]

        return None

    def _is_install_package_request(self, user_input: str) -> bool:
        """Check if the input is a request to install a package"""
        user_input_lower = user_input.lower()

        # Install keywords
        install_keywords = [
            "install", "download", "get", "add", "fetch"
        ]

        package_keywords = [
            "package", "module", "library", "dependency", "plugin", "extension"
        ]

        # Check for install keywords
        has_install_keyword = any(keyword in user_input_lower for keyword in install_keywords)

        # Check for package keywords
        has_package_keyword = any(keyword in user_input_lower for keyword in package_keywords)

        return has_install_keyword and has_package_keyword

    def _extract_package_name(self, user_input: str) -> Optional[str]:
        """Extract a package name from user input"""
        # Look for quoted strings that might be package names
        quoted_match = re.search(r"[\"']([^\"']+)[\"']", user_input)
        if quoted_match:
            return quoted_match.group(1)

        # Look for words after install keywords
        install_keywords = ["install", "download", "get", "add", "fetch"]
        for keyword in install_keywords:
            if keyword in user_input.lower():
                parts = user_input.lower().split(keyword, 1)
                if len(parts) > 1:
                    # Get the first word after the keyword
                    words = parts[1].strip().split()
                    if words:
                        # Skip package keywords
                        package_keywords = ["package", "module", "library", "dependency", "plugin", "extension"]
                        if words[0] in package_keywords and len(words) > 1:
                            return words[1]
                        else:
                            return words[0]

        return None

    def _requires_iterative_analysis(self, user_input: str) -> bool:
        """Determine if the user input requires iterative analysis"""
        complex_keywords = [
            "build", "create", "develop", "implement", "generate", "make",
            "complex", "multiple", "steps", "process", "workflow", "pipeline",
            "analyze", "debug", "fix", "optimize", "improve", "refactor",
            "project", "application", "system", "architecture"
        ]

        user_input_lower = user_input.lower()

        # Check for complex keywords
        complex_score = sum(1 for keyword in complex_keywords if keyword in user_input_lower)

        # Check for multiple requirements (and, then, also, etc.)
        connector_words = ["and", "then", "also", "plus", "additionally", "furthermore"]
        connector_score = sum(1 for word in connector_words if word in user_input_lower)

        # Check for file operations that might require multiple steps
        file_operations = ["create", "edit", "modify", "update", "generate"]
        file_score = sum(1 for op in file_operations if op in user_input_lower and "file" in user_input_lower)

        # Require iterative analysis if complexity score is high
        total_score = complex_score + connector_score + file_score
        return total_score >= 2 or len(user_input.split()) > 15

    def _execute_tool_selection(self, tool_selection) -> str:
        """Execute a selected tool and return the result"""
        try:
            tool = tool_selection.tool
            parameters = tool_selection.parameters

            logger.info(f"Executing tool: {tool.name} with parameters: {parameters}")

            # Execute the tool function
            result = tool.function(**parameters)

            # Format the response based on the tool result
            if result.get("success"):
                if tool.name == "shell":
                    output = result.get("output", "")
                    return f"Command executed successfully:\n\n```\n{output}\n```"

                elif tool.name == "readFile":
                    content = result.get("content", "")
                    file_path = result.get("file_path", "")
                    return f"Content of {file_path}:\n\n```\n{content}\n```"

                elif tool.name == "saveFile":
                    file_path = result.get("file_path", "")
                    return f"Successfully saved content to {file_path}"

                elif tool.name == "generateCode":
                    code = result.get("code", "")
                    language = result.get("language", "")
                    # Store the generated code for potential execution
                    self.last_code_block = code
                    return f"Generated {language} code:\n\n```{language}\n{code}\n```\n\nYou can run this code by typing 'run' or save it to a file."

                elif tool.name == "webFetch":
                    content = result.get("content", "")
                    url = result.get("url", "")
                    return f"Content from {url}:\n\n{content[:1000]}..."

                elif tool.name == "analyzeCode":
                    analysis = result.get("analysis", "")
                    return f"Code Analysis:\n\n{analysis}"

                elif tool.name == "debugCode":
                    debug_result = result.get("debug_result", "")
                    return f"Debug Analysis:\n\n{debug_result}"

                elif tool.name == "generateTests":
                    test_code = result.get("test_code", "")
                    language = result.get("language", "")
                    return f"Generated {language} tests:\n\n```{language}\n{test_code}\n```"

                elif tool.name == "installPackage":
                    package_name = result.get("package_name", "")
                    return f"Successfully installed package: {package_name}"

                elif tool.name == "listPackages":
                    packages = result.get("packages", "")
                    return f"Installed packages:\n\n```\n{packages}\n```"

                elif tool.name == "gitStatus":
                    status = result.get("status", "")
                    has_changes = result.get("has_changes", False)
                    if has_changes:
                        return f"Git repository has changes:\n\n```\n{status}\n```"
                    else:
                        return "Git repository is clean (no changes)"

                elif tool.name == "showDir":
                    content = result.get("content", "")
                    path = result.get("path", "")
                    return f"Contents of {path}:\n\n```\n{content}\n```"

                elif tool.name == "createFolder":
                    path = result.get("path", "")
                    return f"Successfully created directory: {path}"

                elif tool.name == "remember":
                    message = result.get("message", "")
                    return f"Remembered: {message}"

                else:
                    # Generic success response
                    return f"Tool {tool.name} executed successfully. Result: {result}"
            else:
                # Handle errors
                error = result.get("error", "Unknown error")
                return f"Tool {tool.name} failed: {error}"

        except Exception as e:
            logger.error(f"Error executing tool {tool_selection.tool.name}: {e}")
            return f"Error executing tool {tool_selection.tool.name}: {str(e)}"

    def _generate_code_with_ai(self, requirements: str, language: str) -> str:
        """Generate code using AI based on requirements"""
        prompt = f"""
Generate {language} code based on the following requirements:

Requirements: {requirements}

Please provide clean, well-commented, production-ready code.
Include proper error handling and follow best practices for {language}.

Code:
"""

        try:
            code = self.gemini_client.generate_response(
                prompt=prompt,
                system_prompt="You are an expert programmer. Generate clean, efficient, and well-documented code."
            )

            # Store the generated code for potential execution
            self.last_code_block = code

            return f"Generated {language} code:\n\n```{language}\n{code}\n```\n\nYou can run this code by typing 'run' or save it to a file."

        except Exception as e:
            logger.error(f"Error generating code: {e}")
            return f"Error generating code: {str(e)}"

    def _handle_file_editing(self, user_input: str) -> str:
        """Handle file editing mode"""
        if not self.current_editing_file:
            self.in_file_editing_mode = False
            return "No file is currently being edited."

        # Check for exit commands
        if user_input.lower() in ["exit", "done", "quit"]:
            self.in_file_editing_mode = False
            file_path = self.current_editing_file
            self.current_editing_file = None
            return f"Exited file editing mode for {file_path}."

        # Check for show command
        if user_input.lower() == "show":
            # Read the current file content
            content, status = self.file_operations.read_file(self.current_editing_file)
            if status != 0:
                return f"Error reading file: {content}"

            return f"Current content of {self.current_editing_file}:\n\n```\n{content}\n```"

        # Process the editing instruction
        return self._process_file_edit(user_input)

    def _start_file_editing(self, file_path: str) -> str:
        """Start file editing mode"""
        # Check if file exists
        file_info, status = self.file_operations.get_file_info(file_path)

        if status == 0 and file_info.get("exists", False) and file_info.get("is_file", False):
            # Read the file content
            content, read_status = self.file_operations.read_file(file_path)
            if read_status != 0:
                return f"Error reading file: {content}"

            # Enter file editing mode
            self.in_file_editing_mode = True
            self.current_editing_file = file_path

            return f"Now editing {file_path}. Current content:\n\n```\n{content}\n```\n\nType your instructions to modify the file, 'show' to see the current content, or 'exit' to finish editing."
        else:
            # Create a new file
            self.in_file_editing_mode = True
            self.current_editing_file = file_path

            return f"Creating new file {file_path}. Type your content or instructions, or 'exit' to finish editing."

    def _process_file_edit(self, instruction: str) -> str:
        """Process a file editing instruction"""
        if not self.current_editing_file:
            return "No file is currently being edited."

        # Read the current file content
        current_content, status = self.file_operations.read_file(self.current_editing_file)
        if status != 0 and status != 1:  # Status 1 might mean file doesn't exist yet
            current_content = ""

        # Generate the edited content based on the instruction
        prompt = f"""
I'm editing a file and need your help to modify it based on an instruction.

Current file: {self.current_editing_file}

Current content:
```
{current_content}
```

Instruction: {instruction}

Please provide the complete updated file content after applying this instruction.
Only return the file content without any explanations or markdown formatting.
"""

        edited_content = self.gemini_client.generate_response(
            prompt=prompt,
            system_prompt="You are a helpful assistant that modifies files based on instructions. Return only the modified file content without any explanations or markdown formatting."
        )

        # Clean up the response to extract just the file content
        if "```" in edited_content:
            # Extract content from code block
            import re
            match = re.search(r"```(?:\w+)?\n([\s\S]+?)\n```", edited_content)
            if match:
                edited_content = match.group(1)

        # Write the edited content to the file
        result, status = self.file_operations.write_file(self.current_editing_file, edited_content)

        if status != 0:
            return f"Error writing to file: {result}"

        return f"File {self.current_editing_file} has been updated based on your instruction."

    def _handle_web_search(self, query: str) -> str:
        """Handle web search command"""
        if not self.enable_web or not self.web_retriever:
            return "Web search is not enabled."

        # Search the web
        search_results = self.web_retriever.search_web(query)

        if not search_results:
            return "No results found. Please try a different query."

        # Format the results
        response = f"Web search results for '{query}':\n\n"

        for i, result in enumerate(search_results):
            response += f"{i+1}. [{result['title']}]({result['url']})\n"
            if 'snippet' in result and result['snippet']:
                response += f"   {result['snippet']}\n"
            response += "\n"

        return response

    def _handle_code_search(self, query: str) -> str:
        """Handle code search command"""
        if not self.enable_rag or not self.code_indexer:
            return "Code search is not enabled."

        # Search the codebase
        search_results = self.code_indexer.search(query)

        if not search_results:
            return "No code found matching your query. Please try a different search term."

        # Format the results
        response = f"Code search results for '{query}':\n\n"

        for i, result in enumerate(search_results):
            response += f"{i+1}. File: {result['path']} (Relevance: {result['similarity']:.2f})\n"

            # Add a snippet of the code (first 10 lines)
            code_lines = result['content'].split('\n')[:10]
            snippet = '\n'.join(code_lines)

            response += f"```\n{snippet}\n```\n\n"

            if i >= 4:  # Limit to 5 results
                break

        return response

    def get_last_command(self) -> Optional[str]:
        """Get the last command extracted from the response"""
        return self.last_command

    def execute_last_command(self) -> Tuple[str, int]:
        """Execute the last command"""
        if not self.last_command:
            return "No command to execute", 1

        # Execute the command
        output, exit_code = self.command_executor.execute_command(self.last_command)

        # Store the command before resetting it (in case we need to retry)
        executed_command = self.last_command

        # Reset the last command
        self.last_command = None

        # If auto-fix is enabled and the command failed, try to fix it
        if settings.auto_fix_errors and exit_code != 0:
            fixed_output, fixed_exit_code = self.fix_and_retry_command(executed_command, output)
            if fixed_exit_code == 0:
                return fixed_output, fixed_exit_code

        return output, exit_code

    def fix_and_retry_command(self, command: str, error_output: str) -> Tuple[str, int]:
        """Fix a failed command and retry it"""
        logger.info(f"Attempting to fix command: {command}")
        logger.info(f"Error output: {error_output}")

        # Generate a fixed command using Gemini
        prompt = f"""
        I tried to execute the following command, but it failed:

        ```
        {command}
        ```

        The error output was:

        ```
        {error_output}
        ```

        Please analyze the error and suggest a fixed version of the command that will work.
        Return ONLY the fixed command without any explanation or markdown formatting.
        """

        fixed_command = self.gemini_client.generate_response(
            prompt=prompt,
            system_prompt="You are a command-line expert. Your task is to fix broken commands. Return ONLY the fixed command without any explanation or markdown formatting."
        )

        # Clean up the response to extract just the command
        fixed_command = fixed_command.strip()
        if "```" in fixed_command:
            # Extract content from code block
            import re
            match = re.search(r"```(?:\w+)?\n([\s\S]+?)\n```", fixed_command)
            if match:
                fixed_command = match.group(1).strip()

        # Skip if the fixed command is the same as the original
        if fixed_command == command:
            logger.info("Could not fix command (same as original)")
            return f"Could not fix command: {command}", 1

        # Skip if the fixed command is too different from the original
        if len(fixed_command) > len(command) * 2 or len(fixed_command) < len(command) / 2:
            logger.info("Fixed command too different from original, not executing")
            return f"Generated fix too different from original command, not executing for safety", 1

        logger.info(f"Fixed command: {fixed_command}")

        # Execute the fixed command
        output, exit_code = self.command_executor.execute_command(fixed_command)

        if exit_code == 0:
            return f"Command fixed and executed successfully:\n\nOriginal: `{command}`\nFixed: `{fixed_command}`\n\nOutput:\n{output}", exit_code
        else:
            return f"Fixed command also failed:\n\nOriginal: `{command}`\nFixed: `{fixed_command}`\n\nError:\n{output}", exit_code

    async def shutdown_async(self) -> None:
        """Async shutdown of the agent and clean up resources"""
        logger.info("Shutting down advanced agent")
        self.state = AgentState.SHUTDOWN

        try:
            # Stop background tasks
            if self.task_queue:
                await self.task_queue.put(None)  # Shutdown signal

            for task in self.background_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # Stop file watcher if enabled
            if self.capabilities.file_watcher_enabled and self.file_watcher:
                try:
                    self.file_watcher.stop()
                    logger.info("File watcher stopped")
                except Exception as e:
                    logger.error(f"Error stopping file watcher: {e}")

            # Save any cached data
            if self.capabilities.rag_enabled and self.code_indexer:
                try:
                    # Save index if needed
                    logger.info("Saving code index")
                except Exception as e:
                    logger.error(f"Error saving code index: {e}")

            # Shutdown plugins
            if self.capabilities.plugins_enabled and self.plugin_manager:
                try:
                    self.plugin_manager.shutdown_plugins()
                    logger.info("Plugins shutdown")
                except Exception as e:
                    logger.error(f"Error shutting down plugins: {e}")

            # Clear any sensitive data
            self.context_manager.clear_history()

            logger.info("Advanced agent shutdown complete")

        except Exception as e:
            logger.error(f"Error during agent shutdown: {e}")

    def shutdown(self) -> None:
        """Synchronous wrapper for shutdown"""
        if self.capabilities.async_operations:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(self.shutdown_async())
                else:
                    asyncio.run(self.shutdown_async())
            except Exception as e:
                logger.error(f"Error in async shutdown: {e}")
                self._shutdown_sync()
        else:
            self._shutdown_sync()

    def _shutdown_sync(self) -> None:
        """Synchronous shutdown for backward compatibility"""
        logger.info("Shutting down advanced agent (sync)")

        # Stop file watcher if enabled
        if self.capabilities.file_watcher_enabled and self.file_watcher:
            try:
                self.file_watcher.stop()
                logger.info("File watcher stopped")
            except Exception as e:
                logger.error(f"Error stopping file watcher: {e}")

        # Save any cached data
        if self.capabilities.rag_enabled and self.code_indexer:
            try:
                # Save index if needed
                logger.info("Saving code index")
            except Exception as e:
                logger.error(f"Error saving code index: {e}")

        # Clear any sensitive data
        self.context_manager.clear_history()

        logger.info("Advanced agent shutdown complete")

    def get_capabilities(self) -> AgentCapabilities:
        """Get agent capabilities"""
        return self.capabilities

    def get_metrics(self) -> AgentMetrics:
        """Get agent metrics"""
        return self.metrics

    def get_state(self) -> AgentState:
        """Get current agent state"""
        return self.state

    def enable_autonomous_mode(self, enabled: bool = True) -> None:
        """Enable or disable autonomous mode"""
        self.capabilities.autonomous_mode = enabled
        logger.info(f"Autonomous mode {'enabled' if enabled else 'disabled'}")

    def get_function_schemas(self) -> List[Dict[str, Any]]:
        """Get all available function schemas for external integrations"""
        return self.function_caller.get_all_schemas()

    async def call_function_by_name(self, name: str, arguments: Dict[str, Any]) -> FunctionResult:
        """Call a function by name with arguments"""
        call = FunctionCall(name=name, arguments=arguments)
        return await self.function_caller.call_function(call)

    def _process_code_and_commands(self, response: str) -> str:
        """Process code blocks and commands in the response"""
        # Extract code blocks from the response
        code_blocks = self.command_parser.extract_code_blocks(response)

        # If there are code blocks, don't auto-save them, just format them nicely in the response
        if code_blocks and len(code_blocks) > 0:
            # Store the code in memory for later use if needed
            self.last_code_block = {
                "code": code_blocks[0]["code"],
                "language": code_blocks[0]["language"]
            }

            # Add a note about how to save the code
            response += "\n\nTo save this code to a file, use the 'save' command or ask me to save it."

            # If it's a Python file, suggest running it
            if code_blocks[0]["language"].lower() in ["python", "py"]:
                # Generate a default filename
                import hashlib
                code_hash = hashlib.md5(code_blocks[0]["code"].encode()).hexdigest()[:8]
                default_filename = f"script_{code_hash}.py"

                response += f"\nTo run this code, use 'save {default_filename}' and then 'run {default_filename}'."

        # Extract commands from the response using multiple approaches
        import re

        # Look for commands in backticks
        command_matches = re.findall(r"`([^`]+)`", response)

        # Look for commands after phrases like "run this command" or "execute this"
        command_phrases = [
            r"run this command:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"execute this:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"you can run:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"try running:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"use this command:?\s*(?:`)?([^`\n]+)(?:`)?",
        ]

        for phrase in command_phrases:
            phrase_match = re.search(phrase, response, re.IGNORECASE)
            if phrase_match:
                command_matches.append(phrase_match.group(1).strip())

        # If there are commands, don't execute them automatically, just suggest them
        if command_matches:
            for cmd in command_matches:
                cmd = cmd.strip()

                # Skip if it's not a command (too long or contains newlines)
                if len(cmd) > 100 or "\n" in cmd:
                    continue

                # Skip if it's a Python code snippet
                if cmd.startswith("import ") or cmd.startswith("from ") or "def " in cmd or "class " in cmd:
                    continue

                # Store the command for later execution
                self.last_command = cmd

                # Add a note about how to execute the command
                response += f"\n\nTo execute the suggested command: `{cmd}`, type 'run' or press Enter."

                # Only suggest the first command
                break

        return response

    def _is_advanced_code_request(self, user_input: str) -> bool:
        """Check if the input is a request for code generation, explanation, or error fixing"""
        user_input_lower = user_input.lower()

        # Code generation keywords
        generation_keywords = ["create", "generate", "write", "implement", "build", "develop", "make"]

        # Code explanation keywords
        explanation_keywords = ["explain", "how", "what", "why", "understand", "clarify", "describe"]

        # Error fixing keywords
        error_keywords = ["error", "bug", "fix", "issue", "problem", "not working", "fails", "debug"]

        # Check if any keyword is in the input
        for keyword in generation_keywords + explanation_keywords + error_keywords:
            if keyword in user_input_lower:
                return True

        return False

    def _process_with_agent_coordinator(self, user_input: str) -> str:
        """Process a request using the agent coordinator"""
        # Get context from conversation history
        context = {
            "conversation_history": self.context_manager.get_context_for_prompt(),
            "code_context": self._get_code_context(user_input)
        }

        # Process with agent coordinator
        result = self.agent_coordinator.process_request(user_input, context)

        # Extract response
        response = result.get("response", "")

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        # Process code blocks and commands
        final_response = self._process_code_and_commands(response)

        return final_response

    def _is_web_info_request(self, user_input: str) -> bool:
        """Check if the input is a request for web information"""
        user_input_lower = user_input.lower()

        # Web information keywords
        web_keywords = ["search", "find", "lookup", "google", "web", "internet", "online", "documentation", "docs"]

        # Check if any keyword is in the input
        for keyword in web_keywords:
            if keyword in user_input_lower:
                return True

        return False

    def _process_web_info_request(self, user_input: str) -> str:
        """Process a request for web information"""
        # Search the web
        search_results = self.web_retriever.search_web(user_input)

        if not search_results:
            return "I couldn't find any relevant information on the web. Please try a different query."

        # Fetch the first result
        first_result = search_results[0]
        web_content = self.web_retriever.fetch_url(first_result["url"])

        # Generate a response based on the web content
        prompt = f"""
User input: {user_input}

I found the following information on the web:

URL: {web_content['url']}
Title: {web_content['title']}

Content:
{web_content['content'][:2000]}  # Limit content to 2000 characters

Please provide a helpful response based on this information.
"""

        response = self.gemini_client.generate_response(
            prompt=prompt,
            system_prompt="You are a helpful assistant that provides information based on web content. Summarize the information clearly and concisely."
        )

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        # Add source information
        response += f"\n\nSource: {web_content['url']}"

        return response

    def _is_code_search_request(self, user_input: str) -> bool:
        """Check if the input is a request for code search"""
        user_input_lower = user_input.lower()

        # Code search keywords
        search_keywords = ["find code", "search code", "look for code", "where is", "find file", "search file", "look for file", "where is file", "where is the file"]

        # Check if any keyword is in the input
        for keyword in search_keywords:
            if keyword in user_input_lower:
                return True

        return False

    def _process_code_search_request(self, user_input: str) -> str:
        """Process a request for code search"""
        # Search the codebase
        search_results = self.code_indexer.search(user_input)

        if not search_results:
            return "I couldn't find any relevant code in the codebase. Please try a different query."

        # Generate a response based on the search results
        prompt = f"""
User input: {user_input}

I found the following code in the codebase:

"""

        for i, result in enumerate(search_results):
            prompt += f"\n--- File: {result['path']} (Relevance: {result['similarity']:.2f}) ---\n"

            # Add a snippet of the code (first 20 lines)
            code_lines = result['content'].split('\n')[:20]
            prompt += '\n'.join(code_lines)

            if i >= 2:  # Limit to 3 results
                break

        prompt += "\n\nPlease provide a helpful response based on these code snippets."

        response = self.gemini_client.generate_response(
            prompt=prompt,
            system_prompt="You are a helpful assistant that provides information based on code search results. Explain the code clearly and concisely."
        )

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        return response

    def _get_web_context(self, user_input: str) -> str:
        """Get web context for the user input"""
        # Check if web retriever is enabled
        if not self.enable_web or not self.web_retriever:
            return ""

        # Search the web for relevant information
        search_results = self.web_retriever.search(user_input, top_k=2)

        if not search_results:
            return ""

        # Format the search results
        web_context = "Relevant information from the web:\n\n"
        for result in search_results:
            web_context += f"Title: {result['title']}\n"
            web_context += f"URL: {result['url']}\n"
            web_context += f"Snippet: {result['snippet']}\n\n"

        return web_context

    def _get_enhanced_context(self, user_input: str, nlp_results: Optional[Dict[str, Any]] = None) -> str:
        """
        Get enhanced context for the user input

        Args:
            user_input: The user input text
            nlp_results: Optional NLP processing results
        """
        enhanced_context = ""

        # Add NLP analysis if available
        if nlp_results:
            # Add intent information
            if "intent" in nlp_results and nlp_results["intent"]["intent"] != "unknown":
                intent_info = nlp_results["intent"]
                enhanced_context += f"Detected intent: {intent_info['intent']} (confidence: {intent_info['confidence']:.2f})\n"

            # Add entity information
            if "entities" in nlp_results and nlp_results["entities"]:
                enhanced_context += "Detected entities:\n"
                for entity_type, entity_value in nlp_results["entities"].items():
                    enhanced_context += f"- {entity_type}: {entity_value}\n"
                enhanced_context += "\n"

            # Add semantic information
            if "semantics" in nlp_results and "categories" in nlp_results["semantics"]:
                categories = nlp_results["semantics"]["categories"]
                if categories:
                    top_categories = sorted(categories.items(), key=lambda x: x[1]["score"], reverse=True)[:3]
                    enhanced_context += "Semantic categories:\n"
                    for category, info in top_categories:
                        enhanced_context += f"- {category} (score: {info['score']:.2f})\n"
                    enhanced_context += "\n"

            # Add context information
            if "semantics" in nlp_results and "context" in nlp_results["semantics"]:
                context = nlp_results["semantics"]["context"]
                if context:
                    enhanced_context += "Context analysis:\n"
                    for key, value in context.items():
                        if value is True:  # Only include positive context indicators
                            enhanced_context += f"- {key.replace('_', ' ').capitalize()}\n"
                    enhanced_context += "\n"

        # Process the user input with NLP if not already done
        if not nlp_results:
            nlp_results = self.nlp_processor.process(user_input)

        # Add code context if available
        code_context = self._get_code_context(user_input)
        if code_context:
            enhanced_context += f"Code context:\n{code_context}\n\n"

        # Add web context if available
        web_context = self._get_web_context(user_input)
        if web_context:
            enhanced_context += f"Web context:\n{web_context}\n\n"

        # Add conversation history
        conversation_history = self.context_manager.get_context_for_prompt()
        if conversation_history:
            enhanced_context += f"Conversation history:\n{conversation_history}\n\n"

        return enhanced_context

    def _get_code_context(self, user_input: str) -> str:
        """Get code context for the user input"""
        # Check if code indexer is enabled
        if not self.enable_rag or not self.code_indexer:
            return ""

        # Search the codebase for relevant code
        search_results = self.code_indexer.search(user_input, top_k=2)

        if not search_results:
            return ""

        code_context = ""
        for result in search_results:
            code_context += f"File: {result['path']}\n"

            # Add a snippet of the code (first 10 lines)
            code_lines = result['content'].split('\n')[:10]
            code_context += '\n'.join(code_lines)
            code_context += "\n...\n\n"

        return code_context

    def _is_valid_shell_command(self, command: str) -> bool:
        """Check if a string is likely a valid shell command"""
        # Skip empty commands
        if not command or command.strip() == "":
            return False

        # Common shell commands
        common_commands = [
            # File operations
            "ls", "dir", "cd", "pwd", "mkdir", "rmdir", "touch", "rm", "cp", "mv", "cat", "type",
            # System info
            "ps", "top", "htop", "free", "df", "du", "uname", "systeminfo", "whoami", "hostname",
            # Network
            "ping", "tracert", "traceroute", "netstat", "ifconfig", "ipconfig", "curl", "wget", "ssh",
            # Package managers
            "apt", "apt-get", "yum", "dnf", "brew", "pip", "npm", "gem", "conda", "choco",
            # Version control
            "git", "svn", "hg",
            # Programming languages
            "python", "python3", "node", "java", "javac", "gcc", "g++", "make", "dotnet", "go", "ruby", "perl",
            # Text editors
            "vim", "nano", "emacs", "code", "notepad",
            # Process management
            "kill", "taskkill", "bg", "fg", "jobs", "nohup",
            # Compression
            "tar", "zip", "unzip", "gzip", "gunzip",
            # File permissions
            "chmod", "chown", "attrib",
            # Search
            "find", "grep", "findstr", "select-string",
            # Other common tools
            "echo", "printf", "set", "export", "env", "man", "help", "clear", "cls", "history",
            # Windows specific
            "powershell", "cmd", "tasklist", "schtasks", "net", "reg",
        ]

        # Check if the command starts with any common command
        first_word = command.split()[0].lower()
        if first_word in common_commands:
            return True

        # Check for command patterns
        command_patterns = [
            # Executable with arguments
            r"^[a-zA-Z0-9_\-\.]+\s+.*$",
            # Path to executable
            r"^\.{0,2}/[a-zA-Z0-9_\-\./]+$",
            r"^[a-zA-Z]:\\[a-zA-Z0-9_\-\\\s\.]+$",
            # Command with options
            r"^[a-zA-Z0-9_\-\.]+\s+\-{1,2}[a-zA-Z0-9]+",
        ]

        for pattern in command_patterns:
            if re.match(pattern, command):
                return True

        return False

    def _get_file_extension(self, language: str) -> str:
        """Get file extension based on language"""
        language = language.lower()
        extensions = {
            # Common programming languages
            "python": "py",
            "javascript": "js",
            "typescript": "ts",
            "jsx": "jsx",
            "tsx": "tsx",
            "html": "html",
            "css": "css",
            "scss": "scss",
            "sass": "sass",
            "less": "less",
            "java": "java",
            "c": "c",
            "cpp": "cpp",
            "c++": "cpp",
            "csharp": "cs",
            "c#": "cs",
            "go": "go",
            "golang": "go",
            "rust": "rs",
            "ruby": "rb",
            "php": "php",
            "swift": "swift",
            "kotlin": "kt",
            "scala": "scala",
            "perl": "pl",
            "lua": "lua",
            "r": "r",
            "dart": "dart",
            "haskell": "hs",
            "elixir": "ex",
            "erlang": "erl",
            "clojure": "clj",
            "groovy": "groovy",
            "julia": "jl",
            "fortran": "f90",
            "cobol": "cbl",

            # Shell and scripting
            "shell": "sh",
            "bash": "sh",
            "zsh": "sh",
            "powershell": "ps1",
            "batch": "bat",
            "cmd": "bat",

            # Data formats
            "sql": "sql",
            "json": "json",
            "yaml": "yaml",
            "yml": "yml",
            "xml": "xml",
            "csv": "csv",
            "tsv": "tsv",
            "toml": "toml",
            "ini": "ini",
            "conf": "conf",

            # Markup and documentation
            "markdown": "md",
            "latex": "tex",
            "tex": "tex",
            "restructuredtext": "rst",
            "asciidoc": "adoc",

            # Web technologies
            "graphql": "graphql",
            "protobuf": "proto",
            "thrift": "thrift",
            "vue": "vue",
            "svelte": "svelte",
            "angular": "ts",
            "react": "jsx",

            # Configuration and build
            "dockerfile": "Dockerfile",
            "makefile": "Makefile",
            "cmake": "cmake",
            "gradle": "gradle",
            "maven": "xml",

            # Other
            "text": "txt",
            "plain": "txt",
            "plaintext": "txt",
        }
        return extensions.get(language, "txt")

    def _generate_filename(self, code: str, extension: str) -> str:
        """Generate a filename based on code content"""
        import hashlib
        import time

        # Generate a hash of the code
        code_hash = hashlib.md5(code.encode()).hexdigest()[:8]

        # Get current timestamp
        timestamp = int(time.time())

        # Generate filename
        filename = f"warpai_code_{timestamp}_{code_hash}.{extension}"

        return filename

    def _process_command(self, command: str) -> str:
        """Process a command"""
        # Check if it's a plugin command
        if self.enable_plugins and self.plugin_manager:
            # Check if any plugin can handle this command
            plugin_response = self.plugin_manager.execute_command(command)
            if plugin_response is not None:
                # Add response to context
                self.context_manager.add_to_history("assistant", plugin_response)
                return plugin_response

        # Check if it's a run command
        if command.lower().startswith("run "):
            # Extract the file path
            file_path = command[4:].strip()
            return self._run_file(file_path)

        # Check if it's an install command
        if command.lower().startswith("install "):
            # Extract the package name
            package = command[8:].strip()
            return self._install_package(package)

        # Check if it's a plugin-related command
        if command.lower() == "plugins" and self.enable_plugins and self.plugin_manager:
            # List available plugins
            plugins = self.plugin_manager.get_plugins()
            if not plugins:
                return "No plugins are currently loaded."

            response = "Available plugins:\n\n"
            for name, plugin in plugins.items():
                response += f"- {name} v{plugin.version}: {plugin.description}\n"

                # List commands provided by the plugin
                commands = plugin.get_commands()
                if commands:
                    response += "  Commands:\n"
                    for cmd_name in commands.keys():
                        response += f"  - {cmd_name}\n"

            return response

        # Check if it's a code analysis command
        if command.lower().startswith("analyze ") and self.enable_plugins and self.plugin_manager:
            # Check if the code analyzer plugin is available
            analyzer_plugin = self.plugin_manager.get_plugin("code_analyzer")
            if analyzer_plugin:
                # Extract the file path
                file_path = command[8:].strip()
                return analyzer_plugin.analyze_file(file_path)

        # Notify plugins before executing command
        if self.enable_plugins and self.plugin_manager:
            modified_command = self.plugin_manager.execute_hook("before_execute_command", command)
            if modified_command:
                command = modified_command

        # Execute the command
        output, exit_code = self.command_executor.execute_command(command)

        # Notify plugins after executing command
        if self.enable_plugins and self.plugin_manager:
            modified_output = self.plugin_manager.execute_hook("after_execute_command", command, exit_code, output)
            if modified_output:
                output = modified_output[0] if isinstance(modified_output, list) and modified_output else output

        # If the command failed, analyze the error
        if exit_code != 0:
            response = self.gemini_client.analyze_error(output)

            # Process response through plugins
            if self.enable_plugins and self.plugin_manager:
                modified_response = self.plugin_manager.execute_hook("on_error", response)
                if modified_response:
                    response = modified_response[0] if isinstance(modified_response, list) and modified_response else response

            self.context_manager.add_to_history("assistant", response)
            return response

        # Analyze the command output
        response = self.gemini_client.analyze_command_output(command, output)

        # Process response through plugins
        if self.enable_plugins and self.plugin_manager:
            modified_response = self.plugin_manager.execute_hook("on_response", response)
            if modified_response:
                response = modified_response[0] if isinstance(modified_response, list) and modified_response else response

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        return response

    def _run_file(self, file_path: str) -> str:
        """Run a file with auto-execution capabilities"""
        logger.info(f"Running file: {file_path}")

        # Check if file exists
        if not os.path.isfile(file_path):
            return f"Error: File '{file_path}' not found."

        # Check if auto-execution is enabled
        if self.auto_execute_enabled:
            # Use the code executor to run the file
            result = self.code_executor.execute_file(
                file_path=file_path,
                auto_install_deps=self.auto_install_deps,
                auto_fix_errors=self.auto_fix_errors
            )

            # Format the result
            output = []

            # Add language and command information
            if result["language"]:
                output.append(f"Language detected: {result['language']}")
            if result["command"]:
                output.append(f"Execution command: {result['command']}")

            # Add execution status
            if result["success"]:
                output.append("Execution successful!")
            else:
                output.append("Execution failed.")

            # Add output and error
            if result["output"]:
                output.append("\nOutput:")
                output.append(result["output"])

            if result["error"]:
                output.append("\nError:")
                output.append(result["error"])

            return "\n".join(output)
        else:
            # Fall back to basic execution
            output, exit_code = self.command_executor.run_file(file_path)

            if exit_code == 0:
                return f"Successfully ran {file_path}:\n\n{output}"
            else:
                return f"Error running {file_path}:\n\n{output}"

    def _install_package(self, package: str) -> str:
        """Install a package"""
        logger.info(f"Installing package: {package}")

        # Determine the package manager based on the package name
        if package.startswith("pip:"):
            # Python package
            package = package[4:].strip()
            command = f"pip install {package}"
        elif package.startswith("npm:"):
            # Node.js package
            package = package[4:].strip()
            command = f"npm install {package}"
        elif package.startswith("gem:"):
            # Ruby package
            package = package[4:].strip()
            command = f"gem install {package}"
        elif package.startswith("apt:"):
            # Ubuntu/Debian package
            package = package[4:].strip()
            command = f"apt-get install -y {package}"
        elif package.startswith("brew:"):
            # Homebrew package
            package = package[4:].strip()
            command = f"brew install {package}"
        elif package.startswith("choco:"):
            # Chocolatey package (Windows)
            package = package[6:].strip()
            command = f"choco install -y {package}"
        else:
            # Try to determine the package manager based on the package name
            if package.endswith(".py") or package.startswith("python-"):
                command = f"pip install {package}"
            elif package.endswith(".js") or package.startswith("node-"):
                command = f"npm install {package}"
            else:
                # Default to pip
                command = f"pip install {package}"

        # Execute the command
        output, exit_code = self.command_executor.execute_command(command)

        if exit_code == 0:
            return f"Successfully installed {package}:\n\n{output}"
        else:
            return f"Error installing {package}:\n\n{output}"

    # These methods are now implemented above

    def _save_last_code_block(self, user_input: str) -> str:
        """Save the last code block to a file"""
        if not self.last_code_block:
            return "No code to save. Generate some code first."

        # Parse the save command to get the filename
        parts = user_input.split(maxsplit=1)

        # Get code and language information
        code = self.last_code_block["code"]
        language = self.last_code_block["language"].lower()

        # Detect the actual language from the code content if needed
        detected_language = self._detect_language_from_content(code)
        if detected_language and detected_language != language:
            language = detected_language

        # Get the appropriate file extension
        extension = self._get_file_extension(language)

        if len(parts) > 1:
            # User provided a filename
            filename = parts[1].strip()

            # Add extension if not provided
            if '.' not in filename:
                filename = f"{filename}.{extension}"
        else:
            # Generate a more meaningful filename based on the content
            import re

            # Try to extract a class or function name from the code
            class_match = re.search(r"class\s+(\w+)", code)
            function_match = re.search(r"(?:def|function)\s+(\w+)", code)

            if class_match:
                name = class_match.group(1).lower()
            elif function_match:
                name = function_match.group(1).lower()
            else:
                # Use a generic name based on the language
                name = f"code_{language}"

            filename = f"{name}.{extension}"

        try:
            # Ensure the code is properly formatted for the language
            formatted_code = self._format_code_for_language(code, language)

            # Save the code to the file
            with open(filename, "w", encoding="utf-8") as f:
                f.write(formatted_code)

            # Clear the last code block
            self.last_code_block = None

            return f"Code saved to {filename}:\n\n```{language}\n{formatted_code[:100]}{'...' if len(formatted_code) > 100 else ''}\n```"
        except Exception as e:
            return f"Error saving code: {str(e)}"

    def _detect_language_from_content(self, code: str) -> Optional[str]:
        """Detect the programming language from code content"""
        import re

        # Check for Python
        if re.search(r"def\s+\w+\s*\(.*\):", code) or re.search(r"import\s+\w+", code):
            return "python"

        # Check for JavaScript/TypeScript
        if re.search(r"function\s+\w+\s*\(.*\)", code) or re.search(r"const\s+\w+\s*=", code) or re.search(r"let\s+\w+\s*=", code):
            if "interface " in code or ": string" in code or ": number" in code:
                return "typescript"
            return "javascript"

        # Check for HTML
        if re.search(r"<!DOCTYPE html>|<html>|<body>|<head>", code, re.IGNORECASE):
            return "html"

        # Check for CSS
        if re.search(r"{\s*[\w-]+\s*:\s*\w+", code):
            return "css"

        # Check for Java
        if re.search(r"public\s+class\s+\w+", code) or re.search(r"private\s+\w+\s+\w+\s*\(", code):
            return "java"

        # Check for C/C++
        if re.search(r"#include\s+[<\"].*[>\"]", code) or re.search(r"int\s+main\s*\(", code):
            if re.search(r"std::", code) or re.search(r"class\s+\w+\s*{", code):
                return "cpp"
            return "c"

        # Check for Shell/Bash
        if re.search(r"^#!\s*/bin/(ba)?sh", code) or re.search(r"echo\s+[\"\']", code):
            return "bash"

        # Check for SQL
        if re.search(r"SELECT\s+.*\s+FROM\s+", code, re.IGNORECASE) or re.search(r"CREATE\s+TABLE", code, re.IGNORECASE):
            return "sql"

        # Default to text if can't detect
        return None

    def _format_code_for_language(self, code: str, language: str) -> str:
        """Format code appropriately for the given language"""
        # Remove unnecessary backticks that might be in the code
        code = re.sub(r'^```.*\n', '', code)
        code = re.sub(r'\n```$', '', code)

        # Add appropriate headers/shebang based on language
        if language == "python" and not code.startswith("#!/usr/bin/env python"):
            if not code.startswith("#"):
                code = f"#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\n{code}"
        elif language == "bash" and not code.startswith("#!"):
            code = f"#!/bin/bash\n\n{code}"
        elif language == "javascript" and not code.startswith("//"):
            if not (code.startswith("/*") or code.startswith("//")):
                code = f"// JavaScript code\n\n{code}"
        elif language == "html" and not code.startswith("<!DOCTYPE"):
            if not code.startswith("<"):
                code = f"<!DOCTYPE html>\n<html>\n<head>\n    <title>HTML Document</title>\n</head>\n<body>\n{code}\n</body>\n</html>"

        return code

    def clear_last_command(self) -> None:
        """Clear the last suggested command"""
        self.last_command = None

    def save_code_to_file(self, code: str, filename: str) -> str:
        """Save code to a file"""
        result, exit_code = self.file_operations.write_file(filename, code)
        if exit_code == 0:
            return f"Code saved to {filename} successfully."
        else:
            return f"Error saving code to {filename}: {result}"

    def create_live_file(self, filename: str, initial_content: str = "") -> bool:
        """Create a live file that will be watched for changes"""
        try:
            # Save the initial content to the file directly
            with open(filename, "w", encoding="utf-8") as f:
                f.write(initial_content)
            return True
        except Exception as e:
            print(f"Error creating file {filename}: {e}")
            return False

    def get_live_file_content(self, filename: str) -> Optional[str]:
        """Get the content of a live file"""
        try:
            with open(filename, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            print(f"Error reading file {filename}: {e}")
            return None

    def update_live_file(self, filename: str, content: str) -> bool:
        """Update the content of a live file"""
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"Error updating file {filename}: {e}")
            return False

    def shutdown(self) -> None:
        """Shutdown the agent"""
        # Stop the file watcher
        if self.enable_file_watcher and self.file_watcher:
            self.file_watcher.stop()

        # Shutdown plugins
        if self.enable_plugins and self.plugin_manager:
            self.plugin_manager.shutdown_plugins()
            logger.info("Plugins shut down")
