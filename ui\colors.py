"""
Color utilities for WarpAI
"""
from typing import Dict, Optional
from colorama import Fore, Back, Style, init

# Initialize colorama
init()

class Colors:
    """Color utilities for WarpAI"""
    
    # ANSI color codes
    RESET = Style.RESET_ALL
    BOLD = Style.BRIGHT
    DIM = Style.DIM
    
    # Foreground colors
    BLACK = Fore.BLACK
    RED = Fore.RED
    GREEN = Fore.GREEN
    YELLOW = Fore.YELLOW
    BLUE = Fore.BLUE
    MAGENTA = Fore.MAGENTA
    CYAN = Fore.CYAN
    WHITE = Fore.WHITE
    
    # Background colors
    BG_BLACK = Back.BLACK
    BG_RED = Back.RED
    BG_GREEN = Back.GREEN
    BG_YELLOW = Back.YELLOW
    BG_BLUE = Back.BLUE
    BG_MAGENTA = Back.MAGENTA
    BG_CYAN = Back.CYAN
    BG_WHITE = Back.WHITE
    
    @staticmethod
    def colorize(text: str, color: str, background: Optional[str] = None, style: Optional[str] = None) -> str:
        """Colorize text with ANSI color codes"""
        result = ""
        
        # Add style
        if style:
            result += style
        
        # Add background color
        if background:
            result += background
        
        # Add foreground color
        result += color
        
        # Add text
        result += text
        
        # Reset
        result += Colors.RESET
        
        return result
    
    @staticmethod
    def success(text: str) -> str:
        """Format text as success message"""
        return Colors.colorize(text, Colors.GREEN, style=Colors.BOLD)
    
    @staticmethod
    def error(text: str) -> str:
        """Format text as error message"""
        return Colors.colorize(text, Colors.RED, style=Colors.BOLD)
    
    @staticmethod
    def warning(text: str) -> str:
        """Format text as warning message"""
        return Colors.colorize(text, Colors.YELLOW, style=Colors.BOLD)
    
    @staticmethod
    def info(text: str) -> str:
        """Format text as info message"""
        return Colors.colorize(text, Colors.BLUE, style=Colors.BOLD)
    
    @staticmethod
    def prompt(text: str) -> str:
        """Format text as prompt"""
        return Colors.colorize(text, Colors.CYAN, style=Colors.BOLD)
    
    @staticmethod
    def highlight(text: str) -> str:
        """Format text as highlighted"""
        return Colors.colorize(text, Colors.WHITE, background=Colors.BG_BLUE, style=Colors.BOLD)
    
    @staticmethod
    def code(text: str) -> str:
        """Format text as code"""
        return Colors.colorize(text, Colors.WHITE, background=Colors.BG_BLACK, style=Colors.DIM)
