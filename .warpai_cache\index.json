{"advanced_agent.py": {"size": 69890, "path": "advanced_agent.py", "abs_path": "D:\\Sandeep\\warpai\\advanced_agent.py", "content_hash": "c1c68788fb1b5e56b766f0c9a54bffb6"}, "advanced_cli.py": {"size": 3100, "path": "advanced_cli.py", "abs_path": "D:\\Sandeep\\warpai\\advanced_cli.py", "content_hash": "79d18a4c5fc245a3f309621007d6266e"}, "advanced_terminal.py": {"size": 20516, "path": "advanced_terminal.py", "abs_path": "D:\\Sandeep\\warpai\\advanced_terminal.py", "content_hash": "10000e2ee0608a645b16cd08556cb7a4"}, "agent.py": {"size": 8669, "path": "agent.py", "abs_path": "D:\\Sandeep\\warpai\\agent.py", "content_hash": "a06644bb3dadfe9a436f843005cd9c7f"}, "cli.py": {"size": 1391, "path": "cli.py", "abs_path": "D:\\Sandeep\\warpai\\cli.py", "content_hash": "a198a5804d3dbd051d046290039a0465"}, "file_watcher.py": {"size": 6692, "path": "file_watcher.py", "abs_path": "D:\\Sandeep\\warpai\\file_watcher.py", "content_hash": "7a4d94146a4f9336a1524dece82e4c5e"}, "gemini_client.py": {"size": 3418, "path": "gemini_client.py", "abs_path": "D:\\Sandeep\\warpai\\gemini_client.py", "content_hash": "7df1184928b4012bfd6932175f8b7c39"}, "terminal.py": {"size": 14719, "path": "terminal.py", "abs_path": "D:\\Sandeep\\warpai\\terminal.py", "content_hash": "26b9a3b72a249e37dcb6ccfea9322d61"}, "__init__.py": {"size": 97, "path": "__init__.py", "abs_path": "D:\\Sandeep\\warpai\\__init__.py", "content_hash": "6976a5b621658ea4572d3284003ece36"}, "agents\\agent_coordinator.py": {"size": 11457, "path": "agents\\agent_coordinator.py", "abs_path": "D:\\Sandeep\\warpai\\agents\\agent_coordinator.py", "content_hash": "fd59f4eb689c07eb4707b09358042880"}, "agents\\base_agent.py": {"size": 6488, "path": "agents\\base_agent.py", "abs_path": "D:\\Sandeep\\warpai\\agents\\base_agent.py", "content_hash": "45616bb10e1a89ee58c603d9856be145"}, "agents\\code_explainer.py": {"size": 3848, "path": "agents\\code_explainer.py", "abs_path": "D:\\Sandeep\\warpai\\agents\\code_explainer.py", "content_hash": "369eab3f3ad6705abe818bfa971a6800"}, "agents\\code_generator.py": {"size": 3526, "path": "agents\\code_generator.py", "abs_path": "D:\\Sandeep\\warpai\\agents\\code_generator.py", "content_hash": "b742491fae1f87df3dc5c940a9ba98f4"}, "agents\\error_fixer.py": {"size": 4633, "path": "agents\\error_fixer.py", "abs_path": "D:\\Sandeep\\warpai\\agents\\error_fixer.py", "content_hash": "********************************"}, "agents\\__init__.py": {"size": 38, "path": "agents\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\agents\\__init__.py", "content_hash": "85f55d531598a9c3ecf2bbc8da14aaea"}, "agent_mode\\agent.py": {"size": 13064, "path": "agent_mode\\agent.py", "abs_path": "D:\\Sandeep\\warpai\\agent_mode\\agent.py", "content_hash": "cc00353ce54d5d9489360ef07936441c"}, "agent_mode\\autonomy.py": {"size": 7282, "path": "agent_mode\\autonomy.py", "abs_path": "D:\\Sandeep\\warpai\\agent_mode\\autonomy.py", "content_hash": "35ae07792ad494d811293aeb47ee6f63"}, "agent_mode\\conversation.py": {"size": 11500, "path": "agent_mode\\conversation.py", "abs_path": "D:\\Sandeep\\warpai\\agent_mode\\conversation.py", "content_hash": "ba5986a8f646f990078c9a630c98b459"}, "agent_mode\\detector.py": {"size": 22604, "path": "agent_mode\\detector.py", "abs_path": "D:\\Sandeep\\warpai\\agent_mode\\detector.py", "content_hash": "5a27ef4404561aed5c5042758e8800fd"}, "agent_mode\\dispatch.py": {"size": 11361, "path": "agent_mode\\dispatch.py", "abs_path": "D:\\Sandeep\\warpai\\agent_mode\\dispatch.py", "content_hash": "7df0f203979a1456fca6b60352df8e68"}, "agent_mode\\__init__.py": {"size": 575, "path": "agent_mode\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\agent_mode\\__init__.py", "content_hash": "7f8944e8af172f964fece522ab556b65"}, "auto_execution\\dependency_manager.py": {"size": 13651, "path": "auto_execution\\dependency_manager.py", "abs_path": "D:\\Sandeep\\warpai\\auto_execution\\dependency_manager.py", "content_hash": "fb05518e746ae28f52dfbfabe9b58671"}, "auto_execution\\error_handler.py": {"size": 16552, "path": "auto_execution\\error_handler.py", "abs_path": "D:\\Sandeep\\warpai\\auto_execution\\error_handler.py", "content_hash": "89dfe505c836996db9824f08a73b26a4"}, "auto_execution\\executor.py": {"size": 13358, "path": "auto_execution\\executor.py", "abs_path": "D:\\Sandeep\\warpai\\auto_execution\\executor.py", "content_hash": "2fd17015384b8028d848a59a607d7520"}, "auto_execution\\language_detector.py": {"size": 10303, "path": "auto_execution\\language_detector.py", "abs_path": "D:\\Sandeep\\warpai\\auto_execution\\language_detector.py", "content_hash": "1ce9032f02d9fb6c5b2349ed1298369d"}, "auto_execution\\__init__.py": {"size": 576, "path": "auto_execution\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\auto_execution\\__init__.py", "content_hash": "e61aaa2155d84eada46a09988ea7f7be"}, "config\\enhanced_settings.py": {"size": 5001, "path": "config\\enhanced_settings.py", "abs_path": "D:\\Sandeep\\warpai\\config\\enhanced_settings.py", "content_hash": "8586055ffcd9f1f98ca4d7ce8274d0ba"}, "config\\prompts.py": {"size": 5900, "path": "config\\prompts.py", "abs_path": "D:\\Sandeep\\warpai\\config\\prompts.py", "content_hash": "43444d52f7b211c7623a8c4e03b668cd"}, "config\\settings.py": {"size": 9669, "path": "config\\settings.py", "abs_path": "D:\\Sandeep\\warpai\\config\\settings.py", "content_hash": "57d5c9af29e4537d71aaa690336119cc"}, "config\\__init__.py": {"size": 33, "path": "config\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\config\\__init__.py", "content_hash": "a84c00699cab94cac7a9fc0a2cb91c44"}, "intelligent_tools\\iterative_analyzer.py": {"size": 18163, "path": "intelligent_tools\\iterative_analyzer.py", "abs_path": "D:\\Sandeep\\warpai\\intelligent_tools\\iterative_analyzer.py", "content_hash": "73f250883b84d3ad74c95f5fdb1cffa6"}, "intelligent_tools\\tool_implementations.py": {"size": 18660, "path": "intelligent_tools\\tool_implementations.py", "abs_path": "D:\\Sandeep\\warpai\\intelligent_tools\\tool_implementations.py", "content_hash": "8fc061b16d8c2262f76a4bc788a81bbb"}, "intelligent_tools\\tool_registry.py": {"size": 10212, "path": "intelligent_tools\\tool_registry.py", "abs_path": "D:\\Sandeep\\warpai\\intelligent_tools\\tool_registry.py", "content_hash": "94468b6fa1f2ac7a32a687dee054427f"}, "intelligent_tools\\tool_selector.py": {"size": 12968, "path": "intelligent_tools\\tool_selector.py", "abs_path": "D:\\Sandeep\\warpai\\intelligent_tools\\tool_selector.py", "content_hash": "7fe29f07cd8ede65ec0d1510628a5b70"}, "intelligent_tools\\__init__.py": {"size": 381, "path": "intelligent_tools\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\intelligent_tools\\__init__.py", "content_hash": "ead770ef8054497efdba90163490ad7c"}, "models\\model_manager.py": {"size": 10656, "path": "models\\model_manager.py", "abs_path": "D:\\Sandeep\\warpai\\models\\model_manager.py", "content_hash": "bc89eac275462c8f5c37ddcd90367d20"}, "nlp\\entity_extraction.py": {"size": 5929, "path": "nlp\\entity_extraction.py", "abs_path": "D:\\Sandeep\\warpai\\nlp\\entity_extraction.py", "content_hash": "5d547c13d7d54116d63db746a3c63473"}, "nlp\\intent_recognition.py": {"size": 7375, "path": "nlp\\intent_recognition.py", "abs_path": "D:\\Sandeep\\warpai\\nlp\\intent_recognition.py", "content_hash": "05a463244ca8627a233a9ab02a32f2c1"}, "nlp\\processor.py": {"size": 8714, "path": "nlp\\processor.py", "abs_path": "D:\\Sandeep\\warpai\\nlp\\processor.py", "content_hash": "8c0486c95317a95146fded5f7f69e68e"}, "nlp\\semantic_understanding.py": {"size": 7618, "path": "nlp\\semantic_understanding.py", "abs_path": "D:\\Sandeep\\warpai\\nlp\\semantic_understanding.py", "content_hash": "864604e40a2339e45cfd3f84ab2eb605"}, "nlp\\__init__.py": {"size": 515, "path": "nlp\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\nlp\\__init__.py", "content_hash": "57480af9405defd562058b666e99c8d9"}, "plugins\\plugin_manager.py": {"size": 6720, "path": "plugins\\plugin_manager.py", "abs_path": "D:\\Sandeep\\warpai\\plugins\\plugin_manager.py", "content_hash": "bbcf857ebb735f6232a7468726d912c2"}, "plugins\\builtin\\code_analyzer\\__init__.py": {"size": 12810, "path": "plugins\\builtin\\code_analyzer\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\plugins\\builtin\\code_analyzer\\__init__.py", "content_hash": "19629143c80835ce775c71a01591b4ce"}, "plugins\\builtin\\dependency_manager\\__init__.py": {"size": 12382, "path": "plugins\\builtin\\dependency_manager\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\plugins\\builtin\\dependency_manager\\__init__.py", "content_hash": "2ae5f75d45a18f75591eac6a9f63e937"}, "plugins\\builtin\\git_helper\\__init__.py": {"size": 6505, "path": "plugins\\builtin\\git_helper\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\plugins\\builtin\\git_helper\\__init__.py", "content_hash": "8d7dc65c1510b06973de45d7a10fd9a8"}, "rag\\advanced_retriever.py": {"size": 9629, "path": "rag\\advanced_retriever.py", "abs_path": "D:\\Sandeep\\warpai\\rag\\advanced_retriever.py", "content_hash": "6f381c139b4c489f3c9f54501d6e8c21"}, "rag\\code_indexer.py": {"size": 7326, "path": "rag\\code_indexer.py", "abs_path": "D:\\Sandeep\\warpai\\rag\\code_indexer.py", "content_hash": "335db0401223ba7ccacef6aa7bfea738"}, "rag\\web_retriever.py": {"size": 5301, "path": "rag\\web_retriever.py", "abs_path": "D:\\Sandeep\\warpai\\rag\\web_retriever.py", "content_hash": "fee4c9eaf924058961548003bc1142b9"}, "rag\\__init__.py": {"size": 63, "path": "rag\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\rag\\__init__.py", "content_hash": "0fb4c8b6406cd2d86739ee2c0cd19544"}, "ui\\colors.py": {"size": 2545, "path": "ui\\colors.py", "abs_path": "D:\\Sandeep\\warpai\\ui\\colors.py", "content_hash": "72a7948d0545b52764ac674a8ec48fbe"}, "ui\\display.py": {"size": 7575, "path": "ui\\display.py", "abs_path": "D:\\Sandeep\\warpai\\ui\\display.py", "content_hash": "9d0b2e68f826bb8393f8c539633b84d1"}, "ui\\keyboard_help.py": {"size": 2940, "path": "ui\\keyboard_help.py", "abs_path": "D:\\Sandeep\\warpai\\ui\\keyboard_help.py", "content_hash": "949261078b8b7c35cef99da91ed81db2"}, "ui\\__init__.py": {"size": 33, "path": "ui\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\ui\\__init__.py", "content_hash": "91eae494a9832bd39c739e23491c337a"}, "utils\\command_executor.py": {"size": 24566, "path": "utils\\command_executor.py", "abs_path": "D:\\Sandeep\\warpai\\utils\\command_executor.py", "content_hash": "451d251b50c3f18e6dbf7369ff8b2894"}, "utils\\command_parser.py": {"size": 11840, "path": "utils\\command_parser.py", "abs_path": "D:\\Sandeep\\warpai\\utils\\command_parser.py", "content_hash": "f5df7fbc97c942703b782df0a7597754"}, "utils\\context_manager.py": {"size": 3514, "path": "utils\\context_manager.py", "abs_path": "D:\\Sandeep\\warpai\\utils\\context_manager.py", "content_hash": "91423b02894388ea28c63f93fc848083"}, "utils\\file_operations.py": {"size": 14741, "path": "utils\\file_operations.py", "abs_path": "D:\\Sandeep\\warpai\\utils\\file_operations.py", "content_hash": "44be7031a6d07908438825d2f44566c8"}, "utils\\__init__.py": {"size": 37, "path": "utils\\__init__.py", "abs_path": "D:\\Sandeep\\warpai\\utils\\__init__.py", "content_hash": "9a1eb111af4d4b80c605280391a7081a"}}