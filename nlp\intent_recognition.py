"""
Intent recognition module for WarpAI

This module provides advanced intent recognition capabilities for WarpAI,
allowing it to understand the user's intent from natural language input.
"""
import re
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

class IntentRecognizer:
    """Intent recognition for WarpAI"""
    
    def __init__(self):
        """Initialize the intent recognizer"""
        # Define intent patterns
        self.intent_patterns = {
            "run_file": [
                r"run (?:the )?(?:file )?(?:called )?[\"']?([^\"']+\.\w+)[\"']?",
                r"execute (?:the )?(?:file )?(?:called )?[\"']?([^\"']+\.\w+)[\"']?",
                r"start (?:the )?(?:file )?(?:called )?[\"']?([^\"']+\.\w+)[\"']?",
                r"launch (?:the )?(?:file )?(?:called )?[\"']?([^\"']+\.\w+)[\"']?",
            ],
            "install_package": [
                r"install (?:the )?(?:package )?(?:called )?[\"']?(\w+)[\"']?",
                r"download (?:the )?(?:package )?(?:called )?[\"']?(\w+)[\"']?",
                r"add (?:the )?(?:package|dependency) (?:called )?[\"']?(\w+)[\"']?",
            ],
            "create_file": [
                r"create (?:a )?(?:new )?file (?:called|named) [\"']?([^\"']+)[\"']?",
                r"make (?:a )?(?:new )?file (?:called|named) [\"']?([^\"']+)[\"']?",
                r"generate (?:a )?(?:new )?file (?:called|named) [\"']?([^\"']+)[\"']?",
            ],
            "edit_file": [
                r"edit (?:the )?file (?:called|named) [\"']?([^\"']+)[\"']?",
                r"modify (?:the )?file (?:called|named) [\"']?([^\"']+)[\"']?",
                r"change (?:the )?file (?:called|named) [\"']?([^\"']+)[\"']?",
                r"update (?:the )?file (?:called|named) [\"']?([^\"']+)[\"']?",
            ],
            "explain_code": [
                r"explain (?:this|the) code",
                r"what does (?:this|the) code do",
                r"how does (?:this|the) code work",
                r"analyze (?:this|the) code",
            ],
            "fix_error": [
                r"fix (?:this|the) (?:error|bug|issue|problem)",
                r"solve (?:this|the) (?:error|bug|issue|problem)",
                r"debug (?:this|the) (?:code|error|bug|issue|problem)",
                r"what's wrong with (?:this|the) code",
            ],
            "generate_code": [
                r"(?:write|create|generate|implement|code) (?:a|an) (\w+)",
                r"(?:write|create|generate|implement|code) (?:a|an) (\w+) (?:function|class|method|module)",
                r"(?:write|create|generate|implement|code) (?:a|an) (\w+) (?:in|using) (\w+)",
            ],
            "search_web": [
                r"search (?:for|about) (.+)",
                r"find information (?:about|on) (.+)",
                r"look up (.+)",
                r"google (.+)",
            ],
            "git_operation": [
                r"(?:git) (commit|push|pull|clone|checkout|branch|merge|status|log|add|reset)",
                r"(?:commit|push|pull|clone|checkout|branch|merge) (?:the|my) (?:changes|code|repository|repo)",
            ],
            "help_request": [
                r"help (?:me )?(?:with|on|about) (.+)",
                r"how (?:do|can) I (.+)",
                r"show me how to (.+)",
            ],
        }
        
        # Define intent keywords for fallback detection
        self.intent_keywords = {
            "run_file": ["run", "execute", "start", "launch"],
            "install_package": ["install", "download", "add", "dependency", "package"],
            "create_file": ["create", "make", "new", "file", "generate"],
            "edit_file": ["edit", "modify", "change", "update", "file"],
            "explain_code": ["explain", "what does", "how does", "analyze", "understand"],
            "fix_error": ["fix", "solve", "debug", "error", "bug", "issue", "problem"],
            "generate_code": ["write", "create", "generate", "implement", "code", "function", "class", "method"],
            "search_web": ["search", "find", "information", "look up", "google"],
            "git_operation": ["git", "commit", "push", "pull", "clone", "checkout", "branch", "merge"],
            "help_request": ["help", "how do I", "how can I", "show me how to"],
        }
    
    def recognize_intent(self, text: str) -> Dict[str, Any]:
        """
        Recognize the intent from the text
        
        Args:
            text: The text to recognize the intent from
            
        Returns:
            A dictionary containing the intent and any extracted entities
        """
        # Clean the input text
        text = text.strip()
        
        # Check for pattern matches first (more accurate)
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    # Extract entities from the match
                    entities = {}
                    if match.groups():
                        if intent == "run_file":
                            entities["file_path"] = match.group(1)
                        elif intent == "install_package":
                            entities["package_name"] = match.group(1)
                        elif intent == "create_file" or intent == "edit_file":
                            entities["file_path"] = match.group(1)
                        elif intent == "generate_code":
                            entities["code_type"] = match.group(1)
                            if len(match.groups()) > 1:
                                entities["language"] = match.group(2)
                        elif intent == "search_web":
                            entities["query"] = match.group(1)
                        elif intent == "git_operation":
                            entities["operation"] = match.group(1)
                        elif intent == "help_request":
                            entities["topic"] = match.group(1)
                    
                    return {
                        "intent": intent,
                        "confidence": 0.9,  # High confidence for pattern matches
                        "entities": entities
                    }
        
        # Fallback to keyword matching (less accurate)
        intent_scores = {}
        words = text.lower().split()
        
        for intent, keywords in self.intent_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in text.lower():
                    score += 1
            
            if score > 0:
                intent_scores[intent] = score / len(keywords)
        
        if intent_scores:
            # Get the intent with the highest score
            best_intent = max(intent_scores.items(), key=lambda x: x[1])
            return {
                "intent": best_intent[0],
                "confidence": best_intent[1] * 0.7,  # Lower confidence for keyword matches
                "entities": {}  # No entities extracted from keyword matching
            }
        
        # Default to unknown intent
        return {
            "intent": "unknown",
            "confidence": 0.0,
            "entities": {}
        }
