"""
Gemini AI client for WarpAI
"""
import os
from typing import Dict, List, Optional, Any, Union
import google.generativeai as genai
from google.generativeai.types import GenerationConfig

from warpai.config.settings import settings

class GeminiClient:
    """Client for interacting with Gemini AI API"""
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """Initialize the Gemini client"""
        self.api_key = api_key or settings.gemini_api_key
        self.model_name = model or settings.default_model
        self.history = []
        
        if not self.api_key:
            raise ValueError("Gemini API key is required. Set it in the .env file or pass it as an argument.")
        
        # Configure the Gemini API
        genai.configure(api_key=self.api_key)
        
        # Get the model
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=GenerationConfig(
                temperature=settings.temperature,
                max_output_tokens=settings.max_tokens,
            )
        )
        
        # Initialize the chat session
        self.chat = self.model.start_chat(history=[])
    
    def generate_response(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate a response from Gemini AI"""
        try:
            if system_prompt:
                response = self.model.generate_content(
                    [system_prompt, prompt],
                )
            else:
                response = self.chat.send_message(prompt)
            
            return response.text
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def generate_code(self, task: str, language: str) -> str:
        """Generate code for a specific task and language"""
        from warpai.config.prompts import CODE_GENERATION_PROMPT
        
        prompt = CODE_GENERATION_PROMPT.format(
            task=task,
            language=language
        )
        
        return self.generate_response(prompt)
    
    def analyze_error(self, error: str) -> str:
        """Analyze an error and suggest a solution"""
        from warpai.config.prompts import ERROR_ANALYSIS_PROMPT
        
        prompt = ERROR_ANALYSIS_PROMPT.format(
            error=error
        )
        
        return self.generate_response(prompt)
    
    def analyze_command_output(self, command: str, output: str) -> str:
        """Analyze the output of a command and provide insights"""
        from warpai.config.prompts import COMMAND_EXECUTION_PROMPT
        
        prompt = COMMAND_EXECUTION_PROMPT.format(
            command=command,
            output=output
        )
        
        return self.generate_response(prompt)
    
    def reset_chat(self) -> None:
        """Reset the chat history"""
        self.chat = self.model.start_chat(history=[])
        self.history = []
    
    def add_to_history(self, role: str, content: str) -> None:
        """Add a message to the chat history"""
        self.history.append({"role": role, "content": content})
        
        # Trim history if it exceeds the maximum length
        if len(self.history) > settings.max_conversation_history * 2:  # *2 because each exchange has user and assistant messages
            self.history = self.history[-settings.max_conversation_history * 2:]
