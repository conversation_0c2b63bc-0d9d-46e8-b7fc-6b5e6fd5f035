"""
Error fixer agent for WarpAI
"""
from typing import Dict, <PERSON>, Optional, Tuple, Union, Any
import json
import re

from warpai.agents.base_agent import BaseAgent

class ErrorFixerAgent(BaseAgent):
    """Agent specialized in fixing code errors"""
    
    def __init__(self, gemini_client=None, tool_registry=None):
        """Initialize the error fixer agent"""
        super().__init__(name="ErrorFixer", gemini_client=gemini_client, tool_registry=tool_registry)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the agent"""
        return """
You are <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, an AI assistant specialized in diagnosing and fixing code errors.
Your goal is to identify the root cause of errors and provide working solutions.

Follow these guidelines:
1. Carefully analyze error messages and stack traces
2. Identify the root cause of the error
3. Provide a clear explanation of what's wrong
4. Suggest specific fixes with code examples
5. Explain why your solution works
"""
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and fix errors"""
        error_message = input_data.get("error_message", "")
        code = input_data.get("code", "")
        language = input_data.get("language", "python")
        context = input_data.get("context", "")
        
        # Generate error fix
        prompt = f"""
Error message:
{error_message}

Code with error:
```{language}
{code}
```

{f'Context: {context}' if context else ''}

Please analyze this error and provide a fix. Include:
1. What's causing the error
2. How to fix it
3. The corrected code
"""
        
        # Use the debugCode tool
        tool_result = self.call_tool(
            "debugCode",
            code=code,
            error_message=error_message,
            language=language
        )

        if tool_result.get("success"):
            debug_result = tool_result.get("debug_result", "")
            # Perform self-critique on the debug result
            critique = self.self_critique(debug_result, [
                "Error diagnosis accuracy",
                "Solution effectiveness",
                "Explanation clarity",
                "Code correctness"
            ])
            
            # If the score is below 7, try to improve the response
            if critique.get("overall_score", 10) < 7:
                improved_response = self.improve_response(debug_result, critique)
                
                self.add_to_memory({
                    "error_message": error_message,
                    "code": code,
                    "language": language,
                    "original_debug_result": debug_result,
                    "critique": critique,
                    "improved_debug_result": improved_response
                })
                
                return {
                    "explanation": improved_response,
                    "fixed_code": self._extract_code_from_debug_result(improved_response, language),
                    "critique": critique,
                    "improved": True
                }
            else:
                self.add_to_memory({
                    "error_message": error_message,
                    "code": code,
                    "language": language,
                    "debug_result": debug_result,
                    "critique": critique
                })
                
                return {
                    "explanation": debug_result,
                    "fixed_code": self._extract_code_from_debug_result(debug_result, language),
                    "critique": critique,
                    "improved": False
                }
        else:
            return {
                "explanation": "",
                "fixed_code": "",
                "error": tool_result.get("error", "Failed to debug code using tool."),
                "improved": False
            }
    
    def _extract_code_from_debug_result(self, text: str, language: str) -> str:
        """Extract fixed code from the debug result, assuming it's in a code block"""
        # Try to find code blocks with the specified language
        pattern = f"```{language}\\n([\\s\\S]+?)\\n```"
        matches = re.findall(pattern, text)
        
        if matches:
            return matches[0].strip()
        
        # Try to find any code blocks if language-specific fails
        pattern = r"```(\w+)?\n([\s\S]+?)\n```"
        matches = re.findall(pattern, text)
        
        if matches:
            return matches[0][1].strip() # Return the content of the first code block
        
        return ""
