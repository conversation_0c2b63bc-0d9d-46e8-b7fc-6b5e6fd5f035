"""
Semantic understanding module for WarpAI

This module provides advanced semantic understanding capabilities for WarpAI,
allowing it to understand the meaning and context of natural language input.
"""
import re
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

class SemanticAnalyzer:
    """Semantic analysis for WarpAI"""
    
    def __init__(self):
        """Initialize the semantic analyzer"""
        # Define semantic categories
        self.categories = {
            "code_generation": [
                "write", "create", "generate", "implement", "code", "function", 
                "class", "method", "script", "program", "develop", "build"
            ],
            "code_explanation": [
                "explain", "understand", "analyze", "what does", "how does", 
                "describe", "clarify", "breakdown", "detail", "interpret"
            ],
            "error_handling": [
                "error", "bug", "issue", "problem", "fix", "solve", "debug", 
                "exception", "crash", "failure", "not working", "fails"
            ],
            "file_operations": [
                "file", "create", "edit", "modify", "update", "delete", "remove", 
                "rename", "copy", "move", "read", "write", "save"
            ],
            "package_management": [
                "install", "uninstall", "update", "upgrade", "package", "dependency", 
                "library", "module", "pip", "npm", "yarn", "gem", "nuget"
            ],
            "git_operations": [
                "git", "commit", "push", "pull", "clone", "checkout", "branch", 
                "merge", "status", "log", "add", "reset", "repository", "repo"
            ],
            "web_search": [
                "search", "find", "look up", "google", "information", "web", 
                "internet", "online", "research", "query"
            ],
            "project_management": [
                "project", "structure", "organize", "setup", "configure", "init", 
                "initialize", "start", "new", "create"
            ],
            "help_request": [
                "help", "assist", "guide", "show me", "teach me", "how to", 
                "how do I", "can you", "please", "need help"
            ],
        }
        
        # Define sentiment indicators
        self.sentiment_indicators = {
            "positive": [
                "good", "great", "excellent", "awesome", "nice", "perfect", 
                "thanks", "thank you", "appreciate", "helpful", "useful"
            ],
            "negative": [
                "bad", "terrible", "awful", "horrible", "poor", "wrong", 
                "not working", "doesn't work", "failed", "error", "issue", "problem"
            ],
            "urgent": [
                "urgent", "immediately", "asap", "quickly", "hurry", "fast", 
                "emergency", "critical", "important", "now", "right away"
            ],
            "confused": [
                "confused", "don't understand", "unclear", "what", "why", 
                "how", "not sure", "confusing", "complicated", "complex"
            ],
        }
        
        # Define complexity indicators
        self.complexity_indicators = {
            "simple": [
                "simple", "basic", "easy", "straightforward", "quick", 
                "just", "only", "small", "minimal", "trivial"
            ],
            "complex": [
                "complex", "complicated", "advanced", "sophisticated", "detailed", 
                "comprehensive", "complete", "full", "extensive", "thorough"
            ],
        }
    
    def analyze_semantics(self, text: str) -> Dict[str, Any]:
        """
        Analyze the semantics of the text
        
        Args:
            text: The text to analyze
            
        Returns:
            A dictionary containing the semantic analysis
        """
        # Clean the input text
        text = text.strip().lower()
        
        # Initialize results
        results = {
            "categories": {},
            "sentiment": {},
            "complexity": {},
            "context": {},
        }
        
        # Analyze categories
        for category, keywords in self.categories.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                if keyword.lower() in text:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                results["categories"][category] = {
                    "score": score / len(keywords),
                    "matched_keywords": matched_keywords
                }
        
        # Analyze sentiment
        for sentiment, keywords in self.sentiment_indicators.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                if keyword.lower() in text:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                results["sentiment"][sentiment] = {
                    "score": score / len(keywords),
                    "matched_keywords": matched_keywords
                }
        
        # Analyze complexity
        for complexity, keywords in self.complexity_indicators.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                if keyword.lower() in text:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                results["complexity"][complexity] = {
                    "score": score / len(keywords),
                    "matched_keywords": matched_keywords
                }
        
        # Analyze context
        results["context"] = self._analyze_context(text)
        
        return results
    
    def _analyze_context(self, text: str) -> Dict[str, Any]:
        """
        Analyze the context of the text
        
        Args:
            text: The text to analyze
            
        Returns:
            A dictionary containing the context analysis
        """
        context = {}
        
        # Check if the text is a question
        context["is_question"] = text.endswith("?") or any(q in text for q in ["what", "how", "why", "when", "where", "who", "which"])
        
        # Check if the text is a command
        context["is_command"] = any(c in text for c in ["run", "execute", "start", "create", "make", "install", "download", "edit", "modify"])
        
        # Check if the text is a request
        context["is_request"] = any(r in text for r in ["please", "can you", "could you", "would you", "help me", "assist me"])
        
        # Check if the text is a statement
        context["is_statement"] = not (context["is_question"] or context["is_command"] or context["is_request"])
        
        # Check if the text is referring to previous context
        context["refers_to_previous"] = any(p in text for p in ["this", "that", "it", "these", "those", "the above", "previous"])
        
        # Check if the text is referring to code
        context["refers_to_code"] = any(c in text for c in ["code", "function", "class", "method", "variable", "object", "array", "list", "dictionary"])
        
        # Check if the text is referring to files
        context["refers_to_files"] = any(f in text for f in ["file", "directory", "folder", "path"])
        
        return context
