"""
Intelligent Tool Selector for WarpAI

This module provides intelligent tool selection based on user input analysis.
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from warpai.intelligent_tools.tool_registry import ToolRegistry, ToolDefinition, ToolCategory
from warpai.gemini_client import Gemini<PERSON>lient
from warpai.config.settings import settings

logger = logging.getLogger(__name__)

@dataclass
class ToolSelection:
    """Represents a selected tool with its parameters and confidence"""
    tool: ToolDefinition
    parameters: Dict[str, Any]
    confidence: float
    reasoning: str

class IntelligentToolSelector:
    """Intelligent tool selector that analyzes user input and selects appropriate tools"""

    def __init__(self, api_key: str = None, model: str = None, tool_registry: Optional[ToolRegistry] = None):
        """Initialize the intelligent tool selector"""
        self.tool_registry = tool_registry if tool_registry else ToolRegistry(api_key=api_key, model=model)
        self.gemini_client = GeminiClient(api_key=api_key, model=model or settings.model)

        # Intent patterns for quick tool selection
        self.intent_patterns = {
            "file_read": [
                r"(?:read|show|display|view|open|cat)\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?",
                r"what(?:'s|\s+is)\s+in\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?",
                r"contents?\s+of\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?"
            ],
            "file_write": [
                r"(?:write|save|create)\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?",
                r"(?:put|store)\s+.*\s+(?:in|to)\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?"
            ],
            "file_edit": [
                r"(?:edit|modify|change|update)\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?",
                r"(?:fix|correct)\s+.*\s+in\s+(?:file\s+)?['\"]?([^'\"]+)['\"]?"
            ],
            "shell_command": [
                r"(?:run|execute|exec)\s+['\"]?([^'\"]+)['\"]?",
                r"(?:command|cmd):\s*['\"]?([^'\"]+)['\"]?",
                r"terminal:\s*['\"]?([^'\"]+)['\"]?"
            ],
            "code_generate": [
                r"(?:create|generate|write|build|implement)\s+(?:a\s+)?(?:function|class|script|program|code)",
                r"(?:make|develop)\s+(?:a\s+)?(?:.*)\s+(?:in\s+)?(\w+)?",
                r"code\s+(?:for|to)\s+(.*)"
            ],
            "package_install": [
                r"(?:install|add)\s+(?:package\s+)?['\"]?([^'\"]+)['\"]?",
                r"(?:pip|npm|yarn)\s+install\s+['\"]?([^'\"]+)['\"]?"
            ],
            "web_fetch": [
                r"(?:fetch|get|download)\s+(?:from\s+)?(https?://[^\s]+)",
                r"(?:web|url):\s*(https?://[^\s]+)"
            ]
        }

        logger.info("Intelligent tool selector initialized")

    def select_tools(self, user_input: str, context: Dict[str, Any] = None) -> List[ToolSelection]:
        """
        Select appropriate tools based on user input

        Args:
            user_input: The user's input text
            context: Additional context information

        Returns:
            List of selected tools with parameters and confidence scores
        """
        if context is None:
            context = {}

        # First, try pattern-based selection for common operations
        pattern_selections = self._pattern_based_selection(user_input)
        if pattern_selections:
            logger.debug(f"Pattern-based selection found {len(pattern_selections)} tools")
            return pattern_selections

        # If no patterns match, use AI-based selection
        ai_selections = self._ai_based_selection(user_input, context)
        logger.debug(f"AI-based selection found {len(ai_selections)} tools")

        return ai_selections

    def _pattern_based_selection(self, user_input: str) -> List[ToolSelection]:
        """Select tools based on predefined patterns"""
        selections = []

        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    tool_selection = self._create_tool_selection_from_intent(intent, match, user_input)
                    if tool_selection:
                        selections.append(tool_selection)
                        break

        return selections

    def _create_tool_selection_from_intent(self, intent: str, match: re.Match, user_input: str) -> Optional[ToolSelection]:
        """Create tool selection from recognized intent"""

        if intent == "file_read":
            tool = self.tool_registry.get_tool("readFile")
            if tool and match.groups():
                return ToolSelection(
                    tool=tool,
                    parameters={"file_path": match.group(1).strip()},
                    confidence=0.9,
                    reasoning=f"Pattern match for file reading: {match.group(1)}"
                )

        elif intent == "file_write":
            tool = self.tool_registry.get_tool("saveFile")
            if tool and match.groups():
                # Extract content from user input (simplified)
                content = self._extract_content_for_file(user_input, match.group(1))
                return ToolSelection(
                    tool=tool,
                    parameters={"file_path": match.group(1).strip(), "content": content},
                    confidence=0.85,
                    reasoning=f"Pattern match for file writing: {match.group(1)}"
                )

        elif intent == "file_edit":
            tool = self.tool_registry.get_tool("editFile")
            if tool and match.groups():
                return ToolSelection(
                    tool=tool,
                    parameters={"file_path": match.group(1).strip(), "changes": {}},
                    confidence=0.8,
                    reasoning=f"Pattern match for file editing: {match.group(1)}"
                )

        elif intent == "shell_command":
            tool = self.tool_registry.get_tool("shell")
            if tool and match.groups():
                return ToolSelection(
                    tool=tool,
                    parameters={"command": match.group(1).strip()},
                    confidence=0.9,
                    reasoning=f"Pattern match for shell command: {match.group(1)}"
                )

        elif intent == "code_generate":
            tool = self.tool_registry.get_tool("generateCode")
            if tool:
                language = self._detect_language_from_input(user_input)
                return ToolSelection(
                    tool=tool,
                    parameters={"requirements": user_input, "language": language},
                    confidence=0.8,
                    reasoning="Pattern match for code generation"
                )

        elif intent == "package_install":
            tool = self.tool_registry.get_tool("installPackage")
            if tool and match.groups():
                return ToolSelection(
                    tool=tool,
                    parameters={"package_name": match.group(1).strip()},
                    confidence=0.9,
                    reasoning=f"Pattern match for package installation: {match.group(1)}"
                )

        elif intent == "web_fetch":
            tool = self.tool_registry.get_tool("webFetch")
            if tool and match.groups():
                return ToolSelection(
                    tool=tool,
                    parameters={"url": match.group(1).strip()},
                    confidence=0.9,
                    reasoning=f"Pattern match for web fetch: {match.group(1)}"
                )

        return None

    def _ai_based_selection(self, user_input: str, context: Dict[str, Any]) -> List[ToolSelection]:
        """Use AI to select appropriate tools"""

        # Get available tools information
        tools_info = self._get_tools_info_for_ai()

        # Create prompt for AI tool selection
        prompt = f"""
        You are an intelligent tool selector for a coding assistant. Analyze the user input and select the most appropriate tools to accomplish the task.

        User Input: {user_input}

        Context: {json.dumps(context, indent=2)}

        Available Tools:
        {tools_info}

        Instructions:
        1. Analyze the user's intent and requirements
        2. Select 1-3 most appropriate tools that can accomplish the task
        3. For each selected tool, provide the parameters needed
        4. Assign a confidence score (0.0-1.0) for each selection
        5. Provide reasoning for each selection

        Return your response as a JSON array of tool selections:
        [
            {{
                "tool_name": "toolName",
                "parameters": {{"param1": "value1", "param2": "value2"}},
                "confidence": 0.85,
                "reasoning": "Why this tool was selected"
            }}
        ]

        Only return the JSON array, no other text.
        """

        try:
            response = self.gemini_client.generate_response(prompt)

            # Extract JSON from response
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                selections_data = json.loads(json_match.group(0))
                return self._convert_ai_response_to_selections(selections_data)

        except Exception as e:
            logger.error(f"Error in AI-based tool selection: {e}")

        # Fallback to keyword-based selection
        return self._keyword_based_selection(user_input)

    def _keyword_based_selection(self, user_input: str) -> List[ToolSelection]:
        """Fallback keyword-based tool selection"""
        words = user_input.lower().split()
        matching_tools = self.tool_registry.search_tools_by_keywords(words)

        selections = []
        for tool in matching_tools[:3]:  # Limit to top 3 matches
            selections.append(ToolSelection(
                tool=tool,
                parameters={},
                confidence=0.6,
                reasoning=f"Keyword match for tool: {tool.name}"
            ))

        return selections

    def _convert_ai_response_to_selections(self, selections_data: List[Dict]) -> List[ToolSelection]:
        """Convert AI response to ToolSelection objects"""
        selections = []

        for selection_data in selections_data:
            tool_name = selection_data.get("tool_name")
            tool = self.tool_registry.get_tool(tool_name)

            if tool:
                selections.append(ToolSelection(
                    tool=tool,
                    parameters=selection_data.get("parameters", {}),
                    confidence=selection_data.get("confidence", 0.5),
                    reasoning=selection_data.get("reasoning", "AI selection")
                ))

        return selections

    def _get_tools_info_for_ai(self) -> str:
        """Get formatted tools information for AI prompt"""
        tools_info = []

        for tool in self.tool_registry.get_all_tools():
            tool_info = f"- {tool.name}: {tool.description}"
            if tool.parameters:
                params = ", ".join(f"{k}:{v.__name__}" for k, v in tool.parameters.items())
                tool_info += f" (Parameters: {params})"
            tools_info.append(tool_info)

        return "\n".join(tools_info)

    def _extract_content_for_file(self, user_input: str, file_path: str) -> str:
        """Extract content to be written to file from user input"""
        # Simple extraction - look for content after "write" or "save"
        patterns = [
            r"(?:write|save|put)\s+['\"]([^'\"]*)['\"]",
            r"content:\s*['\"]([^'\"]*)['\"]",
            r"text:\s*['\"]([^'\"]*)['\"]"
        ]

        for pattern in patterns:
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                return match.group(1)

        return ""  # Will be filled by AI if needed

    def _detect_language_from_input(self, user_input: str) -> str:
        """Detect programming language from user input"""
        language_keywords = {
            "python": ["python", "py", "django", "flask", "pandas", "numpy"],
            "javascript": ["javascript", "js", "node", "react", "vue", "angular"],
            "java": ["java", "spring", "maven", "gradle"],
            "csharp": ["c#", "csharp", "dotnet", ".net"],
            "cpp": ["c++", "cpp", "cmake"],
            "go": ["go", "golang"],
            "rust": ["rust", "cargo"],
            "php": ["php", "laravel", "symfony"]
        }

        user_input_lower = user_input.lower()
        for language, keywords in language_keywords.items():
            if any(keyword in user_input_lower for keyword in keywords):
                return language

        return "python"  # Default to Python
