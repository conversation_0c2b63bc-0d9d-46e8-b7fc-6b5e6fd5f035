"""
Agent Mode for WarpAI

This module provides Agent Mode functionality, allowing users to interact with
WarpAI using natural language and execute commands automatically.
"""

from warpai.agent_mode.agent import AgentMode
from warpai.agent_mode.dispatch import DispatchMode
from warpai.agent_mode.conversation import Conversation
from warpai.agent_mode.detector import NaturalLanguageDetector
from warpai.agent_mode.autonomy import AutonomyManager

__all__ = [
    "AgentMode",
    "DispatchMode",
    "Conversation",
    "NaturalLanguageDetector",
    "AutonomyManager",
]
