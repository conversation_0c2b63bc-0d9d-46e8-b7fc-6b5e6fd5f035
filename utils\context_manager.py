"""
Context management utilities for WarpAI
"""
import os
from typing import Dict, List, Optional, Any
import json
from pathlib import Path

from warpai.config.settings import settings

class ContextManager:
    """Utility for managing conversation context"""

    def __init__(self):
        """Initialize the context manager"""
        self.history = []
        self.max_history = settings.max_conversation_history
        self.history_file = settings.history_file

        # Load history from file if it exists
        self._load_history()

    def add_to_history(self, role: str, content: str) -> None:
        """Add a message to the conversation history"""
        self.history.append({
            "role": role,
            "content": content
        })

        # Trim history if it exceeds the maximum length
        if len(self.history) > self.max_history * 2:  # *2 because each exchange has user and assistant messages
            self.history = self.history[-self.max_history * 2:]

        # Save history to file
        self._save_history()

    def get_history(self) -> List[Dict[str, str]]:
        """Get the conversation history"""
        return self.history

    def clear_history(self) -> None:
        """Clear the conversation history"""
        self.history = []
        self._save_history()

    def _save_history(self) -> None:
        """Save the conversation history to a file"""
        try:
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)

            # Save the history to a file
            with open(self.history_file, "w") as f:
                json.dump(self.history, f)
        except Exception as e:
            print(f"Error saving history: {e}")

    def _load_history(self) -> None:
        """Load the conversation history from a file"""
        try:
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, "r") as f:
                        self.history = json.load(f)

                        # Ensure history doesn't exceed the maximum length
                        if len(self.history) > self.max_history * 2:
                            self.history = self.history[-self.max_history * 2:]
                except json.JSONDecodeError:
                    # If the history file is corrupted, backup and create a new one
                    if os.path.exists(self.history_file):
                        backup_file = f"{self.history_file}.bak"
                        try:
                            import shutil
                            shutil.copy2(self.history_file, backup_file)
                            print(f"Corrupted history file backed up to {backup_file}")
                        except Exception:
                            pass

                    # Reset history
                    self.history = []
                    self._save_history()
        except Exception as e:
            print(f"Error loading history: {e}")
            self.history = []

    def get_context_for_prompt(self) -> str:
        """Get the conversation history formatted for a prompt"""
        context = ""

        for message in self.history:
            role = message["role"]
            content = message["content"]

            if role == "user":
                context += f"User: {content}\n\n"
            elif role == "assistant":
                context += f"Assistant: {content}\n\n"

        return context
