"""
Autonomy management for Agent Mode

This module provides the AutonomyManager class for managing command execution
autonomy in Agent Mode.
"""
import re
import logging
from typing import List, Optional, Dict, Any

from warpai.config.settings import settings
from warpai.gemini_client import GeminiClient

# Set up logging
logger = logging.getLogger(__name__)

class AutonomyManager:
    """
    Autonomy Manager
    
    This class provides methods for managing command execution autonomy in
    Agent Mode, including command allowlists and denylists.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """
        Initialize the autonomy manager
        
        Args:
            api_key: API key for the LLM provider
            model: Model to use for autonomy decisions
        """
        # Use the model from settings if not provided
        if model is None:
            model = settings.model
            
        # Initialize client for model-based decisions
        self.model = model
        self.api_key = api_key
        self.gemini_client = GeminiClient(api_key=api_key, model=model)
        
        # Load allowlist and denylist from settings
        self.allowlist = settings.command_allowlist
        self.denylist = settings.command_denylist
        
        # Set autonomy level
        self.autonomy_enabled = settings.autonomy_enabled
        self.model_based_autonomy = settings.autonomy_model_based
        self.read_permissions = settings.autonomy_read_permissions
        
        logger.info("Autonomy manager initialized")
    
    def is_command_allowed(self, command: str) -> bool:
        """
        Check if a command is allowed to be executed
        
        Args:
            command: Command to check
            
        Returns:
            bool: True if command is allowed, False otherwise
        """
        # Skip empty commands
        if not command or not command.strip():
            return False
        
        # Check if autonomy is enabled
        if not self.autonomy_enabled:
            return False
        
        # Check if command is in denylist
        for pattern in self.denylist:
            if re.match(pattern, command.strip()):
                logger.warning(f"Command denied (denylist): {command}")
                return False
        
        # Check if command is in allowlist
        for pattern in self.allowlist:
            if re.match(pattern, command.strip()):
                logger.info(f"Command allowed (allowlist): {command}")
                return True
        
        # If model-based autonomy is enabled, use the model to decide
        if self.model_based_autonomy:
            return self._model_based_decision(command)
        
        # Default to deny
        logger.warning(f"Command denied (default): {command}")
        return False
    
    def _model_based_decision(self, command: str) -> bool:
        """
        Use the model to decide if a command is safe to execute
        
        Args:
            command: Command to check
            
        Returns:
            bool: True if command is safe, False otherwise
        """
        # Skip if no model is available
        if not self.gemini_client:
            return False
        
        # Prepare the prompt
        prompt = f"""
        You are a security expert evaluating whether a shell command is safe to execute.
        
        Command: {command}
        
        Evaluate if this command is safe to execute automatically without user confirmation.
        Safe commands are those that:
        1. Do not modify the system in dangerous ways
        2. Do not delete or overwrite important files
        3. Do not access sensitive information
        4. Do not install software from untrusted sources
        5. Do not make network connections to untrusted hosts
        6. Do not execute arbitrary code
        
        Respond with ONLY "SAFE" or "UNSAFE".
        """
        
        # Generate response
        response = self.gemini_client.generate_response(
            prompt=prompt,
            max_tokens=10,
            temperature=0.0,
        )
        
        # Check if response indicates command is safe
        is_safe = "SAFE" in response.upper() and "UNSAFE" not in response.upper()
        
        if is_safe:
            logger.info(f"Command allowed (model): {command}")
        else:
            logger.warning(f"Command denied (model): {command}")
        
        return is_safe
    
    def add_to_allowlist(self, pattern: str) -> None:
        """
        Add a pattern to the allowlist
        
        Args:
            pattern: Pattern to add to allowlist
        """
        if pattern not in self.allowlist:
            self.allowlist.append(pattern)
            logger.debug(f"Added pattern to allowlist: {pattern}")
    
    def remove_from_allowlist(self, pattern: str) -> None:
        """
        Remove a pattern from the allowlist
        
        Args:
            pattern: Pattern to remove from allowlist
        """
        if pattern in self.allowlist:
            self.allowlist.remove(pattern)
            logger.debug(f"Removed pattern from allowlist: {pattern}")
    
    def add_to_denylist(self, pattern: str) -> None:
        """
        Add a pattern to the denylist
        
        Args:
            pattern: Pattern to add to denylist
        """
        if pattern not in self.denylist:
            self.denylist.append(pattern)
            logger.debug(f"Added pattern to denylist: {pattern}")
    
    def remove_from_denylist(self, pattern: str) -> None:
        """
        Remove a pattern from the denylist
        
        Args:
            pattern: Pattern to remove from denylist
        """
        if pattern in self.denylist:
            self.denylist.remove(pattern)
            logger.debug(f"Removed pattern from denylist: {pattern}")
    
    def get_allowlist(self) -> List[str]:
        """
        Get the current allowlist
        
        Returns:
            List[str]: Current allowlist
        """
        return self.allowlist
    
    def get_denylist(self) -> List[str]:
        """
        Get the current denylist
        
        Returns:
            List[str]: Current denylist
        """
        return self.denylist
    
    def set_autonomy_enabled(self, enabled: bool) -> None:
        """
        Set whether autonomy is enabled
        
        Args:
            enabled: Whether autonomy is enabled
        """
        self.autonomy_enabled = enabled
        logger.debug(f"Autonomy enabled: {enabled}")
    
    def set_model_based_autonomy(self, enabled: bool) -> None:
        """
        Set whether model-based autonomy is enabled
        
        Args:
            enabled: Whether model-based autonomy is enabled
        """
        self.model_based_autonomy = enabled
        logger.debug(f"Model-based autonomy enabled: {enabled}")
    
    def set_read_permissions(self, enabled: bool) -> None:
        """
        Set whether read permissions are enabled
        
        Args:
            enabled: Whether read permissions are enabled
        """
        self.read_permissions = enabled
        logger.debug(f"Read permissions enabled: {enabled}")
