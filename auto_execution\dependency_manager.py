"""
Dependency manager module for WarpAI

This module provides dependency management capabilities for WarpAI,
allowing it to automatically detect and install dependencies for code files.
"""
import os
import re
import subprocess
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

class DependencyManager:
    """Dependency management for WarpAI"""
    
    def __init__(self):
        """Initialize the dependency manager"""
        # Define package managers for each language
        self.package_managers = {
            "python": {
                "command": "pip install {package}",
                "check_command": "pip show {package}",
                "import_pattern": r"(?:^|\n)(?:import|from)\s+([a-zA-Z0-9_\.]+)",
                "requirements_file": "requirements.txt",
                "install_requirements": "pip install -r requirements.txt",
            },
            "javascript": {
                "command": "npm install {package}",
                "check_command": "npm list {package}",
                "import_pattern": r"(?:^|\n)(?:import|require)\s*\(?['\"]([^'\"]+)['\"]",
                "requirements_file": "package.json",
                "install_requirements": "npm install",
            },
            "typescript": {
                "command": "npm install {package} @types/{package}",
                "check_command": "npm list {package}",
                "import_pattern": r"(?:^|\n)(?:import|require)\s*\(?['\"]([^'\"]+)['\"]",
                "requirements_file": "package.json",
                "install_requirements": "npm install",
            },
            "ruby": {
                "command": "gem install {package}",
                "check_command": "gem list {package}",
                "import_pattern": r"(?:^|\n)require\s+['\"]([^'\"]+)['\"]",
                "requirements_file": "Gemfile",
                "install_requirements": "bundle install",
            },
            "php": {
                "command": "composer require {package}",
                "check_command": "composer show {package}",
                "import_pattern": r"(?:^|\n)use\s+([a-zA-Z0-9_\\]+)",
                "requirements_file": "composer.json",
                "install_requirements": "composer install",
            },
            "rust": {
                "command": "cargo add {package}",
                "check_command": "cargo tree | grep {package}",
                "import_pattern": r"(?:^|\n)use\s+([a-zA-Z0-9_:]+)",
                "requirements_file": "Cargo.toml",
                "install_requirements": "cargo build",
            },
            "go": {
                "command": "go get {package}",
                "check_command": "go list -m {package}",
                "import_pattern": r"(?:^|\n)import\s+['\"]([^'\"]+)['\"]",
                "requirements_file": "go.mod",
                "install_requirements": "go mod download",
            },
            "java": {
                "command": "mvn install",  # Assumes Maven
                "check_command": "mvn dependency:list | grep {package}",
                "import_pattern": r"(?:^|\n)import\s+([a-zA-Z0-9_\.]+)",
                "requirements_file": "pom.xml",
                "install_requirements": "mvn install",
            },
            "c#": {
                "command": "dotnet add package {package}",
                "check_command": "dotnet list package | grep {package}",
                "import_pattern": r"(?:^|\n)using\s+([a-zA-Z0-9_\.]+)",
                "requirements_file": "*.csproj",
                "install_requirements": "dotnet restore",
            },
        }
        
        # Define common packages for each language
        self.common_packages = {
            "python": {
                "numpy": ["numpy", "np"],
                "pandas": ["pandas", "pd"],
                "matplotlib": ["matplotlib", "plt"],
                "scikit-learn": ["sklearn"],
                "tensorflow": ["tensorflow", "tf"],
                "torch": ["torch"],
                "django": ["django"],
                "flask": ["flask"],
                "requests": ["requests"],
                "beautifulsoup4": ["bs4", "BeautifulSoup"],
                "selenium": ["selenium"],
                "pytest": ["pytest"],
                "sqlalchemy": ["sqlalchemy"],
                "pillow": ["PIL"],
            },
            "javascript": {
                "express": ["express"],
                "react": ["react"],
                "vue": ["vue"],
                "angular": ["@angular/core"],
                "axios": ["axios"],
                "lodash": ["lodash", "_"],
                "moment": ["moment"],
                "jquery": ["jquery", "$"],
                "typescript": ["typescript"],
                "webpack": ["webpack"],
                "babel": ["@babel/core"],
                "jest": ["jest"],
                "mocha": ["mocha"],
            },
            "ruby": {
                "rails": ["rails"],
                "sinatra": ["sinatra"],
                "rspec": ["rspec"],
                "nokogiri": ["nokogiri"],
                "httparty": ["httparty"],
                "devise": ["devise"],
                "activerecord": ["active_record"],
            },
        }
        
        # Define standard library packages (don't need to be installed)
        self.standard_libraries = {
            "python": [
                "os", "sys", "re", "math", "random", "datetime", "time", "json",
                "csv", "collections", "itertools", "functools", "typing",
                "pathlib", "argparse", "logging", "unittest", "multiprocessing",
                "threading", "subprocess", "shutil", "glob", "tempfile",
            ],
            "javascript": [
                "fs", "path", "os", "http", "https", "url", "querystring",
                "crypto", "zlib", "util", "stream", "events", "buffer",
                "child_process", "cluster", "dgram", "dns", "net", "readline",
            ],
            "ruby": [
                "date", "time", "json", "csv", "fileutils", "pathname",
                "stringio", "tempfile", "uri", "net/http", "open-uri",
                "securerandom", "base64", "digest", "logger", "optparse",
                "ostruct", "singleton", "thread", "thwait", "timeout",
            ],
        }
    
    def detect_dependencies(self, file_path: str, language: str) -> List[str]:
        """
        Detect dependencies from a file
        
        Args:
            file_path: The path to the file
            language: The programming language
            
        Returns:
            A list of detected dependencies
        """
        # Check if language is supported
        if language not in self.package_managers:
            logger.warning(f"Dependency detection not supported for {language}")
            return []
        
        # Check if file exists
        if not os.path.isfile(file_path):
            logger.error(f"File not found: {file_path}")
            return []
        
        try:
            # Read file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Get import pattern for the language
            import_pattern = self.package_managers[language]["import_pattern"]
            
            # Find all imports
            imports = re.findall(import_pattern, content)
            
            # Process imports
            dependencies = self._process_imports(imports, language)
            
            return dependencies
        except Exception as e:
            logger.error(f"Error detecting dependencies in {file_path}: {e}")
            return []
    
    def _process_imports(self, imports: List[str], language: str) -> List[str]:
        """
        Process imports to get package names
        
        Args:
            imports: List of import statements
            language: The programming language
            
        Returns:
            A list of package names
        """
        dependencies = []
        
        # Get standard libraries for the language
        standard_libs = self.standard_libraries.get(language, [])
        
        # Process each import
        for imp in imports:
            # Extract the base package name
            base_package = imp.split(".")[0].split("/")[0]
            
            # Skip standard libraries
            if base_package in standard_libs:
                continue
            
            # Check if it's a common package
            package_name = self._get_package_name(base_package, language)
            
            if package_name and package_name not in dependencies:
                dependencies.append(package_name)
        
        return dependencies
    
    def _get_package_name(self, import_name: str, language: str) -> Optional[str]:
        """
        Get the package name for an import
        
        Args:
            import_name: The import name
            language: The programming language
            
        Returns:
            The package name or None if not found
        """
        # Check if language has common packages defined
        if language not in self.common_packages:
            return import_name
        
        # Check if import is a known package
        for package, aliases in self.common_packages[language].items():
            if import_name in aliases:
                return package
        
        # Default to the import name
        return import_name
    
    def install_dependency(self, package: str, language: str) -> bool:
        """
        Install a dependency
        
        Args:
            package: The package to install
            language: The programming language
            
        Returns:
            True if installation was successful, False otherwise
        """
        # Check if language is supported
        if language not in self.package_managers:
            logger.warning(f"Dependency installation not supported for {language}")
            return False
        
        # Get installation command
        install_cmd = self.package_managers[language]["command"].format(package=package)
        
        try:
            # Run installation command
            logger.info(f"Installing {package} for {language}...")
            result = subprocess.run(install_cmd, shell=True, check=True, capture_output=True, text=True)
            
            logger.info(f"Successfully installed {package}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error installing {package}: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"Error installing {package}: {e}")
            return False
    
    def check_dependency_installed(self, package: str, language: str) -> bool:
        """
        Check if a dependency is installed
        
        Args:
            package: The package to check
            language: The programming language
            
        Returns:
            True if the package is installed, False otherwise
        """
        # Check if language is supported
        if language not in self.package_managers:
            logger.warning(f"Dependency checking not supported for {language}")
            return False
        
        # Get check command
        check_cmd = self.package_managers[language]["check_command"].format(package=package)
        
        try:
            # Run check command
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
            
            # Check if package is installed
            return result.returncode == 0
        except Exception as e:
            logger.error(f"Error checking if {package} is installed: {e}")
            return False
    
    def install_requirements(self, directory: str, language: str) -> bool:
        """
        Install requirements from a requirements file
        
        Args:
            directory: The directory containing the requirements file
            language: The programming language
            
        Returns:
            True if installation was successful, False otherwise
        """
        # Check if language is supported
        if language not in self.package_managers:
            logger.warning(f"Requirements installation not supported for {language}")
            return False
        
        # Get requirements file name
        req_file = self.package_managers[language]["requirements_file"]
        req_path = os.path.join(directory, req_file)
        
        # Check if requirements file exists
        if not os.path.isfile(req_path):
            logger.warning(f"Requirements file {req_file} not found in {directory}")
            return False
        
        # Get installation command
        install_cmd = self.package_managers[language]["install_requirements"]
        
        try:
            # Run installation command
            logger.info(f"Installing requirements from {req_file}...")
            result = subprocess.run(install_cmd, shell=True, cwd=directory, check=True, capture_output=True, text=True)
            
            logger.info(f"Successfully installed requirements from {req_file}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error installing requirements from {req_file}: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"Error installing requirements from {req_file}: {e}")
            return False
