"""
Keyboard shortcut help for WarpAI

This module provides a visual keyboard shortcut help system for WarpAI.
"""
import platform
from typing import Dict, List, Optional, Tuple

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

class KeyboardHelp:
    """Keyboard shortcut help for WarpAI"""

    def __init__(self):
        """Initialize the keyboard shortcut help"""
        self.console = Console()
        self.shortcuts = self._get_shortcuts()

    def show(self) -> None:
        """Show the keyboard shortcut help"""
        # Create a table for the shortcuts
        table = Table(title="Keyboard Shortcuts", show_header=True, header_style="bold cyan")

        # Add columns
        table.add_column("Shortcut", style="bold")
        table.add_column("Description")
        table.add_column("Mode")

        # Add rows
        for shortcut, description, mode in self.shortcuts:
            table.add_row(shortcut, description, mode)

        # Create a panel for the table
        panel = Panel(
            table,
            title="WarpAI Pro Keyboard Shortcuts",
            subtitle="Press any key to close",
            border_style="blue",
            padding=(1, 2),
        )

        # Show the panel
        self.console.print(panel)

    def _get_shortcuts(self) -> List[Tuple[str, str, str]]:
        """
        Get the keyboard shortcuts

        Returns:
            List[Tuple[str, str, str]]: List of (shortcut, description, mode) tuples
        """
        # Determine the platform-specific modifier key
        if platform.system() == "Darwin":  # macOS
            mod = "⌘"
            alt = "⌥"
            shift = "⇧"
            ctrl = "⌃"
        else:  # Windows/Linux
            mod = "Ctrl"
            alt = "Alt"
            shift = "Shift"
            ctrl = "Ctrl"

        # Define the shortcuts
        shortcuts = [
            # Mode toggles
            (f"{alt}+A", "Toggle Agent Mode", "Global"),
            (f"{ctrl}+D", "Toggle Dispatch Mode", "Global"),
            (f"{ctrl}+F", "Toggle Follow-up Mode", "Global"),
            (f"{ctrl}+H", "Show Keyboard Help", "Global"),

            # Navigation
            ("↑/↓", "Navigate command history", "Input"),
            ("Tab", "Auto-complete command", "Input"),
            ("Esc", "Clear current command", "Input"),
            ("Enter", "Submit command", "Input"),

            # Command execution
            ("Enter", "Execute last command (when input is empty)", "Input"),

            # Special commands
            ("help", "Show help", "Command"),
            ("exit", "Exit WarpAI", "Command"),
            ("clear", "Clear screen", "Command"),
            ("agent", "Toggle Agent Mode", "Command"),
            ("dispatch", "Toggle Dispatch Mode", "Command"),
            ("follow", "Toggle Follow-up Mode", "Command"),
        ]

        return shortcuts
