"""
Agent Mode implementation for WarpAI

This module provides the main Agent Mode class that handles natural language
interactions with the user and executes commands.
"""
import os
import re
import time
import logging
from typing import Dict, List, Optional, Tuple, Union, Any

from warpai.gemini_client import Gemini<PERSON>lient
from warpai.utils.command_executor import CommandExecutor
from warpai.utils.context_manager import ContextManager
from warpai.config.settings import settings
from warpai.agent_mode.conversation import Conversation
from warpai.agent_mode.detector import NaturalLanguageDetector
from warpai.agent_mode.autonomy import AutonomyManager

# Set up logging
logger = logging.getLogger(__name__)

class AgentMode:
    """
    Agent Mode for WarpAI

    This class provides the main functionality for Agent Mode, allowing users
    to interact with WarpAI using natural language and execute commands.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        context_manager: Optional[ContextManager] = None,
        command_executor: Optional[CommandExecutor] = None,
    ):
        """
        Initialize Agent Mode

        Args:
            api_key: API key for the LLM provider
            model: Model to use for Agent Mode
            context_manager: Context manager instance
            command_executor: Command executor instance
        """
        # Use the model from settings if not provided
        if model is None:
            model = settings.agent_mode_model

        # Initialize clients based on model provider
        self.model = model
        self.api_key = api_key

        # Initialize components
        self.gemini_client = GeminiClient(api_key=api_key, model=model)
        self.context_manager = context_manager or ContextManager()
        self.command_executor = command_executor or CommandExecutor()

        # Initialize Agent Mode components
        self.detector = NaturalLanguageDetector()
        self.autonomy_manager = AutonomyManager()
        self.current_conversation = Conversation()

        # State tracking
        self.is_active = False
        self.is_follow_up = False
        self.attached_blocks = []

        logger.info(f"Agent Mode initialized with model: {model}")

    def activate(self) -> None:
        """Activate Agent Mode"""
        self.is_active = True
        logger.info("Agent Mode activated")

    def deactivate(self) -> None:
        """Deactivate Agent Mode"""
        self.is_active = False
        self.is_follow_up = False
        self.attached_blocks = []
        logger.info("Agent Mode deactivated")

    def toggle(self) -> bool:
        """
        Toggle Agent Mode on/off

        Returns:
            bool: True if Agent Mode is now active, False otherwise
        """
        if self.is_active:
            self.deactivate()
        else:
            self.activate()
        return self.is_active

    def is_natural_language(self, text: str) -> bool:
        """
        Check if text is natural language

        Args:
            text: Text to check

        Returns:
            bool: True if text is natural language, False otherwise
        """
        return self.detector.is_natural_language(text)

    def set_follow_up(self, is_follow_up: bool = True) -> None:
        """
        Set follow-up mode

        Args:
            is_follow_up: Whether to set follow-up mode
        """
        self.is_follow_up = is_follow_up
        logger.debug(f"Follow-up mode set to: {is_follow_up}")

    def attach_block(self, block_content: str) -> None:
        """
        Attach a block to the current query

        Args:
            block_content: Content of the block to attach
        """
        self.attached_blocks.append(block_content)
        logger.debug(f"Block attached: {block_content[:50]}...")

    def clear_attached_blocks(self) -> None:
        """Clear all attached blocks"""
        self.attached_blocks = []
        logger.debug("Attached blocks cleared")

    def process_query(self, query: str) -> Dict[str, Any]:
        """
        Process a natural language query

        Args:
            query: Natural language query

        Returns:
            Dict: Response containing AI output and suggested commands
        """
        # Start a new conversation if not in follow-up mode
        if not self.is_follow_up:
            self.current_conversation = Conversation()

        # Add query to conversation
        self.current_conversation.add_user_message(query)

        # Add attached blocks to conversation
        for block in self.attached_blocks:
            self.current_conversation.add_context(block)

        # Get conversation history
        conversation_history = self.current_conversation.get_history()

        # Generate response
        response = self.gemini_client.generate_response(
            prompt=query,
            conversation_history=conversation_history,
            system_prompt=self._get_system_prompt(),
        )

        # Parse response for commands
        suggested_command = self._extract_command(response)

        # Add response to conversation
        self.current_conversation.add_assistant_message(response)

        # Clear attached blocks
        self.clear_attached_blocks()

        # Return response
        return {
            "response": response,
            "suggested_command": suggested_command,
            "conversation_id": self.current_conversation.id,
        }

    def execute_suggested_command(self, command: str) -> Dict[str, Any]:
        """
        Execute a suggested command

        Args:
            command: Command to execute

        Returns:
            Dict: Execution result
        """
        # Check if command is allowed
        if not self.autonomy_manager.is_command_allowed(command):
            return {
                "success": False,
                "output": "",
                "error": "Command not allowed for security reasons",
            }

        # Execute command
        output, exit_code = self.command_executor.execute_command(command)

        # Add command and output to conversation
        self.current_conversation.add_command(command, output, exit_code == 0)

        # Return result
        return {
            "success": exit_code == 0,
            "output": output,
            "error": "" if exit_code == 0 else output,
        }

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for Agent Mode

        Returns:
            str: System prompt
        """
        return """
        You are WarpAI, an advanced AI assistant integrated into a terminal environment.
        Your goal is to help the user accomplish tasks in the terminal using natural language.

        When responding to the user:
        1. Provide clear, concise explanations
        2. Suggest specific commands that will help accomplish the task
        3. If you need more information, ask for it
        4. If you suggest a command, format it as: COMMAND: <command>

        You have access to the user's terminal environment and can execute commands.
        Always prioritize safety and security when suggesting commands.
        """

    def _extract_command(self, response: str) -> Optional[str]:
        """
        Extract a command from the response

        Args:
            response: Response from the LLM

        Returns:
            Optional[str]: Extracted command, if any
        """
        # Look for explicit command pattern
        command_match = re.search(r"COMMAND:\s*(.+?)(?:\n|$)", response)
        if command_match:
            return command_match.group(1).strip()

        # Look for code block with shell or bash language
        code_block_match = re.search(r"```(?:shell|bash|sh|cmd|powershell|ps1)\n(.*?)\n```", response, re.DOTALL)
        if code_block_match:
            # Extract the first line as the command
            command_lines = code_block_match.group(1).strip().split("\n")
            if command_lines:
                # Remove any leading $ or > prompt characters
                return re.sub(r'^[$>]\s*', '', command_lines[0].strip())

        # Look for lines that look like commands (starting with common CLI tools)
        command_line_match = re.search(r"(?:^|\n)(?:\$\s*|\>\s*)?([a-zA-Z0-9_\-\.]+\s+(?:[-a-zA-Z0-9_\./\"']+\s*)+)(?:\n|$)", response)
        if command_line_match:
            command_candidate = command_line_match.group(1).strip()
            # Check if it starts with a common CLI tool
            common_cli_tools = [
                "python", "pip", "node", "npm", "yarn", "git", "docker", "kubectl",
                "aws", "az", "gcloud", "terraform", "ansible", "vagrant", "ssh",
                "scp", "curl", "wget", "ls", "dir", "cd", "mkdir", "rm", "del",
                "cp", "copy", "mv", "move", "cat", "type", "grep", "find",
                "chmod", "chown", "tar", "zip", "unzip", "gzip", "gunzip",
                "apt", "apt-get", "yum", "dnf", "brew", "pacman", "gem", "go",
                "cargo", "dotnet", "mvn", "gradle", "make", "gcc", "g++",
                "javac", "java", "perl", "ruby", "php", "powershell", "pwsh",
                "cmd", "bash", "sh", "zsh", "fish", "vim", "nano", "emacs",
                "code", "notepad", "subl", "atom", "flutter", "dart", "ionic",
                "cordova", "react-native", "expo", "heroku", "netlify", "vercel"
            ]

            command_start = command_candidate.split()[0].lower()
            if command_start in common_cli_tools:
                return command_candidate

        # Try to infer a command based on context
        if "install" in response.lower() and "package" in response.lower():
            # Look for package installation commands
            package_match = re.search(r"(?:install|add)\s+(?:the\s+)?(?:package\s+)?([a-zA-Z0-9_\-\.]+)", response.lower())
            if package_match:
                package = package_match.group(1).strip()
                # Determine the package manager based on context
                if "pip" in response.lower() or "python" in response.lower():
                    return f"pip install {package}"
                elif "npm" in response.lower() or "node" in response.lower() or "javascript" in response.lower():
                    return f"npm install {package}"
                elif "yarn" in response.lower():
                    return f"yarn add {package}"
                elif "gem" in response.lower() or "ruby" in response.lower():
                    return f"gem install {package}"
                elif "apt" in response.lower() or "ubuntu" in response.lower() or "debian" in response.lower():
                    return f"apt-get install {package}"
                elif "brew" in response.lower() or "homebrew" in response.lower() or "mac" in response.lower():
                    return f"brew install {package}"
                else:
                    return f"pip install {package}"  # Default to pip

        # Look for file operations
        if "create" in response.lower() and "file" in response.lower():
            file_match = re.search(r"create\s+(?:a\s+)?(?:new\s+)?file\s+(?:called\s+)?([a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)", response.lower())
            if file_match:
                file_name = file_match.group(1).strip()
                return f"touch {file_name}"

        if "run" in response.lower() and "file" in response.lower():
            file_match = re.search(r"run\s+(?:the\s+)?file\s+(?:called\s+)?([a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)", response.lower())
            if file_match:
                file_name = file_match.group(1).strip()
                if file_name.endswith(".py"):
                    return f"python {file_name}"
                elif file_name.endswith(".js"):
                    return f"node {file_name}"
                elif file_name.endswith(".sh"):
                    return f"bash {file_name}"
                elif file_name.endswith(".rb"):
                    return f"ruby {file_name}"
                elif file_name.endswith(".pl"):
                    return f"perl {file_name}"
                elif file_name.endswith(".php"):
                    return f"php {file_name}"
                elif file_name.endswith(".java"):
                    return f"java {file_name.replace('.java', '')}"
                elif file_name.endswith(".go"):
                    return f"go run {file_name}"
                elif file_name.endswith(".rs"):
                    return f"rustc {file_name} && ./{file_name.replace('.rs', '')}"
                elif file_name.endswith(".c"):
                    return f"gcc {file_name} -o {file_name.replace('.c', '')} && ./{file_name.replace('.c', '')}"
                elif file_name.endswith(".cpp"):
                    return f"g++ {file_name} -o {file_name.replace('.cpp', '')} && ./{file_name.replace('.cpp', '')}"
                else:
                    return f"./{file_name}"

        # No command found
        return None
