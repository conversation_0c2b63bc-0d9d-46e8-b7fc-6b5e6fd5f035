"""
Error handler module for WarpAI

This module provides error handling capabilities for WarpAI,
allowing it to automatically detect and fix common errors in code files.
"""
import os
import re
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

class ErrorHandler:
    """Error handling for WarpAI"""
    
    def __init__(self):
        """Initialize the error handler"""
        # Define common error patterns for each language
        self.error_patterns = {
            "python": {
                r"ImportError: No module named '([^']+)'": {
                    "type": "missing_import",
                    "fix": "install_package",
                    "message": "Missing package: {0}",
                },
                r"ModuleNotFoundError: No module named '([^']+)'": {
                    "type": "missing_import",
                    "fix": "install_package",
                    "message": "Missing package: {0}",
                },
                r"NameError: name '([^']+)' is not defined": {
                    "type": "undefined_variable",
                    "fix": "define_variable",
                    "message": "Undefined variable: {0}",
                },
                r"SyntaxError: invalid syntax": {
                    "type": "syntax_error",
                    "fix": "fix_syntax",
                    "message": "Syntax error in the code",
                },
                r"IndentationError: ([^']+)": {
                    "type": "indentation_error",
                    "fix": "fix_indentation",
                    "message": "Indentation error: {0}",
                },
                r"TypeError: ([^']+)": {
                    "type": "type_error",
                    "fix": "fix_type",
                    "message": "Type error: {0}",
                },
                r"FileNotFoundError: \[Errno 2\] No such file or directory: '([^']+)'": {
                    "type": "file_not_found",
                    "fix": "create_file",
                    "message": "File not found: {0}",
                },
                r"AttributeError: '([^']+)' object has no attribute '([^']+)'": {
                    "type": "attribute_error",
                    "fix": "fix_attribute",
                    "message": "Attribute error: {0} has no attribute {1}",
                },
                r"KeyError: '([^']+)'": {
                    "type": "key_error",
                    "fix": "fix_key",
                    "message": "Key error: {0}",
                },
                r"IndexError: ([^']+)": {
                    "type": "index_error",
                    "fix": "fix_index",
                    "message": "Index error: {0}",
                },
                r"ValueError: ([^']+)": {
                    "type": "value_error",
                    "fix": "fix_value",
                    "message": "Value error: {0}",
                },
                r"ZeroDivisionError: ([^']+)": {
                    "type": "zero_division",
                    "fix": "fix_division",
                    "message": "Zero division error: {0}",
                },
            },
            "javascript": {
                r"Error: Cannot find module '([^']+)'": {
                    "type": "missing_import",
                    "fix": "install_package",
                    "message": "Missing package: {0}",
                },
                r"ReferenceError: ([^']+) is not defined": {
                    "type": "undefined_variable",
                    "fix": "define_variable",
                    "message": "Undefined variable: {0}",
                },
                r"SyntaxError: ([^']+)": {
                    "type": "syntax_error",
                    "fix": "fix_syntax",
                    "message": "Syntax error: {0}",
                },
                r"TypeError: ([^']+)": {
                    "type": "type_error",
                    "fix": "fix_type",
                    "message": "Type error: {0}",
                },
                r"ENOENT: no such file or directory, open '([^']+)'": {
                    "type": "file_not_found",
                    "fix": "create_file",
                    "message": "File not found: {0}",
                },
                r"TypeError: Cannot read property '([^']+)' of ([^']+)": {
                    "type": "property_error",
                    "fix": "fix_property",
                    "message": "Property error: Cannot read property {0} of {1}",
                },
            },
            "java": {
                r"error: cannot find symbol\s+symbol:\s+([^\n]+)": {
                    "type": "undefined_symbol",
                    "fix": "define_symbol",
                    "message": "Undefined symbol: {0}",
                },
                r"error: ';' expected": {
                    "type": "missing_semicolon",
                    "fix": "add_semicolon",
                    "message": "Missing semicolon",
                },
                r"error: incompatible types: ([^\n]+)": {
                    "type": "type_error",
                    "fix": "fix_type",
                    "message": "Type error: {0}",
                },
                r"error: class ([^']+) not found": {
                    "type": "class_not_found",
                    "fix": "import_class",
                    "message": "Class not found: {0}",
                },
            },
            "c": {
                r"undefined reference to `([^']+)'": {
                    "type": "undefined_reference",
                    "fix": "define_function",
                    "message": "Undefined reference: {0}",
                },
                r"implicit declaration of function '([^']+)'": {
                    "type": "implicit_declaration",
                    "fix": "declare_function",
                    "message": "Implicit declaration of function: {0}",
                },
                r"error: expected ';' before": {
                    "type": "missing_semicolon",
                    "fix": "add_semicolon",
                    "message": "Missing semicolon",
                },
            },
        }
        
        # Define common fixes for each error type
        self.common_fixes = {
            "missing_import": self._fix_missing_import,
            "undefined_variable": self._fix_undefined_variable,
            "syntax_error": self._fix_syntax_error,
            "indentation_error": self._fix_indentation_error,
            "type_error": self._fix_type_error,
            "file_not_found": self._fix_file_not_found,
            "attribute_error": self._fix_attribute_error,
            "key_error": self._fix_key_error,
            "index_error": self._fix_index_error,
            "value_error": self._fix_value_error,
            "zero_division": self._fix_zero_division,
            "undefined_symbol": self._fix_undefined_symbol,
            "missing_semicolon": self._fix_missing_semicolon,
            "class_not_found": self._fix_class_not_found,
            "undefined_reference": self._fix_undefined_reference,
            "implicit_declaration": self._fix_implicit_declaration,
            "property_error": self._fix_property_error,
        }
    
    def analyze_error(self, error_message: str, language: str) -> Dict[str, Any]:
        """
        Analyze an error message
        
        Args:
            error_message: The error message
            language: The programming language
            
        Returns:
            A dictionary with error analysis
        """
        # Check if language is supported
        if language not in self.error_patterns:
            logger.warning(f"Error analysis not supported for {language}")
            return {"type": "unknown", "message": error_message, "fixable": False}
        
        # Check each error pattern
        for pattern, info in self.error_patterns[language].items():
            match = re.search(pattern, error_message)
            if match:
                # Extract groups from the match
                groups = match.groups()
                
                # Format the error message
                message = info["message"].format(*groups)
                
                return {
                    "type": info["type"],
                    "fix": info["fix"],
                    "message": message,
                    "groups": groups,
                    "fixable": True,
                }
        
        # If no pattern matches, return unknown error
        return {"type": "unknown", "message": error_message, "fixable": False}
    
    def fix_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """
        Fix an error in a file
        
        Args:
            error_info: The error information
            file_path: The path to the file
            language: The programming language
            
        Returns:
            A tuple of (success, message)
        """
        # Check if error is fixable
        if not error_info.get("fixable", False):
            return False, "Error is not fixable automatically"
        
        # Get the fix function
        fix_type = error_info.get("fix")
        fix_func = self.common_fixes.get(error_info.get("type"))
        
        if not fix_func:
            return False, f"No fix available for error type: {error_info.get('type')}"
        
        try:
            # Apply the fix
            success, message = fix_func(error_info, file_path, language)
            return success, message
        except Exception as e:
            logger.error(f"Error fixing {error_info.get('type')} in {file_path}: {e}")
            return False, f"Error applying fix: {e}"
    
    def _fix_missing_import(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix missing import error"""
        # This would typically involve installing the package
        # For now, just return a message
        package = error_info.get("groups", [""])[0]
        return False, f"Please install the missing package: {package}"
    
    def _fix_undefined_variable(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix undefined variable error"""
        variable = error_info.get("groups", [""])[0]
        
        try:
            # Read file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.readlines()
            
            # Add variable definition at the beginning of the file
            if language == "python":
                content.insert(0, f"{variable} = None  # Auto-fixed by WarpAI\n")
            elif language == "javascript":
                content.insert(0, f"let {variable} = null;  // Auto-fixed by WarpAI\n")
            
            # Write back to file
            with open(file_path, "w", encoding="utf-8") as f:
                f.writelines(content)
            
            return True, f"Added definition for variable: {variable}"
        except Exception as e:
            logger.error(f"Error fixing undefined variable {variable} in {file_path}: {e}")
            return False, f"Error fixing undefined variable: {e}"
    
    def _fix_syntax_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix syntax error"""
        # Syntax errors are complex and require more context
        # For now, just return a message
        return False, "Syntax errors require manual fixing"
    
    def _fix_indentation_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix indentation error"""
        # Indentation errors are complex and require more context
        # For now, just return a message
        return False, "Indentation errors require manual fixing"
    
    def _fix_type_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix type error"""
        # Type errors are complex and require more context
        # For now, just return a message
        return False, "Type errors require manual fixing"
    
    def _fix_file_not_found(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix file not found error"""
        missing_file = error_info.get("groups", [""])[0]
        
        try:
            # Create the missing file
            with open(missing_file, "w", encoding="utf-8") as f:
                f.write(f"# Auto-created by WarpAI\n")
            
            return True, f"Created missing file: {missing_file}"
        except Exception as e:
            logger.error(f"Error creating missing file {missing_file}: {e}")
            return False, f"Error creating missing file: {e}"
    
    def _fix_attribute_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix attribute error"""
        # Attribute errors are complex and require more context
        # For now, just return a message
        return False, "Attribute errors require manual fixing"
    
    def _fix_key_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix key error"""
        # Key errors are complex and require more context
        # For now, just return a message
        return False, "Key errors require manual fixing"
    
    def _fix_index_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix index error"""
        # Index errors are complex and require more context
        # For now, just return a message
        return False, "Index errors require manual fixing"
    
    def _fix_value_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix value error"""
        # Value errors are complex and require more context
        # For now, just return a message
        return False, "Value errors require manual fixing"
    
    def _fix_zero_division(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix zero division error"""
        # Zero division errors are complex and require more context
        # For now, just return a message
        return False, "Zero division errors require manual fixing"
    
    def _fix_undefined_symbol(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix undefined symbol error"""
        # Undefined symbol errors are complex and require more context
        # For now, just return a message
        return False, "Undefined symbol errors require manual fixing"
    
    def _fix_missing_semicolon(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix missing semicolon error"""
        # Missing semicolon errors are complex and require more context
        # For now, just return a message
        return False, "Missing semicolon errors require manual fixing"
    
    def _fix_class_not_found(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix class not found error"""
        # Class not found errors are complex and require more context
        # For now, just return a message
        return False, "Class not found errors require manual fixing"
    
    def _fix_undefined_reference(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix undefined reference error"""
        # Undefined reference errors are complex and require more context
        # For now, just return a message
        return False, "Undefined reference errors require manual fixing"
    
    def _fix_implicit_declaration(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix implicit declaration error"""
        # Implicit declaration errors are complex and require more context
        # For now, just return a message
        return False, "Implicit declaration errors require manual fixing"
    
    def _fix_property_error(self, error_info: Dict[str, Any], file_path: str, language: str) -> Tuple[bool, str]:
        """Fix property error"""
        # Property errors are complex and require more context
        # For now, just return a message
        return False, "Property errors require manual fixing"
