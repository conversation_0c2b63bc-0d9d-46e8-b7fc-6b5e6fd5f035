"""
Language detector module for WarpAI

This module provides language detection capabilities for WarpAI,
allowing it to determine the programming language of code files
and snippets for proper execution.
"""
import os
import re
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

class LanguageDetector:
    """Language detection for WarpAI"""
    
    def __init__(self):
        """Initialize the language detector"""
        # Define language extensions
        self.language_extensions = {
            "py": "python",
            "js": "javascript",
            "ts": "typescript",
            "html": "html",
            "css": "css",
            "java": "java",
            "c": "c",
            "cpp": "c++",
            "cs": "c#",
            "go": "go",
            "rb": "ruby",
            "php": "php",
            "rs": "rust",
            "swift": "swift",
            "kt": "kotlin",
            "sh": "bash",
            "ps1": "powershell",
            "sql": "sql",
            "md": "markdown",
            "json": "json",
            "xml": "xml",
            "yaml": "yaml",
            "yml": "yaml",
        }
        
        # Define language patterns
        self.language_patterns = {
            "python": [
                r"^#!/usr/bin/env python",
                r"^import\s+[a-zA-Z0-9_]+",
                r"^from\s+[a-zA-Z0-9_\.]+\s+import",
                r"def\s+[a-zA-Z0-9_]+\s*\(",
                r"class\s+[a-zA-Z0-9_]+\s*(\(.*\))?:",
            ],
            "javascript": [
                r"^#!/usr/bin/env node",
                r"const\s+[a-zA-Z0-9_]+\s*=",
                r"let\s+[a-zA-Z0-9_]+\s*=",
                r"var\s+[a-zA-Z0-9_]+\s*=",
                r"function\s+[a-zA-Z0-9_]+\s*\(",
                r"import\s+[a-zA-Z0-9_]+\s+from",
                r"export\s+",
                r"document\.getElementById",
            ],
            "typescript": [
                r"interface\s+[a-zA-Z0-9_]+\s*\{",
                r"type\s+[a-zA-Z0-9_]+\s*=",
                r"class\s+[a-zA-Z0-9_]+\s*implements",
                r":\s*[a-zA-Z0-9_]+\[\]",
                r":\s*[a-zA-Z0-9_]+<",
            ],
            "html": [
                r"<!DOCTYPE\s+html>",
                r"<html",
                r"<head",
                r"<body",
                r"<div",
                r"<span",
                r"<p>",
            ],
            "css": [
                r"^[a-zA-Z0-9_\-\.#]+\s*\{",
                r"@media",
                r"@import",
                r"@keyframes",
                r"@font-face",
            ],
            "java": [
                r"public\s+class\s+[a-zA-Z0-9_]+",
                r"private\s+[a-zA-Z0-9_<>]+\s+[a-zA-Z0-9_]+",
                r"protected\s+[a-zA-Z0-9_<>]+\s+[a-zA-Z0-9_]+",
                r"import\s+java\.",
                r"package\s+[a-zA-Z0-9_\.]+;",
            ],
            "c": [
                r"#include\s+<[a-zA-Z0-9_\.]+>",
                r"int\s+main\s*\(\s*void\s*\)",
                r"int\s+main\s*\(\s*int\s+argc,\s*char\s*\*\s*argv\[\]\s*\)",
                r"void\s+[a-zA-Z0-9_]+\s*\(",
                r"struct\s+[a-zA-Z0-9_]+\s*\{",
            ],
            "c++": [
                r"#include\s+<[a-zA-Z0-9_\.]+>",
                r"using\s+namespace\s+std",
                r"std::",
                r"class\s+[a-zA-Z0-9_]+\s*\{",
                r"template\s*<",
            ],
            "c#": [
                r"using\s+System;",
                r"namespace\s+[a-zA-Z0-9_\.]+",
                r"public\s+class\s+[a-zA-Z0-9_]+",
                r"private\s+[a-zA-Z0-9_<>]+\s+[a-zA-Z0-9_]+",
                r"protected\s+[a-zA-Z0-9_<>]+\s+[a-zA-Z0-9_]+",
            ],
            "go": [
                r"package\s+[a-zA-Z0-9_]+",
                r"import\s+\(",
                r"func\s+[a-zA-Z0-9_]+\s*\(",
                r"type\s+[a-zA-Z0-9_]+\s+struct\s*\{",
            ],
            "ruby": [
                r"^#!/usr/bin/env ruby",
                r"require\s+['\"][a-zA-Z0-9_]+['\"]",
                r"def\s+[a-zA-Z0-9_]+",
                r"class\s+[a-zA-Z0-9_]+",
                r"module\s+[a-zA-Z0-9_]+",
            ],
            "php": [
                r"<\?php",
                r"\$[a-zA-Z0-9_]+\s*=",
                r"function\s+[a-zA-Z0-9_]+\s*\(",
                r"class\s+[a-zA-Z0-9_]+",
                r"namespace\s+[a-zA-Z0-9_\\]+;",
            ],
            "rust": [
                r"fn\s+[a-zA-Z0-9_]+\s*\(",
                r"struct\s+[a-zA-Z0-9_]+",
                r"enum\s+[a-zA-Z0-9_]+",
                r"impl\s+[a-zA-Z0-9_]+",
                r"use\s+[a-zA-Z0-9_:]+;",
            ],
            "bash": [
                r"^#!/bin/bash",
                r"^#!/usr/bin/env bash",
                r"if\s+\[\s+",
                r"for\s+[a-zA-Z0-9_]+\s+in",
                r"while\s+\[\s+",
            ],
            "powershell": [
                r"^#!.*powershell",
                r"\$[a-zA-Z0-9_]+\s*=",
                r"function\s+[a-zA-Z0-9_\-]+\s*\{",
                r"Get-[a-zA-Z0-9_]+",
                r"Set-[a-zA-Z0-9_]+",
            ],
        }
        
        # Define execution commands
        self.execution_commands = {
            "python": "python",
            "javascript": "node",
            "typescript": "ts-node",
            "html": "open",  # Open in browser
            "css": None,  # Cannot be executed directly
            "java": "java",
            "c": "gcc",  # Compile first
            "c++": "g++",  # Compile first
            "c#": "dotnet run",
            "go": "go run",
            "ruby": "ruby",
            "php": "php",
            "rust": "cargo run",  # Assumes Cargo project
            "swift": "swift",
            "kotlin": "kotlin",
            "bash": "bash",
            "powershell": "powershell -File",
            "sql": "sqlite3",  # Assumes SQLite
            "markdown": None,  # Cannot be executed
            "json": None,  # Cannot be executed
            "xml": None,  # Cannot be executed
            "yaml": None,  # Cannot be executed
        }
        
        # Define compilation commands
        self.compilation_commands = {
            "c": "gcc {input_file} -o {output_file}",
            "c++": "g++ {input_file} -o {output_file}",
            "java": "javac {input_file}",
            "rust": "rustc {input_file} -o {output_file}",
            "kotlin": "kotlinc {input_file} -include-runtime -d {output_file}.jar",
        }
        
        # Define needs compilation flag
        self.needs_compilation = {
            "c": True,
            "c++": True,
            "java": True,
            "rust": True,
            "kotlin": True,
        }
    
    def detect_language_from_file(self, file_path: str) -> Optional[str]:
        """
        Detect the programming language from a file
        
        Args:
            file_path: The path to the file
            
        Returns:
            The detected language or None if not detected
        """
        # Check if file exists
        if not os.path.isfile(file_path):
            logger.error(f"File not found: {file_path}")
            return None
        
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lstrip(".").lower()
        
        # Check if extension is in known languages
        if ext in self.language_extensions:
            return self.language_extensions[ext]
        
        # If extension is not recognized, try to detect from content
        return self.detect_language_from_content(file_path)
    
    def detect_language_from_content(self, file_path: str) -> Optional[str]:
        """
        Detect the programming language from file content
        
        Args:
            file_path: The path to the file
            
        Returns:
            The detected language or None if not detected
        """
        try:
            # Read file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            return self.detect_language_from_code(content)
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None
    
    def detect_language_from_code(self, code: str) -> Optional[str]:
        """
        Detect the programming language from code snippet
        
        Args:
            code: The code snippet
            
        Returns:
            The detected language or None if not detected
        """
        # Count matches for each language
        language_scores = {}
        
        for language, patterns in self.language_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, code, re.MULTILINE):
                    score += 1
            
            if score > 0:
                language_scores[language] = score
        
        # Return the language with the highest score
        if language_scores:
            return max(language_scores.items(), key=lambda x: x[1])[0]
        
        return None
    
    def get_execution_command(self, language: str) -> Optional[str]:
        """
        Get the execution command for a language
        
        Args:
            language: The programming language
            
        Returns:
            The execution command or None if not executable
        """
        return self.execution_commands.get(language)
    
    def get_compilation_command(self, language: str) -> Optional[str]:
        """
        Get the compilation command for a language
        
        Args:
            language: The programming language
            
        Returns:
            The compilation command or None if not compilable
        """
        return self.compilation_commands.get(language)
    
    def needs_compilation_step(self, language: str) -> bool:
        """
        Check if a language needs compilation
        
        Args:
            language: The programming language
            
        Returns:
            True if the language needs compilation, False otherwise
        """
        return self.needs_compilation.get(language, False)
