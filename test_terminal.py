#!/usr/bin/env python3
"""
Test script for AdvancedTerminal
"""
import sys
import os
import inspect

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_terminal_import():
    """Test AdvancedTerminal import and constructor"""
    print("🧪 Testing AdvancedTerminal Import...")
    
    try:
        from advanced_terminal import AdvancedTerminal
        print("✅ Successfully imported AdvancedTerminal")
        
        # Check the constructor signature
        sig = inspect.signature(AdvancedTerminal.__init__)
        print(f"Constructor signature: {sig}")
        
        # Check if 'capabilities' is in the parameters
        params = list(sig.parameters.keys())
        print(f"Parameters: {params}")
        
        if 'capabilities' in params:
            print("✅ 'capabilities' parameter found in constructor")
        else:
            print("❌ 'capabilities' parameter NOT found in constructor")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_capabilities():
    """Test AgentCapabilities import"""
    print("\n🧪 Testing AgentCapabilities Import...")
    
    try:
        from advanced_agent import AgentCapabilities
        print("✅ Successfully imported AgentCapabilities")
        
        # Create an instance
        capabilities = AgentCapabilities(async_operations=False)
        print(f"✅ Created AgentCapabilities: {capabilities}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 AdvancedTerminal Test Suite")
    print("=" * 50)
    
    # Test imports
    terminal_success = test_terminal_import()
    capabilities_success = test_agent_capabilities()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Terminal Import: {'✅ PASS' if terminal_success else '❌ FAIL'}")
    print(f"Capabilities Import: {'✅ PASS' if capabilities_success else '❌ FAIL'}")
    
    if all([terminal_success, capabilities_success]):
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n⚠️  Some tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
