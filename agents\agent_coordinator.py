"""
Agent coordinator for WarpAI
"""
from typing import Dict, List, Optional, Tuple, Union, Any
import json

from warpai.agents.base_agent import BaseAgent
from warpai.agents.code_generator import CodeGeneratorAgent
from warpai.agents.error_fixer import ErrorFixerAgent
from warpai.agents.code_explainer import CodeExplainerAgent
from warpai.intelligent_tools.tool_registry import ToolReg<PERSON>ry, ToolDefinition, ToolCategory

class AgentCoordinator:
    """Coordinator for multiple agents"""
    
    def __init__(self, gemini_client=None, api_key: str = None, model: str = None):
        """Initialize the agent coordinator"""
        self.gemini_client = gemini_client
        self.tool_registry = ToolRegistry(api_key=api_key, model=model)
        
        # Initialize agents
        self.agents = {
            "code_generator": CodeGeneratorAgent(gemini_client=gemini_client, tool_registry=self.tool_registry),
            "error_fixer": ErrorFixerAgent(gemini_client=gemini_client, tool_registry=self.tool_registry),
            "code_explainer": CodeExplainerAgent(gemini_client=gemini_client, tool_registry=self.tool_registry),
        }
        
        # Initialize memory
        self.memory = []
        self.max_memory = 20
    
    def process_request(self, request: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process a user request and route to appropriate agent(s)"""
        if context is None:
            context = {}
        
        # Determine which agent(s) to use
        agent_selection = self._select_agents(request, context)
        
        # Process with selected agents
        results = {}
        for agent_name, agent_input in agent_selection.items():
            if agent_name in self.agents:
                agent = self.agents[agent_name]
                results[agent_name] = agent.process(agent_input)
        
        # Combine results if needed
        combined_result = self._combine_results(results, request, context)
        
        # Add to memory
        self.add_to_memory({
            "request": request,
            "context": context,
            "agent_selection": agent_selection,
            "results": results,
            "combined_result": combined_result
        })
        
        return combined_result
    
    def _select_agents(self, request: str, context: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Select which agent(s) to use for a request"""
        # Use the LLM to determine which agent(s) to use
        if self.gemini_client:
            selection_prompt = f"""
User request: {request}

Context: {json.dumps(context, indent=2)}

I have the following specialized agents:
1. code_generator - Generates code based on requirements
2. error_fixer - Fixes errors in code
3. code_explainer - Explains code functionality

I also have access to the following tools. You should use these tools when the request can be directly fulfilled by one of them, or when an agent needs to perform a specific operation that a tool can handle.

Available Tools:
{self._get_tool_schemas_for_prompt()}

Please determine which agent(s) or tool(s) should handle this request and what input they need.
Return your answer as a JSON object. If an agent is selected, use agent names as keys and their input parameters as values. If a tool is selected, use "tool_call" as the key and a dictionary with "tool_name" and "parameters" as values.

Example for agent selection:
{{
  "code_generator": {{
    "task": "Create a function to calculate fibonacci numbers",
    "language": "python"
  }}
}}

Example for tool call:
{{
  "tool_call": {{
    "tool_name": "readFile",
    "parameters": {{
      "file_path": "src/main.py"
    }}
  }}
}}

You can select multiple agents or a single tool.
"""
            
            selection_response = self.gemini_client.generate_response(
                prompt=selection_prompt,
                system_prompt="You are an expert at routing requests to the appropriate specialized agents or tools. Prioritize using specific tools when a request directly matches their capabilities."
            )
            
            try:
                # Extract JSON from the response
                json_str = selection_response
                if "```json" in json_str:
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif "```" in json_str:
                    json_str = json_str.split("```")[1].split("```")[0].strip()
                
                return json.loads(json_str)
            except Exception as e:
                print(f"Error parsing agent/tool selection: {e}")
        
        # Fallback: Use simple keyword matching for agents and tools
        agent_or_tool_selection = {}
        
        request_lower = request.lower()
        
        # Try to match direct tool calls first
        all_tools = self.tool_registry.get_all_tools()
        for tool in all_tools:
            for keyword in tool.keywords:
                if keyword in request_lower:
                    # Simple heuristic: if a tool keyword is present, assume direct tool call
                    # This needs more sophisticated NLP for real-world use
                    if tool.name == "readFile" and "read file" in request_lower:
                        # Example: "read file src/main.py"
                        match = re.search(r"read file\s+([\w./-]+)", request_lower)
                        if match:
                            agent_or_tool_selection["tool_call"] = {
                                "tool_name": "readFile",
                                "parameters": {"file_path": match.group(1)}
                            }
                            return agent_or_tool_selection
                    elif tool.name == "saveFile" and ("save file" in request_lower or "write file" in request_lower):
                        # Example: "save file output.txt with content 'hello'"
                        match = re.search(r"(save|write) file\s+([\w./-]+)\s+with content\s+'(.+)'", request_lower)
                        if match:
                            agent_or_tool_selection["tool_call"] = {
                                "tool_name": "saveFile",
                                "parameters": {"file_path": match.group(2), "content": match.group(3)}
                            }
                            return agent_or_tool_selection
                    # Add more specific tool matching logic here as needed
        
        # Fallback to agent selection if no direct tool call is matched
        if any(kw in request_lower for kw in ["create", "generate", "write", "implement", "build", "develop","make", "code a",]):
            agent_or_tool_selection["code_generator"] = {
                "task": request,
                "language": context.get("language", "python")
            }
        
        if any(kw in request_lower for kw in ["error", "bug", "fix", "issue", "problem", "not working", "fails"]):
            agent_or_tool_selection["error_fixer"] = {
                "error_message": context.get("error_message", ""),
                "code": context.get("code", ""),
                "language": context.get("language", "python")
            }
        
        if any(kw in request_lower for kw in ["explain", "how", "what", "why", "understand", "clarify", "describe"]):
            agent_or_tool_selection["code_explainer"] = {
                "code": context.get("code", ""),
                "language": context.get("language", "python"),
                "detail_level": "medium"
            }
        
        # Default to code generator if no match
        if not agent_or_tool_selection:
            agent_or_tool_selection["code_generator"] = {
                "task": request,
                "language": context.get("language", "python")
            }
        
        return agent_or_tool_selection
    
    def _combine_results(self, results: Dict[str, Any], request: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Combine results from multiple agents or a single tool call"""
        # If a tool was called, return its result directly
        if "tool_call_result" in results:
            tool_result = results["tool_call_result"]
            return {
                "response": f"Tool '{tool_result.get('tool_name', 'unknown')}' executed. Success: {tool_result.get('success')}. Output: {tool_result.get('output', tool_result.get('content', tool_result.get('message', tool_result.get('error', ''))))}",
                "tool": tool_result.get("tool_name"),
                "details": tool_result
            }

        # If only one agent was used, return its result directly
        if len(results) == 1:
            agent_name = list(results.keys())[0]
            return {
                "response": results[agent_name].get("code", results[agent_name].get("explanation", "")),
                "agent": agent_name,
                "details": results[agent_name]
            }
        
        # If multiple agents were used, combine their results
        if self.gemini_client:
            combine_prompt = f"""
User request: {request}

Results from multiple agents:
{json.dumps(results, indent=2)}

Please combine these results into a coherent response that addresses the user's request.
Focus on providing a clear, helpful answer that integrates the insights from all agents.
"""
            
            combined_response = self.gemini_client.generate_response(
                prompt=combine_prompt,
                system_prompt="You are an expert at combining results from multiple specialized agents into a coherent response."
            )
            
            return {
                "response": combined_response,
                "agents": list(results.keys()),
                "details": results
            }
        
        # Fallback: Simple concatenation
        combined_response = ""
        for agent_name, result in results.items():
            combined_response += f"\n\n--- {agent_name.replace('_', ' ').title()} ---\n\n"
            combined_response += result.get("code", result.get("explanation", ""))
        
        return {
            "response": combined_response.strip(),
            "agents": list(results.keys()),
            "details": results
        }
    
    def add_to_memory(self, data: Dict[str, Any]) -> None:
        """Add data to coordinator memory"""
        self.memory.append(data)
        
        # Trim memory if it exceeds the maximum length
        if len(self.memory) > self.max_memory:
            self.memory = self.memory[-self.max_memory:]
    
    def get_memory(self) -> List[Dict[str, Any]]:
        """Get coordinator memory"""
        return self.memory
    
    def clear_memory(self) -> None:
        """Clear coordinator memory"""
        self.memory = []
        
        # Also clear agent memories
        for agent in self.agents.values():
            agent.clear_memory()

    def _get_tool_schemas_for_prompt(self) -> str:
        """Generates a string of tool schemas for the LLM prompt."""
        tool_schemas = []
        for tool_name, tool_def in self.tool_registry.tools.items():
            params_str = ", ".join([f"{p_name}: {p_type.__name__}" for p_name, p_type in tool_def.parameters.items()])
            tool_schemas.append(f"- {tool_def.name}: {tool_def.description} (Parameters: {params_str})")
        return "\n".join(tool_schemas)
