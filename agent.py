"""
Main agent for WarpAI
"""
from typing import Dict, List, Optional, Tuple, Union, Any
import os
import re

from warpai.gemini_client import Gemini<PERSON>lient
from warpai.utils.command_executor import CommandExecutor
from warpai.utils.command_parser import CommandParser
from warpai.utils.context_manager import Context<PERSON>anager
from warpai.utils.file_operations import FileOperations
from warpai.config.settings import settings
from warpai.config.prompts import SYSTEM_PROMPT
from warpai.nlp.processor import NLProcessor

class Agent:
    """Main agent for WarpAI"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """Initialize the agent"""
        self.gemini_client = GeminiClient(api_key=api_key, model=model)
        self.command_executor = CommandExecutor()
        self.command_parser = CommandParser()
        self.context_manager = ContextManager()
        self.file_operations = FileOperations()

        # Initialize NLP processor
        self.nlp_processor = NLProcessor()

        # Initialize conversation
        self.in_conversation = False
        self.last_command = None

    def process_input(self, user_input: str) -> str:
        """Process user input and return a response"""
        # Check for special commands
        if user_input.lower() == "exit":
            return "exit"
        elif user_input.lower() == "help":
            return "help"
        elif user_input.lower() == "clear":
            return "clear"
        elif user_input.lower() == "reset":
            self.context_manager.clear_history()
            self.gemini_client.reset_chat()
            return "Conversation history has been reset."

        # Add user input to context
        self.context_manager.add_to_history("user", user_input)

        # Process input through NLP pipeline
        nlp_results = self.nlp_processor.process(user_input)

        # Check if input is natural language or a command
        if nlp_results["is_natural_language"]:
            # Process as natural language with enhanced understanding
            return self._process_natural_language(user_input, nlp_results)
        else:
            # Process as command
            return self._process_command(user_input)

    def _process_natural_language(self, user_input: str, nlp_results: Optional[Dict[str, Any]] = None) -> str:
        """
        Process natural language input

        Args:
            user_input: The user input text
            nlp_results: Optional NLP processing results
        """
        # Use NLP results to enhance the prompt
        enhanced_prompt = user_input

        if nlp_results:
            # Add intent information if available with high confidence
            if "intent" in nlp_results and nlp_results["intent"]["intent"] != "unknown" and nlp_results["intent"]["confidence"] > 0.7:
                intent_info = nlp_results["intent"]
                enhanced_prompt = f"[Intent: {intent_info['intent']}] {user_input}"

            # Add entity information if available
            if "entities" in nlp_results and nlp_results["entities"]:
                entity_info = "\nEntities detected:\n"
                for entity_type, entity_value in nlp_results["entities"].items():
                    entity_info += f"- {entity_type}: {entity_value}\n"
                enhanced_prompt = f"{enhanced_prompt}\n{entity_info}"

        # Generate a response from Gemini AI
        response = self.gemini_client.generate_response(
            prompt=enhanced_prompt,
            system_prompt=SYSTEM_PROMPT
        )

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        # Extract commands from the response
        commands = self.command_parser.extract_commands(response)

        # Extract code blocks from the response
        code_blocks = self.command_parser.extract_code_blocks(response)

        # If there are code blocks, auto-save the first one
        if code_blocks and len(code_blocks) > 0:
            code = code_blocks[0]["code"]
            language = code_blocks[0]["language"]

            # Determine file extension based on language
            extension = self._get_file_extension(language)

            # Generate a filename based on the content
            filename = self._generate_filename(code, extension)

            # Save the code to the file
            self.save_code_to_file(code, filename)

            # Add the filename to the response
            response += f"\n\nCode has been automatically saved to: {filename}"

        # If there are commands, add them to the response for easy execution
        if commands:
            valid_command = False
            for cmd in commands:
                # Check if it's a valid command
                if self.command_executor.is_command_allowed(cmd):
                    valid_command = True
                    self.last_command = cmd

                    # Auto-execute safe commands
                    output, exit_code = self.command_executor.execute_command(cmd)

                    # Add the output to the response
                    if exit_code == 0:
                        response += f"\n\nCommand executed successfully:\n{output}"
                    else:
                        response += f"\n\nCommand execution failed:\n{output}"

                    # Clear the last command since it's already executed
                    self.last_command = None

                    # Only execute the first valid command
                    break

            # If no valid command was found, don't set last_command
            if not valid_command:
                self.last_command = None

        return response

    def _get_file_extension(self, language: str) -> str:
        """Get file extension based on language"""
        language = language.lower()
        extensions = {
            "python": "py",
            "javascript": "js",
            "typescript": "ts",
            "html": "html",
            "css": "css",
            "java": "java",
            "c": "c",
            "cpp": "cpp",
            "csharp": "cs",
            "go": "go",
            "rust": "rs",
            "ruby": "rb",
            "php": "php",
            "swift": "swift",
            "kotlin": "kt",
            "shell": "sh",
            "bash": "sh",
            "powershell": "ps1",
            "sql": "sql",
            "json": "json",
            "yaml": "yaml",
            "xml": "xml",
            "markdown": "md",
        }
        return extensions.get(language, "txt")

    def _generate_filename(self, code: str, extension: str) -> str:
        """Generate a filename based on code content"""
        import hashlib
        import time

        # Generate a hash of the code
        code_hash = hashlib.md5(code.encode()).hexdigest()[:8]

        # Get current timestamp
        timestamp = int(time.time())

        # Generate filename
        filename = f"warpai_code_{timestamp}_{code_hash}.{extension}"

        return filename

    def _process_command(self, command: str) -> str:
        """Process a command"""
        # Execute the command
        output, exit_code = self.command_executor.execute_command(command)

        # If the command failed, analyze the error
        if exit_code != 0:
            response = self.gemini_client.analyze_error(output)
            self.context_manager.add_to_history("assistant", response)
            return response

        # Analyze the command output
        response = self.gemini_client.analyze_command_output(command, output)

        # Add response to context
        self.context_manager.add_to_history("assistant", response)

        return response

    def execute_last_command(self) -> Tuple[str, int]:
        """Execute the last suggested command"""
        if self.last_command:
            output, exit_code = self.command_executor.execute_command(self.last_command)
            self.last_command = None
            return output, exit_code
        else:
            return "No command to execute", 1

    def get_last_command(self) -> Optional[str]:
        """Get the last suggested command"""
        return self.last_command

    def clear_last_command(self) -> None:
        """Clear the last suggested command"""
        self.last_command = None

    def save_code_to_file(self, code: str, filename: str) -> str:
        """Save code to a file"""
        result, exit_code = self.file_operations.write_file(filename, code)
        if exit_code == 0:
            return f"Code saved to {filename} successfully."
        else:
            return f"Error saving code to {filename}: {result}"
