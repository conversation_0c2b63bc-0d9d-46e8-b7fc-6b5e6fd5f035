"""
Code explainer agent for WarpAI
"""
from typing import Dict, List, Optional, Tuple, Union, Any
import json

from warpai.agents.base_agent import BaseAgent

class CodeExplainerAgent(BaseAgent):
    """Agent specialized in explaining code"""
    
    def __init__(self, gemini_client=None, tool_registry=None):
        """Initialize the code explainer agent"""
        super().__init__(name="CodeExplainer", gemini_client=gemini_client, tool_registry=tool_registry)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the agent"""
        return """
You are CodeExplainer, an AI assistant specialized in explaining code.
Your goal is to help users understand code by providing clear, concise explanations.

Follow these guidelines:
1. Break down complex code into understandable parts
2. Explain the purpose and functionality of the code
3. Highlight important patterns, algorithms, or techniques used
4. Use simple language and avoid unnecessary jargon
5. Provide examples to illustrate concepts when helpful
"""
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and explain code"""
        code = input_data.get("code", "")
        language = input_data.get("language", "python")
        detail_level = input_data.get("detail_level", "medium")  # low, medium, high
        focus = input_data.get("focus", "general")  # general, algorithm, performance, security
        
        # Generate explanation
        prompt = f"""
Code to explain:
```{language}
{code}
```

Detail level: {detail_level}
Focus: {focus}

        Please explain this code. Include:
1. What the code does
2. How it works
3. Key components and their purpose
"""
        
        # Use the analyzeCode tool
        tool_result = self.call_tool(
            "analyzeCode",
            code=code,
            language=language,
            analysis_type=focus # Using focus as analysis_type for now
        )

        if tool_result.get("success"):
            analysis = tool_result.get("analysis", "")
            # Perform self-critique on the analysis
            critique = self.self_critique(analysis, [
                "Accuracy",
                "Clarity",
                "Completeness",
                "Appropriate detail level",
                "Focus relevance"
            ])
            
            # If the score is below 7, try to improve the response
            if critique.get("overall_score", 10) < 7:
                improved_response = self.improve_response(analysis, critique)
                
                self.add_to_memory({
                    "code": code,
                    "language": language,
                    "detail_level": detail_level,
                    "focus": focus,
                    "original_analysis": analysis,
                    "critique": critique,
                    "improved_analysis": improved_response
                })
                
                return {
                    "explanation": improved_response,
                    "critique": critique,
                    "improved": True
                }
            else:
                self.add_to_memory({
                    "code": code,
                    "language": language,
                    "detail_level": detail_level,
                    "focus": focus,
                    "analysis": analysis,
                    "critique": critique
                })
                
                return {
                    "explanation": analysis,
                    "critique": critique,
                    "improved": False
                }
        else:
            return {
                "explanation": "",
                "error": tool_result.get("error", "Failed to analyze code using tool."),
                "improved": False
            }
