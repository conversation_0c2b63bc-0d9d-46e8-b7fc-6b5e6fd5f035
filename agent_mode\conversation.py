"""
Conversation management for Agent Mode

This module provides the Conversation class for managing conversations in Agent Mode.
"""
import re
import time
import uuid
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from warpai.config.settings import settings

@dataclass
class Message:
    """Message in a conversation"""
    role: str  # "user", "assistant", "system", "command", "output"
    content: str
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

class Conversation:
    """
    Conversation class for Agent Mode

    This class manages the conversation history for Agent Mode, including
    user messages, assistant responses, and command execution.
    """

    def __init__(self):
        """Initialize a new conversation"""
        self.id = str(uuid.uuid4())
        self.messages: List[Message] = []
        self.context: List[str] = []
        self.created_at = time.time()
        self.updated_at = time.time()
        self.token_count = 0

    def add_user_message(self, content: str) -> None:
        """
        Add a user message to the conversation

        Args:
            content: Message content
        """
        self.messages.append(Message(role="user", content=content))
        self.updated_at = time.time()
        # Approximate token count (4 chars ~= 1 token)
        self.token_count += len(content) // 4

    def add_assistant_message(self, content: str) -> None:
        """
        Add an assistant message to the conversation

        Args:
            content: Message content
        """
        self.messages.append(Message(role="assistant", content=content))
        self.updated_at = time.time()
        # Approximate token count (4 chars ~= 1 token)
        self.token_count += len(content) // 4

    def add_system_message(self, content: str) -> None:
        """
        Add a system message to the conversation

        Args:
            content: Message content
        """
        self.messages.append(Message(role="system", content=content))
        self.updated_at = time.time()
        # Approximate token count (4 chars ~= 1 token)
        self.token_count += len(content) // 4

    def add_command(self, command: str, output: str, success: bool) -> None:
        """
        Add a command and its output to the conversation

        Args:
            command: Command that was executed
            output: Command output
            success: Whether the command was successful
        """
        self.messages.append(
            Message(
                role="command",
                content=command,
                metadata={"success": success}
            )
        )
        self.messages.append(
            Message(
                role="output",
                content=output,
                metadata={"success": success}
            )
        )
        self.updated_at = time.time()
        # Approximate token count (4 chars ~= 1 token)
        self.token_count += (len(command) + len(output)) // 4

    def add_context(self, content: str) -> None:
        """
        Add context to the conversation

        Args:
            content: Context content
        """
        self.context.append(content)
        # Approximate token count (4 chars ~= 1 token)
        self.token_count += len(content) // 4

    def get_history(self) -> List[Dict[str, str]]:
        """
        Get the conversation history in a format suitable for LLMs

        Returns:
            List[Dict[str, str]]: Conversation history
        """
        # Check if we need to truncate the conversation
        if self.token_count > settings.conversation_max_tokens:
            return self._get_truncated_history()

        # Convert messages to the format expected by LLMs
        history = []

        # Add context if available
        if self.context:
            history.append({
                "role": "system",
                "content": "Context:\n" + "\n".join(self.context)
            })

        # Add messages
        for message in self.messages:
            # Skip system messages (they're added separately)
            if message.role == "system":
                continue

            # Format command and output messages
            if message.role == "command":
                history.append({
                    "role": "user",
                    "content": f"I ran the command: {message.content}"
                })
            elif message.role == "output":
                success = message.metadata.get("success", True)
                prefix = "The command succeeded with output:" if success else "The command failed with error:"
                history.append({
                    "role": "user",
                    "content": f"{prefix}\n{message.content}"
                })
            else:
                # Regular user/assistant messages
                history.append({
                    "role": message.role,
                    "content": message.content
                })

        return history

    def get_summary(self, max_length: int = 200) -> str:
        """
        Get a summary of the conversation

        Args:
            max_length: Maximum length of the summary in words

        Returns:
            str: Summary of the conversation
        """
        # If there are no messages, return an empty string
        if not self.messages:
            return ""

        # Get the last few messages (up to 5)
        recent_messages = self.messages[-5:]

        # Create a summary
        summary = []

        # Add a header
        summary.append(f"Conversation ID: {self.id}")
        summary.append(f"Started: {self._format_timestamp(self.created_at)}")
        summary.append(f"Last updated: {self._format_timestamp(self.updated_at)}")
        summary.append(f"Message count: {len(self.messages)}")
        summary.append("")

        # Add recent messages
        summary.append("Recent messages:")
        for message in recent_messages:
            # Format the message based on its role
            if message.role == "user":
                # Truncate user messages if they're too long
                content = message.content
                if len(content) > 100:
                    content = content[:97] + "..."
                summary.append(f"User: {content}")
            elif message.role == "assistant":
                # Summarize assistant messages
                content = self._summarize_text(message.content, 100)
                summary.append(f"Assistant: {content}")
            elif message.role == "command":
                summary.append(f"Command: {message.content}")
            elif message.role == "output":
                # Truncate output if it's too long
                content = message.content
                if len(content) > 100:
                    content = content[:97] + "..."
                success = message.metadata.get("success", True)
                status = "Success" if success else "Failed"
                summary.append(f"Output ({status}): {content}")
            elif message.role == "system":
                summary.append(f"System: {message.content}")

        # Join the summary
        return "\n".join(summary)

    def _format_timestamp(self, timestamp: float) -> str:
        """
        Format a timestamp

        Args:
            timestamp: Timestamp to format

        Returns:
            str: Formatted timestamp
        """
        from datetime import datetime
        return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

    def _summarize_text(self, text: str, max_length: int = 100) -> str:
        """
        Summarize text to a maximum length

        Args:
            text: Text to summarize
            max_length: Maximum length of the summary

        Returns:
            str: Summarized text
        """
        # If the text is already short enough, return it as is
        if len(text) <= max_length:
            return text

        # Split the text into sentences
        sentences = re.split(r'(?<=[.!?])\s+', text)

        # If there's only one sentence, truncate it
        if len(sentences) <= 1:
            return text[:max_length - 3] + "..."

        # Try to include as many full sentences as possible
        summary = ""
        for sentence in sentences:
            if len(summary) + len(sentence) + 1 <= max_length:
                summary += sentence + " "
            else:
                break

        # If we couldn't include any full sentences, truncate the first one
        if not summary:
            return text[:max_length - 3] + "..."

        return summary.strip()

    def _get_truncated_history(self) -> List[Dict[str, str]]:
        """
        Get a truncated version of the conversation history

        Returns:
            List[Dict[str, str]]: Truncated conversation history
        """
        # Start with the most recent messages and work backwards
        # until we hit the token limit
        history = []
        current_tokens = 0
        max_tokens = settings.conversation_max_tokens

        # Always include context
        context_tokens = sum(len(c) // 4 for c in self.context)
        if self.context:
            history.append({
                "role": "system",
                "content": "Context:\n" + "\n".join(self.context)
            })
            current_tokens += context_tokens

        # Add messages in reverse order
        for message in reversed(self.messages):
            # Skip system messages
            if message.role == "system":
                continue

            # Calculate message tokens
            message_tokens = len(message.content) // 4

            # Check if adding this message would exceed the limit
            if current_tokens + message_tokens > max_tokens:
                break

            # Format the message
            if message.role == "command":
                formatted_message = {
                    "role": "user",
                    "content": f"I ran the command: {message.content}"
                }
            elif message.role == "output":
                success = message.metadata.get("success", True)
                prefix = "The command succeeded with output:" if success else "The command failed with error:"
                formatted_message = {
                    "role": "user",
                    "content": f"{prefix}\n{message.content}"
                }
            else:
                # Regular user/assistant messages
                formatted_message = {
                    "role": message.role,
                    "content": message.content
                }

            # Add message to history and update token count
            history.insert(0, formatted_message)
            current_tokens += message_tokens

        # Add a system message explaining the truncation
        if len(history) < len(self.messages):
            history.insert(0, {
                "role": "system",
                "content": "Note: The conversation history has been truncated due to length constraints."
            })

        return history

    def is_expired(self) -> bool:
        """
        Check if the conversation has expired

        Returns:
            bool: True if the conversation has expired, False otherwise
        """
        timeout = settings.conversation_timeout
        return (time.time() - self.updated_at) > timeout
