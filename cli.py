"""
CLI entry point for WarpAI
"""
import os
import sys
from typing import Optional
import typer
from pathlib import Path

from warpai.terminal import Terminal
from warpai.config.settings import settings

app = typer.Typer(help="WarpAI - A Warp-like AI CLI agent for coding powered by Gemini AI")

@app.command()
def main(
    api_key: Optional[str] = typer.Option(
        None, "--api-key", "-k", help="Gemini API key (overrides environment variable)"
    ),
    model: Optional[str] = typer.Option(
        None, "--model", "-m", help=f"Gemini model to use (default: {settings.default_model})"
    ),
    version: bool = typer.Option(
        False, "--version", "-v", help="Show version and exit"
    ),
) -> None:
    """
    WarpAI - A Warp-like AI CLI agent for coding powered by Gemini AI
    """
    if version:
        from warpai import __version__
        typer.echo(f"WarpAI version {__version__}")
        return
    
    # Check if API key is provided
    if not api_key and not settings.gemini_api_key:
        typer.echo(
            "Gemini API key is required. Set it in the .env file or pass it as an argument."
        )
        typer.echo("You can get a Gemini API key from https://aistudio.google.com/")
        return
    
    # Run the terminal interface
    terminal = Terminal(api_key=api_key, model=model)
    terminal.run()

if __name__ == "__main__":
    app()
