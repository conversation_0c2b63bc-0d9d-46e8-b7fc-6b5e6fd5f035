"""
Command parsing utilities for WarpAI with enhanced natural language understanding
"""
import re
import os
from typing import Dict, List, Optional, Tuple, Union, Any

class CommandParser:
    """Utility for parsing and detecting commands with enhanced natural language understanding"""

    def __init__(self):
        """Initialize the command parser"""
        # Regular expressions for detecting commands
        self.command_patterns = [
            # Common CLI commands
            r"^(cd|ls|dir|pwd|echo|cat|grep|find|git|npm|yarn|pip|python|node|docker|kubectl)\s",
            # File patterns
            r"^[a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+\s",  # file.ext pattern
            r"^\./[a-zA-Z0-9_\-\.]+",  # ./script pattern
            r"^/[a-zA-Z0-9_\-\.]+",  # /path pattern
            # Windows paths
            r"^[a-zA-Z]:\\",  # C:\ pattern
            # Package managers
            r"^(npm|pip|gem|apt-get|brew|choco)\s+(install|uninstall|update|upgrade|list)",
        ]

        # Common natural language indicators
        self.nl_indicators = [
            # Question words
            "how", "what", "why", "when", "where", "who", "which",
            # Modal verbs
            "can", "could", "would", "should", "may", "might", "must", "shall", "will",
            # Action verbs
            "make", "create", "build", "generate", "write", "show", "explain", "help",
            "find", "search", "list", "get", "fetch", "display", "install", "setup",
            "configure", "optimize", "fix", "debug", "solve", "implement", "develop",
            "run", "execute", "start", "launch", "open", "close", "stop", "restart",
            "analyze", "check", "test", "verify", "validate", "compare", "convert",
            # Programming concepts
            "function", "class", "method", "variable", "object", "array", "list",
            "dictionary", "map", "set", "string", "integer", "float", "boolean",
            "algorithm", "data", "structure", "pattern", "design", "architecture",
            "framework", "library", "package", "module", "component", "service",
            "api", "interface", "protocol", "standard", "specification", "schema",
        ]

        # Command prefixes for various programming languages and tools
        self.command_prefixes = [
            # Shell and OS commands
            "cd", "ls", "dir", "pwd", "echo", "cat", "grep", "find", "curl", "wget",
            "mv", "cp", "rm", "rmdir", "mkdir", "touch", "chmod", "chown", "tar", "zip", "unzip",
            "ssh", "scp", "rsync", "ps", "top", "kill", "systemctl", "service",
            # Package managers
            "apt", "apt-get", "yum", "dnf", "brew", "choco", "scoop", "winget", "npm", "yarn", "pip", "gem", "conda",
            # Container and cloud
            "docker", "kubectl", "terraform", "aws", "az", "gcloud", "heroku", "netlify", "vercel",
            # Programming languages
            "python", "node", "java", "javac", "gcc", "g++", "dotnet", "go", "rustc", "perl",
            "ruby", "php", "swift", "dart", "flutter", "kotlin", "scala", "clang", "ghc",
            # Build tools
            "make", "cmake", "mvn", "gradle", "ant", "cargo", "bazel", "webpack", "gulp", "grunt",
            # Version control
            "git", "svn", "hg", "bzr",
            # Database
            "mysql", "psql", "mongo", "redis-cli", "sqlite3",
            # Text editors
            "vim", "nano", "emacs", "code", "subl", "atom", "notepad",
            # Network tools
            "ping", "traceroute", "nslookup", "dig", "netstat", "ifconfig", "ipconfig",
            # File operations
            "awk", "sed", "cut", "sort", "uniq", "wc", "head", "tail", "less", "more",
        ]

    def is_natural_language(self, text: str) -> bool:
        """Detect if the input is natural language or a command"""
        # Clean the input text
        text = text.strip()
        if not text:
            return False

        # Check if it matches command patterns
        for pattern in self.command_patterns:
            if re.match(pattern, text):
                return False

        # Check if it starts with a command prefix
        words = text.lower().split()
        if words and words[0] in self.command_prefixes:
            return False

        # Check if it's a file path
        if re.match(r'^[./\\]?[\w\-./\\]+\.\w+$', text):
            return False

        # Check if it's a URL
        if re.match(r'^(https?|ftp)://', text):
            return False

        # Check if it starts with natural language indicators
        if words and words[0] in self.nl_indicators:
            return True

        # Check if it contains multiple natural language indicators
        nl_count = sum(1 for word in words if word in self.nl_indicators)
        if nl_count >= 2:
            return True

        # Check if it's a question
        if text.endswith("?"):
            return True

        # Check for sentence structure (capital letter, ending punctuation)
        if re.match(r'^[A-Z].*[.!?]$', text):
            return True

        # Default to natural language if it's a longer input (likely a description)
        if len(words) > 3:
            return True

        # Default to command for short inputs
        return False

    def extract_code_blocks(self, text: str) -> List[Dict[str, str]]:
        """Extract code blocks from text"""
        # Match code blocks with language specification
        pattern = r"```(\w+)?\n([\s\S]+?)\n```"
        matches = re.findall(pattern, text)

        code_blocks = []
        for lang, code in matches:
            code_blocks.append({
                "language": lang or "text",
                "code": code.strip()
            })

        return code_blocks

    def extract_commands(self, text: str) -> List[str]:
        """Extract commands from text"""
        commands = []

        # Match inline code blocks (single backticks)
        inline_pattern = r"`([^`]+)`"
        inline_matches = re.findall(inline_pattern, text)

        # Match code blocks with language specification
        block_pattern = r"```(\w+)?\n([\s\S]+?)\n```"
        block_matches = re.findall(block_pattern, text)

        # Extract potential commands from code blocks
        potential_commands = inline_matches + [code for _, code in block_matches]

        # Look for commands after phrases like "run this command" or "execute this"
        command_phrases = [
            r"run this command:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"execute this:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"you can run:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"try running:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"use this command:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"run:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"execute:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"type:?\s*(?:`)?([^`\n]+)(?:`)?",
            r"enter:?\s*(?:`)?([^`\n]+)(?:`)?",
        ]

        for phrase in command_phrases:
            phrase_matches = re.findall(phrase, text, re.IGNORECASE)
            potential_commands.extend(phrase_matches)

        # Filter out non-commands and duplicates
        seen = set()
        for cmd in potential_commands:
            cmd = cmd.strip()

            # Skip if already processed
            if cmd in seen:
                continue

            seen.add(cmd)

            # Only include if it's a single line and looks like a command
            if len(cmd.split("\n")) == 1 and self._is_valid_command(cmd):
                commands.append(cmd)

        # Look for natural language command patterns
        if not commands:
            nl_commands = self._extract_natural_language_commands(text)
            commands.extend(nl_commands)

        return commands

    def _is_valid_command(self, text: str) -> bool:
        """Check if text looks like a valid command"""
        # Skip if too long (likely not a command)
        if len(text) > 200:
            return False

        # Skip if it contains multiple lines
        if "\n" in text:
            return False

        # Check if it starts with any of the command prefixes
        for prefix in self.command_prefixes:
            if text.startswith(prefix + " ") or text == prefix:
                return True

        # Check if it looks like a path execution
        if text.startswith("./") or text.startswith("/") or text.startswith("../"):
            return True

        # Check if it looks like a Windows command
        if re.match(r'^[a-zA-Z]:\\', text) or text.endswith(".exe") or text.endswith(".bat") or text.endswith(".cmd"):
            return True

        # Check if it's a file with extension
        if re.search(r'\.\w{1,5}$', text) and not re.search(r'\s', text):
            return True

        # Check if it's a URL
        if re.match(r'^(https?|ftp)://', text):
            return True

        # Check for common command patterns
        command_patterns = [
            r'^npm (install|uninstall|update|run)',
            r'^pip (install|uninstall|list|freeze)',
            r'^git (clone|pull|push|commit|checkout|add|status)',
            r'^docker (run|build|pull|push|ps|exec)',
            r'^kubectl (get|apply|delete|describe|create)',
        ]

        for pattern in command_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _extract_natural_language_commands(self, text: str) -> List[str]:
        """Extract commands from natural language text"""
        commands = []

        # Common natural language command patterns
        nl_command_patterns = [
            # Run file patterns
            (r'run (?:the )?(?:file )?(?:called )?["\']?([^"\']+\.\w+)["\']?', r'python \1'),
            (r'execute (?:the )?(?:file )?(?:called )?["\']?([^"\']+\.\w+)["\']?', r'python \1'),
            (r'start (?:the )?(?:file )?(?:called )?["\']?([^"\']+\.\w+)["\']?', r'python \1'),

            # Install package patterns
            (r'install (?:the )?(?:package )?(?:called )?["\']?(\w+)["\']?', r'pip install \1'),
            (r'download (?:the )?(?:package )?(?:called )?["\']?(\w+)["\']?', r'pip install \1'),

            # Git operations
            (r'clone (?:the )?(?:repository|repo) (?:at )?["\']?(https://github.com/[^"\']+)["\']?', r'git clone \1'),
            (r'pull (?:the )?(?:latest )?changes', r'git pull'),
            (r'commit (?:the )?changes', r'git commit -m "Update"'),

            # File operations
            (r'list (?:the )?(?:files|contents) (?:in )?(?:the )?(?:directory|folder)(?: called| named)? ?["\']?(\S+)?["\']?',
             lambda m: f'ls {m.group(1)}' if m.group(1) else 'ls'),
            (r'show (?:the )?(?:files|contents) (?:in )?(?:the )?(?:directory|folder)(?: called| named)? ?["\']?(\S+)?["\']?',
             lambda m: f'ls {m.group(1)}' if m.group(1) else 'ls'),
            (r'create (?:a )?(?:directory|folder)(?: called| named)? ["\']?(\S+)["\']?', r'mkdir \1'),
            (r'make (?:a )?(?:directory|folder)(?: called| named)? ["\']?(\S+)["\']?', r'mkdir \1'),

            # Change directory
            (r'(?:change|switch to|go to) (?:the )?(?:directory|folder)(?: called| named)? ["\']?(\S+)["\']?', r'cd \1'),
            (r'navigate to (?:the )?(?:directory|folder)(?: called| named)? ["\']?(\S+)["\']?', r'cd \1'),
        ]

        # Apply patterns
        for pattern, replacement in nl_command_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if callable(replacement):
                    cmd = replacement(match)
                else:
                    cmd = re.sub(pattern, replacement, match.group(0), flags=re.IGNORECASE)

                if cmd and self._is_valid_command(cmd):
                    commands.append(cmd)

        return commands
