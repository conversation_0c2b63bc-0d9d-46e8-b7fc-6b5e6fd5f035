"""
Code generator agent for WarpAI
"""
from typing import Dict, List, Optional, Tuple, Union, Any
import json

from warpai.agents.base_agent import BaseAgent

class CodeGeneratorAgent(BaseAgent):
    """Agent specialized in generating code"""
    
    def __init__(self, gemini_client=None, tool_registry=None):
        """Initialize the code generator agent"""
        super().__init__(name="CodeGenerator", gemini_client=gemini_client, tool_registry=tool_registry)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the agent"""
        return """
You are CodeGenerator, an AI assistant specialized in generating high-quality code.
Your goal is to write clean, efficient, and well-documented code based on user requirements.

Follow these guidelines:
1. Write code that is correct, efficient, and follows best practices
2. Include appropriate comments and documentation
3. Handle edge cases and potential errors
4. Follow the style conventions of the language you're using
5. Provide explanations of your code when helpful
"""
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and generate code"""
        task = input_data.get("task", "")
        language = input_data.get("language", "python")
        context = input_data.get("context", "")
        
        # Generate code
        prompt = f"""
Task: {task}

Language: {language}

{f'Context: {context}' if context else ''}

Please generate code that accomplishes this task. Include comments to explain your approach.
"""
        
        # Use the generateCode tool
        tool_result = self.call_tool(
            "generateCode",
            requirements=task,
            language=language,
            style="clean" # Default style, can be made configurable
        )

        if tool_result.get("success"):
            generated_code = tool_result.get("code", "")
            # Perform self-critique on the generated code
            critique = self.self_critique(generated_code, [
                "Correctness",
                "Efficiency",
                "Readability",
                "Error handling",
                "Documentation"
            ])
            
            # If the score is below 7, try to improve the response
            if critique.get("overall_score", 10) < 7:
                improved_response = self.improve_response(generated_code, critique)
                
                self.add_to_memory({
                    "task": task,
                    "language": language,
                    "original_code": generated_code,
                    "critique": critique,
                    "improved_code": improved_response
                })
                
                return {
                    "code": improved_response,
                    "critique": critique,
                    "improved": True
                }
            else:
                self.add_to_memory({
                    "task": task,
                    "language": language,
                    "code": generated_code,
                    "critique": critique
                })
                
                return {
                    "code": generated_code,
                    "critique": critique,
                    "improved": False
                }
        else:
            return {
                "code": "",
                "error": tool_result.get("error", "Failed to generate code using tool."),
                "improved": False
            }
