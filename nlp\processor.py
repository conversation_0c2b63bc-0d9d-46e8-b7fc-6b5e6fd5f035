"""
Natural Language Processor for WarpAI

This module integrates all NLP/NLU components to provide a comprehensive
natural language processing pipeline for WarpAI.
"""
import re
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

from warpai.nlp.intent_recognition import IntentRecognizer
from warpai.nlp.entity_extraction import EntityExtractor
from warpai.nlp.semantic_understanding import SemanticAnalyzer

# Set up logging
logger = logging.getLogger(__name__)

class NLProcessor:
    """Natural Language Processor for WarpAI"""
    
    def __init__(self):
        """Initialize the NLP processor"""
        self.intent_recognizer = IntentRecognizer()
        self.entity_extractor = EntityExtractor()
        self.semantic_analyzer = SemanticAnalyzer()
    
    def process(self, text: str) -> Dict[str, Any]:
        """
        Process the text through the NLP pipeline
        
        Args:
            text: The text to process
            
        Returns:
            A dictionary containing the processed results
        """
        # Clean the input text
        text = text.strip()
        
        # Initialize results
        results = {
            "original_text": text,
            "is_natural_language": self.is_natural_language(text),
        }
        
        # Only process if it's natural language
        if results["is_natural_language"]:
            # Recognize intent
            intent_results = self.intent_recognizer.recognize_intent(text)
            results["intent"] = intent_results
            
            # Extract entities
            entity_results = self.entity_extractor.extract_entities(text)
            results["entities"] = entity_results
            
            # Merge entities from intent recognition
            if "entities" in intent_results and intent_results["entities"]:
                for entity_type, entity_value in intent_results["entities"].items():
                    if entity_type not in results["entities"]:
                        results["entities"][entity_type] = entity_value
            
            # Analyze semantics
            semantic_results = self.semantic_analyzer.analyze_semantics(text)
            results["semantics"] = semantic_results
            
            # Generate summary
            results["summary"] = self._generate_summary(results)
        
        return results
    
    def is_natural_language(self, text: str) -> bool:
        """
        Determine if the text is natural language or a command
        
        Args:
            text: The text to check
            
        Returns:
            True if the text is natural language, False otherwise
        """
        # Clean the input text
        text = text.strip()
        
        # Check for common command patterns
        command_patterns = [
            r"^(cd|ls|dir|pwd|echo|cat|grep|find|git|npm|yarn|pip|python|node|docker|kubectl)\s",
            r"^[a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+\s",  # file.ext pattern
            r"^\./[a-zA-Z0-9_\-\.]+",  # ./script pattern
            r"^/[a-zA-Z0-9_\-\.]+",  # /path pattern
            r"^[a-zA-Z]:\\",  # C:\ pattern
        ]
        
        for pattern in command_patterns:
            if re.match(pattern, text):
                return False
        
        # Check for common command prefixes
        command_prefixes = [
            "cd", "ls", "dir", "pwd", "echo", "cat", "grep", "find", "curl", "wget",
            "mv", "cp", "rm", "rmdir", "mkdir", "touch", "chmod", "chown", "tar", "zip",
            "git", "npm", "yarn", "pip", "python", "node", "docker", "kubectl",
        ]
        
        words = text.lower().split()
        if words and words[0] in command_prefixes:
            return False
        
        # Check for natural language indicators
        nl_indicators = [
            # Question words
            "how", "what", "why", "when", "where", "who", "which",
            # Modal verbs
            "can", "could", "would", "should", "may", "might", "must", "shall", "will",
            # Action verbs
            "make", "create", "build", "generate", "write", "show", "explain", "help",
            "find", "search", "list", "get", "fetch", "display", "install", "setup",
        ]
        
        # Check if it starts with natural language indicators
        if words and words[0] in nl_indicators:
            return True
        
        # Check if it contains multiple natural language indicators
        nl_count = sum(1 for word in words if word in nl_indicators)
        if nl_count >= 2:
            return True
        
        # Check if it's a question
        if text.endswith("?"):
            return True
        
        # Check for sentence structure (capital letter, ending punctuation)
        if re.match(r'^[A-Z].*[.!?]$', text):
            return True
        
        # Default to natural language if it's a longer input (likely a description)
        if len(words) > 3:
            return True
        
        # Default to command for short inputs
        return False
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary of the NLP processing results
        
        Args:
            results: The NLP processing results
            
        Returns:
            A dictionary containing the summary
        """
        summary = {
            "detected_action": None,
            "primary_entity": None,
            "secondary_entity": None,
            "context": None,
            "confidence": 0.0,
        }
        
        # Extract intent information
        if "intent" in results and results["intent"]["intent"] != "unknown":
            intent = results["intent"]["intent"]
            confidence = results["intent"]["confidence"]
            
            # Map intent to action
            intent_to_action = {
                "run_file": "run",
                "install_package": "install",
                "create_file": "create",
                "edit_file": "edit",
                "explain_code": "explain",
                "fix_error": "fix",
                "generate_code": "generate",
                "search_web": "search",
                "git_operation": "git",
                "help_request": "help",
            }
            
            if intent in intent_to_action:
                summary["detected_action"] = intent_to_action[intent]
                summary["confidence"] = confidence
        
        # Extract primary entity
        if "entities" in results:
            entities = results["entities"]
            
            # Determine primary entity based on intent
            if summary["detected_action"] == "run" and "file_path" in entities:
                summary["primary_entity"] = {"type": "file_path", "value": entities["file_path"]}
            elif summary["detected_action"] == "install" and "package_name" in entities:
                summary["primary_entity"] = {"type": "package_name", "value": entities["package_name"]}
            elif summary["detected_action"] in ["create", "edit"] and "file_path" in entities:
                summary["primary_entity"] = {"type": "file_path", "value": entities["file_path"]}
            elif summary["detected_action"] == "generate" and "code_type" in entities:
                summary["primary_entity"] = {"type": "code_type", "value": entities["code_type"]}
            elif summary["detected_action"] == "search" and "query" in entities:
                summary["primary_entity"] = {"type": "query", "value": entities["query"]}
            elif summary["detected_action"] == "git" and "operation" in entities:
                summary["primary_entity"] = {"type": "operation", "value": entities["operation"]}
            elif summary["detected_action"] == "help" and "topic" in entities:
                summary["primary_entity"] = {"type": "topic", "value": entities["topic"]}
            
            # Determine secondary entity
            if "language" in entities:
                summary["secondary_entity"] = {"type": "language", "value": entities["language"]}
        
        # Extract context from semantics
        if "semantics" in results and "context" in results["semantics"]:
            context = results["semantics"]["context"]
            
            # Simplify context
            summary["context"] = {
                "is_question": context.get("is_question", False),
                "is_command": context.get("is_command", False),
                "refers_to_previous": context.get("refers_to_previous", False),
                "refers_to_code": context.get("refers_to_code", False),
            }
        
        return summary
