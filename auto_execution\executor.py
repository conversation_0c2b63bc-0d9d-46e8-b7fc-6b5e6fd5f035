"""
Code executor module for WarpAI

This module provides code execution capabilities for WarpAI,
allowing it to automatically execute code files with proper
language detection, dependency management, and error handling.
"""
import os
import subprocess
import tempfile
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

from warpai.auto_execution.language_detector import LanguageDetector
from warpai.auto_execution.dependency_manager import DependencyManager
from warpai.auto_execution.error_handler import <PERSON>rror<PERSON>andler

# Set up logging
logger = logging.getLogger(__name__)

class CodeExecutor:
    """Code execution for WarpAI"""
    
    def __init__(self):
        """Initialize the code executor"""
        self.language_detector = LanguageDetector()
        self.dependency_manager = DependencyManager()
        self.error_handler = ErrorHandler()
        
        # Maximum number of retry attempts
        self.max_retries = 3
    
    def execute_file(self, file_path: str, auto_install_deps: bool = True, auto_fix_errors: bool = True) -> Dict[str, Any]:
        """
        Execute a code file
        
        Args:
            file_path: The path to the file
            auto_install_deps: Whether to automatically install dependencies
            auto_fix_errors: Whether to automatically fix errors
            
        Returns:
            A dictionary with execution results
        """
        # Check if file exists
        if not os.path.isfile(file_path):
            logger.error(f"File not found: {file_path}")
            return {
                "success": False,
                "output": "",
                "error": f"File not found: {file_path}",
                "language": None,
                "command": None,
            }
        
        # Detect language
        language = self.language_detector.detect_language_from_file(file_path)
        if not language:
            logger.error(f"Could not detect language for file: {file_path}")
            return {
                "success": False,
                "output": "",
                "error": f"Could not detect language for file: {file_path}",
                "language": None,
                "command": None,
            }
        
        # Get execution command
        exec_cmd = self.language_detector.get_execution_command(language)
        if not exec_cmd:
            logger.error(f"Language {language} is not executable")
            return {
                "success": False,
                "output": "",
                "error": f"Language {language} is not executable",
                "language": language,
                "command": None,
            }
        
        # Check if language needs compilation
        if self.language_detector.needs_compilation_step(language):
            # Compile the file
            compile_result = self._compile_file(file_path, language)
            if not compile_result["success"]:
                return compile_result
            
            # Update file path to the compiled file if needed
            if "output_file" in compile_result:
                file_path = compile_result["output_file"]
        
        # Install dependencies if needed
        if auto_install_deps:
            self._install_dependencies(file_path, language)
        
        # Execute the file
        return self._execute_with_retries(file_path, language, exec_cmd, auto_fix_errors)
    
    def execute_code(self, code: str, language: Optional[str] = None, auto_install_deps: bool = True, auto_fix_errors: bool = True) -> Dict[str, Any]:
        """
        Execute a code snippet
        
        Args:
            code: The code snippet
            language: The programming language (optional, will be detected if not provided)
            auto_install_deps: Whether to automatically install dependencies
            auto_fix_errors: Whether to automatically fix errors
            
        Returns:
            A dictionary with execution results
        """
        # Detect language if not provided
        if not language:
            language = self.language_detector.detect_language_from_code(code)
            if not language:
                logger.error("Could not detect language for code snippet")
                return {
                    "success": False,
                    "output": "",
                    "error": "Could not detect language for code snippet",
                    "language": None,
                    "command": None,
                }
        
        # Get execution command
        exec_cmd = self.language_detector.get_execution_command(language)
        if not exec_cmd:
            logger.error(f"Language {language} is not executable")
            return {
                "success": False,
                "output": "",
                "error": f"Language {language} is not executable",
                "language": language,
                "command": None,
            }
        
        # Create a temporary file
        file_ext = self._get_file_extension(language)
        with tempfile.NamedTemporaryFile(suffix=file_ext, mode="w", delete=False) as temp_file:
            temp_file.write(code)
            temp_file_path = temp_file.name
        
        try:
            # Check if language needs compilation
            if self.language_detector.needs_compilation_step(language):
                # Compile the file
                compile_result = self._compile_file(temp_file_path, language)
                if not compile_result["success"]:
                    return compile_result
                
                # Update file path to the compiled file if needed
                if "output_file" in compile_result:
                    temp_file_path = compile_result["output_file"]
            
            # Install dependencies if needed
            if auto_install_deps:
                self._install_dependencies(temp_file_path, language)
            
            # Execute the file
            return self._execute_with_retries(temp_file_path, language, exec_cmd, auto_fix_errors)
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass
    
    def _compile_file(self, file_path: str, language: str) -> Dict[str, Any]:
        """
        Compile a file
        
        Args:
            file_path: The path to the file
            language: The programming language
            
        Returns:
            A dictionary with compilation results
        """
        # Get compilation command
        compile_cmd = self.language_detector.get_compilation_command(language)
        if not compile_cmd:
            logger.error(f"No compilation command for language {language}")
            return {
                "success": False,
                "output": "",
                "error": f"No compilation command for language {language}",
                "language": language,
                "command": None,
            }
        
        # Create output file path
        output_file = os.path.splitext(file_path)[0]
        if language in ["c", "c++", "rust"]:
            output_file += ".exe" if os.name == "nt" else ""
        
        # Format compilation command
        compile_cmd = compile_cmd.format(input_file=file_path, output_file=output_file)
        
        try:
            # Run compilation command
            logger.info(f"Compiling {file_path} with command: {compile_cmd}")
            result = subprocess.run(compile_cmd, shell=True, check=True, capture_output=True, text=True)
            
            return {
                "success": True,
                "output": result.stdout,
                "error": result.stderr,
                "language": language,
                "command": compile_cmd,
                "output_file": output_file,
            }
        except subprocess.CalledProcessError as e:
            logger.error(f"Error compiling {file_path}: {e.stderr}")
            return {
                "success": False,
                "output": e.stdout,
                "error": e.stderr,
                "language": language,
                "command": compile_cmd,
            }
        except Exception as e:
            logger.error(f"Error compiling {file_path}: {e}")
            return {
                "success": False,
                "output": "",
                "error": str(e),
                "language": language,
                "command": compile_cmd,
            }
    
    def _install_dependencies(self, file_path: str, language: str) -> List[str]:
        """
        Install dependencies for a file
        
        Args:
            file_path: The path to the file
            language: The programming language
            
        Returns:
            A list of installed dependencies
        """
        # Detect dependencies
        dependencies = self.dependency_manager.detect_dependencies(file_path, language)
        
        # Install dependencies
        installed_deps = []
        for dep in dependencies:
            # Check if dependency is already installed
            if self.dependency_manager.check_dependency_installed(dep, language):
                logger.info(f"Dependency {dep} is already installed")
                continue
            
            # Install dependency
            if self.dependency_manager.install_dependency(dep, language):
                installed_deps.append(dep)
        
        return installed_deps
    
    def _execute_with_retries(self, file_path: str, language: str, exec_cmd: str, auto_fix_errors: bool) -> Dict[str, Any]:
        """
        Execute a file with retries
        
        Args:
            file_path: The path to the file
            language: The programming language
            exec_cmd: The execution command
            auto_fix_errors: Whether to automatically fix errors
            
        Returns:
            A dictionary with execution results
        """
        # Format execution command
        if language in ["java"]:
            # For Java, use the class name without extension
            class_name = os.path.splitext(os.path.basename(file_path))[0]
            command = f"{exec_cmd} {class_name}"
        else:
            command = f"{exec_cmd} {file_path}"
        
        # Try executing the file
        for attempt in range(self.max_retries):
            try:
                # Run execution command
                logger.info(f"Executing {file_path} with command: {command} (attempt {attempt+1}/{self.max_retries})")
                result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
                
                return {
                    "success": True,
                    "output": result.stdout,
                    "error": result.stderr,
                    "language": language,
                    "command": command,
                }
            except subprocess.CalledProcessError as e:
                logger.error(f"Error executing {file_path}: {e.stderr}")
                
                # Try to fix the error
                if auto_fix_errors and attempt < self.max_retries - 1:
                    error_info = self.error_handler.analyze_error(e.stderr, language)
                    if error_info["fixable"]:
                        success, message = self.error_handler.fix_error(error_info, file_path, language)
                        if success:
                            logger.info(f"Fixed error: {message}")
                            continue
                
                # If we've reached the maximum number of retries or couldn't fix the error
                if attempt == self.max_retries - 1:
                    return {
                        "success": False,
                        "output": e.stdout,
                        "error": e.stderr,
                        "language": language,
                        "command": command,
                    }
            except Exception as e:
                logger.error(f"Error executing {file_path}: {e}")
                return {
                    "success": False,
                    "output": "",
                    "error": str(e),
                    "language": language,
                    "command": command,
                }
    
    def _get_file_extension(self, language: str) -> str:
        """
        Get the file extension for a language
        
        Args:
            language: The programming language
            
        Returns:
            The file extension
        """
        extensions = {
            "python": ".py",
            "javascript": ".js",
            "typescript": ".ts",
            "html": ".html",
            "css": ".css",
            "java": ".java",
            "c": ".c",
            "c++": ".cpp",
            "c#": ".cs",
            "go": ".go",
            "ruby": ".rb",
            "php": ".php",
            "rust": ".rs",
            "swift": ".swift",
            "kotlin": ".kt",
            "bash": ".sh",
            "powershell": ".ps1",
            "sql": ".sql",
            "markdown": ".md",
            "json": ".json",
            "xml": ".xml",
            "yaml": ".yml",
        }
        
        return extensions.get(language, ".txt")
