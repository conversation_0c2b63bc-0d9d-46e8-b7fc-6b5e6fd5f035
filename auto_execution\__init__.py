"""
Auto-execution module for WarpAI

This module provides advanced auto-execution capabilities for WarpAI,
allowing it to automatically execute code files, install dependencies,
and fix errors based on natural language commands.
"""
from warpai.auto_execution.executor import CodeExecutor
from warpai.auto_execution.dependency_manager import DependencyManager
from warpai.auto_execution.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from warpai.auto_execution.language_detector import LanguageDetector

__all__ = ['CodeExecutor', 'DependencyManager', '<PERSON>rror<PERSON>andler', 'LanguageDetector']
