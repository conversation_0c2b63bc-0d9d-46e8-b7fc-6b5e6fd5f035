"""
Entity extraction module for WarpAI

This module provides advanced entity extraction capabilities for WarpAI,
allowing it to extract relevant entities from natural language input.
"""
import re
import os
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

class EntityExtractor:
    """Entity extraction for WarpAI"""
    
    def __init__(self):
        """Initialize the entity extractor"""
        # Define entity patterns
        self.entity_patterns = {
            "file_path": [
                r'(?:file|path)(?:\s+(?:called|named))?\s+["\']?([^"\']+\.\w+)["\']?',
                r'["\']?([^"\']+\.\w+)["\']?',
                r'(\w+\.\w+)',
            ],
            "directory_path": [
                r'(?:directory|folder|dir)(?:\s+(?:called|named))?\s+["\']?([^"\']+)["\']?',
                r'(?:in|to|from)\s+(?:the\s+)?(?:directory|folder|dir)\s+["\']?([^"\']+)["\']?',
            ],
            "package_name": [
                r'(?:package|module|library|dependency)(?:\s+(?:called|named))?\s+["\']?(\w+)["\']?',
                r'(?:install|download|add)\s+(?:the\s+)?(?:package|module|library|dependency)\s+["\']?(\w+)["\']?',
            ],
            "language": [
                r'(?:in|using|with)\s+(?:the\s+)?(?:language\s+)?["\']?(\w+)["\']?',
                r'(\w+)(?:\s+code|\s+script|\s+program|\s+file)',
            ],
            "git_operation": [
                r'(?:git\s+)?(\w+)(?:\s+the\s+)?(?:changes|code|repository|repo)',
                r'git\s+(\w+)',
            ],
            "url": [
                r'(https?://[^\s]+)',
                r'(www\.[^\s]+)',
            ],
            "code_type": [
                r'(?:a|an)\s+(\w+)(?:\s+function|\s+class|\s+method|\s+module)',
                r'(?:a|an)\s+(\w+)(?:\s+that|\s+which|\s+to)',
            ],
            "error_message": [
                r'(?:error|exception)(?:\s+message)?[:\s]+["\']?([^"\']+)["\']?',
                r'["\']([^"\']+(?:error|exception)[^"\']*)["\']',
            ],
        }
        
        # Programming language detection
        self.language_extensions = {
            "py": "python",
            "js": "javascript",
            "ts": "typescript",
            "html": "html",
            "css": "css",
            "java": "java",
            "c": "c",
            "cpp": "c++",
            "cs": "c#",
            "go": "go",
            "rb": "ruby",
            "php": "php",
            "rs": "rust",
            "swift": "swift",
            "kt": "kotlin",
            "sh": "bash",
            "ps1": "powershell",
            "sql": "sql",
            "md": "markdown",
            "json": "json",
            "xml": "xml",
            "yaml": "yaml",
            "yml": "yaml",
        }
        
        # Common programming languages
        self.programming_languages = [
            "python", "javascript", "typescript", "html", "css", "java", 
            "c", "c++", "c#", "go", "ruby", "php", "rust", "swift", "kotlin",
            "bash", "powershell", "sql", "markdown", "json", "xml", "yaml",
        ]
    
    def extract_entities(self, text: str) -> Dict[str, Any]:
        """
        Extract entities from the text
        
        Args:
            text: The text to extract entities from
            
        Returns:
            A dictionary containing the extracted entities
        """
        # Clean the input text
        text = text.strip()
        
        # Initialize entities dictionary
        entities = {}
        
        # Extract entities using patterns
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match and match.group(1):
                    entities[entity_type] = match.group(1)
                    break
        
        # Post-process entities
        self._post_process_entities(entities, text)
        
        return entities
    
    def _post_process_entities(self, entities: Dict[str, Any], text: str) -> None:
        """
        Post-process extracted entities
        
        Args:
            entities: The extracted entities
            text: The original text
        """
        # Detect language from file extension
        if "file_path" in entities and "language" not in entities:
            file_ext = os.path.splitext(entities["file_path"])[1].lstrip(".")
            if file_ext in self.language_extensions:
                entities["language"] = self.language_extensions[file_ext]
        
        # Detect language from text
        if "language" not in entities:
            for lang in self.programming_languages:
                if lang.lower() in text.lower():
                    entities["language"] = lang
                    break
        
        # Clean up file paths
        if "file_path" in entities:
            # Remove quotes if present
            entities["file_path"] = entities["file_path"].strip('"\'')
            
            # Add current directory prefix if not present
            if not entities["file_path"].startswith(("/", "./", "../", "~/")):
                if not re.match(r'^[a-zA-Z]:\\', entities["file_path"]):  # Not a Windows path
                    entities["file_path"] = "./" + entities["file_path"]
        
        # Clean up directory paths
        if "directory_path" in entities:
            # Remove quotes if present
            entities["directory_path"] = entities["directory_path"].strip('"\'')
            
            # Add current directory prefix if not present
            if not entities["directory_path"].startswith(("/", "./", "../", "~/")):
                if not re.match(r'^[a-zA-Z]:\\', entities["directory_path"]):  # Not a Windows path
                    entities["directory_path"] = "./" + entities["directory_path"]
