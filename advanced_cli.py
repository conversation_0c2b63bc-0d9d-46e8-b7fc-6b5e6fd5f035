"""
Advanced CLI entry point for WarpAI
Enhanced with 2025 AI Agent Architecture Standards
"""
import os
import sys
import asyncio
from typing import Optional
import typer
from pathlib import Path

# Try different import strategies for AdvancedTerminal
try:
    from warpai.advanced_terminal import AdvancedTerminal
    from warpai.config.settings import settings
except ImportError:
    try:
        from advanced_terminal import AdvancedTerminal
        from config.settings import settings
    except ImportError:
        # Fallback imports
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))

        from advanced_terminal import AdvancedTerminal
        from config.settings import settings

# Try different import paths for AgentCapabilities
try:
    from warpai.advanced_agent import AgentCapabilities
except ImportError:
    try:
        from advanced_agent import AgentCapabilities
    except ImportError:
        # Define AgentCapabilities locally if import fails
        from dataclasses import dataclass

        @dataclass
        class AgentCapabilities:
            """Agent capabilities configuration"""
            rag_enabled: bool = True
            web_enabled: bool = True
            multi_agent_enabled: bool = True
            file_watcher_enabled: bool = True
            plugins_enabled: bool = True
            async_operations: bool = True
            autonomous_mode: bool = False
            learning_enabled: bool = True
            memory_persistence: bool = True

app = typer.Typer(help="WarpAI - A Warp-like AI CLI agent for coding powered by Gemini AI")

@app.command()
def main(
    api_key: Optional[str] = typer.Option(
        None, "--api-key", "-k", help="Gemini API key (overrides environment variable)"
    ),
    model: Optional[str] = typer.Option(
        None, "--model", "-m", help=f"Gemini model to use (default: {settings.default_model})"
    ),
    version: bool = typer.Option(
        False, "--version", "-v", help="Show version and exit"
    ),
    advanced: bool = typer.Option(
        True, "--advanced/--basic", help="Use advanced features (default: True)"
    ),
    rag: bool = typer.Option(
        True, "--rag/--no-rag", help="Enable Retrieval Augmented Generation (default: True)"
    ),
    web: bool = typer.Option(
        True, "--web/--no-web", help="Enable web search (default: True)"
    ),
    multi_agent: bool = typer.Option(
        True, "--multi-agent/--no-multi-agent", help="Enable multi-agent system (default: True)"
    ),
    file_watcher: bool = typer.Option(
        True, "--file-watcher/--no-file-watcher", help="Enable file watcher (default: True)"
    ),
    plugins: bool = typer.Option(
        True, "--plugins/--no-plugins", help="Enable plugin system (default: True)"
    ),
    async_ops: bool = typer.Option(
        True, "--async/--no-async", help="Enable async operations (default: True)"
    ),
    autonomous: bool = typer.Option(
        False, "--autonomous/--no-autonomous", help="Enable autonomous mode (default: False)"
    ),
    learning: bool = typer.Option(
        True, "--learning/--no-learning", help="Enable learning capabilities (default: True)"
    ),
    memory: bool = typer.Option(
        True, "--memory/--no-memory", help="Enable memory persistence (default: True)"
    ),
    debug: bool = typer.Option(
        False, "--debug", help="Enable debug mode"
    ),
) -> None:
    """
    WarpAI - A powerful AI CLI agent for coding powered by Gemini AI

    This advanced version includes RAG (Retrieval Augmented Generation), web search,
    multi-agent system, file watching, modern function calling, and async operations.
    """
    if version:
        try:
            from warpai import __version__
            typer.echo(f"WarpAI version {__version__}")
        except ImportError:
            typer.echo("WarpAI version 2.0.0-dev (Enhanced)")
        return

    # Enable debug logging if requested
    if debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
        typer.echo("Debug mode enabled")

    # Check if API key is provided
    if not api_key and not settings.gemini_api_key:
        typer.echo(
            "Gemini API key is required. Set it in the .env file or pass it as an argument."
        )
        typer.echo("You can get a Gemini API key from https://aistudio.google.com/")
        return

    # Create agent capabilities configuration
    capabilities = AgentCapabilities(
        rag_enabled=rag,
        web_enabled=web,
        multi_agent_enabled=multi_agent,
        file_watcher_enabled=file_watcher,
        plugins_enabled=plugins,
        async_operations=async_ops,
        autonomous_mode=autonomous,
        learning_enabled=learning,
        memory_persistence=memory
    )

    # Run the terminal interface
    if advanced:
        terminal = AdvancedTerminal(
            api_key=api_key,
            model=model,
            enable_rag=capabilities.rag_enabled,
            enable_web=capabilities.web_enabled,
            enable_multi_agent=capabilities.multi_agent_enabled,
            enable_file_watcher=capabilities.file_watcher_enabled,
            enable_plugins=capabilities.plugins_enabled,
            capabilities=capabilities
        )
    else:
        from warpai.terminal import Terminal
        terminal = Terminal(api_key=api_key, model=model)

    try:
        if async_ops:
            # Run with async support
            asyncio.run(terminal.run_async())
        else:
            # Run synchronously
            terminal.run()
    except KeyboardInterrupt:
        typer.echo("\nShutting down WarpAI...")
    except Exception as e:
        if debug:
            import traceback
            typer.echo(f"Error: {str(e)}")
            typer.echo("\nTraceback:")
            traceback.print_exc()
        else:
            typer.echo(f"Error: {str(e)}")
            typer.echo("Run with --debug for more information.")

if __name__ == "__main__":
    app()
