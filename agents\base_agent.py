"""
Base agent class for WarpAI
"""
from typing import Dict, <PERSON>, Optional, Tuple, Union, Any
import json

from warpai.intelligent_tools.tool_registry import ToolRegistry

class BaseAgent:
    """Base agent class for WarpAI"""
    
    def __init__(self, name: str, gemini_client=None, tool_registry: Optional[ToolRegistry] = None):
        """Initialize the base agent"""
        self.name = name
        self.gemini_client = gemini_client
        self.tool_registry = tool_registry
        self.memory = []
        self.max_memory = 10
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and return a response"""
        raise NotImplementedError("Subclasses must implement this method")
    
    def add_to_memory(self, data: Dict[str, Any]) -> None:
        """Add data to agent memory"""
        self.memory.append(data)
        
        # Trim memory if it exceeds the maximum length
        if len(self.memory) > self.max_memory:
            self.memory = self.memory[-self.max_memory:]
    
    def get_memory(self) -> List[Dict[str, Any]]:
        """Get agent memory"""
        return self.memory
    
    def clear_memory(self) -> None:
        """Clear agent memory"""
        self.memory = []
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the agent"""
        return f"""
You are {self.name}, an AI assistant specialized in helping with coding tasks.
Your goal is to provide helpful, accurate, and concise responses to user queries.
"""
    
    def generate_response(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate a response using the Gemini client"""
        if self.gemini_client is None:
            return "Error: Gemini client not initialized"
        
        system_prompt = system_prompt or self.get_system_prompt()
        
        return self.gemini_client.generate_response(
            prompt=prompt,
            system_prompt=system_prompt
        )
    
    def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """Call a registered tool"""
        if self.tool_registry is None:
            return {"success": False, "error": "Tool registry not initialized for this agent."}
        
        tool_definition = self.tool_registry.get_tool(tool_name)
        if not tool_definition:
            return {"success": False, "error": f"Tool '{tool_name}' not found in registry."}
        
        try:
            # Validate parameters against schema (basic type check for now)
            for param_name, param_type in tool_definition.parameters.items():
                if param_name not in kwargs:
                    # Handle optional parameters if needed, for now assume all are required
                    if param_type != Optional[str] and param_type != Optional[int] and param_type != Optional[bool] and param_type != Optional[dict] and param_type != Optional[list]:
                        return {"success": False, "error": f"Missing required parameter: {param_name} for tool {tool_name}"}
                elif not isinstance(kwargs[param_name], param_type):
                    # Basic type check, can be expanded for more complex types
                    if not (param_type == str and isinstance(kwargs[param_name], (str, bytes))): # Allow bytes for str
                        return {"success": False, "error": f"Parameter '{param_name}' has incorrect type. Expected {param_type}, got {type(kwargs[param_name])}"}
            
            result = tool_definition.function(**kwargs)
            self.add_to_memory({"tool_call": tool_name, "parameters": kwargs, "result": result})
            return result
        except Exception as e:
            return {"success": False, "error": f"Error calling tool '{tool_name}': {str(e)}"}

    def self_critique(self, response: str, criteria: List[str]) -> Dict[str, Any]:
        """Perform self-critique on a response"""
        if self.gemini_client is None:
            return {"critique": "Error: Gemini client not initialized", "score": 0}
        
        critique_prompt = f"""
Please evaluate the following response based on these criteria:
{', '.join(criteria)}

Response to evaluate:
{response}

For each criterion, provide a score from 1-10 and a brief explanation.
Then provide an overall assessment and suggestions for improvement.
Finally, give an overall score from 1-10.

Format your response as JSON with the following structure:
{{
    "criteria": {{
        "criterion1": {{
            "score": 8,
            "explanation": "Explanation here"
        }},
        ...
    }},
    "overall_assessment": "Overall assessment here",
    "suggestions": ["Suggestion 1", "Suggestion 2", ...],
    "overall_score": 7
}}
"""
        
        critique_response = self.gemini_client.generate_response(
            prompt=critique_prompt,
            system_prompt="You are a critical evaluator of AI responses. Your job is to provide honest, constructive feedback."
        )
        
        try:
            # Extract JSON from the response
            json_str = critique_response
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
            
            critique_data = json.loads(json_str)
            return critique_data
        except Exception as e:
            return {
                "critique": f"Error parsing critique: {str(e)}",
                "score": 0,
                "raw_response": critique_response
            }
    
    def improve_response(self, original_response: str, critique: Dict[str, Any]) -> str:
        """Improve a response based on self-critique"""
        if self.gemini_client is None:
            return original_response
        
        improve_prompt = f"""
I need to improve the following response based on this critique:

Original response:
{original_response}

Critique:
{json.dumps(critique, indent=2)}

Please provide an improved version of the original response that addresses the issues mentioned in the critique.
"""
        
        improved_response = self.gemini_client.generate_response(
            prompt=improve_prompt,
            system_prompt="You are an expert at improving responses based on feedback. Your goal is to create the best possible response."
        )
        
        return improved_response
