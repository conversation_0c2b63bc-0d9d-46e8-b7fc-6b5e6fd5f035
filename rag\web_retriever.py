
import os
import re
import json
import hashlib
from typing import Dict, List, Optional, Tuple, Union, Any
import time
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup

class WebRetriever:
    """Web information retrieval system"""
    
    def __init__(self, cache_dir: str = ".warpai_cache/web"):
        """Initialize the web retriever"""
        self.cache_dir = cache_dir
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # User agent for requests
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        }
    
    def fetch_url(self, url: str, force_refresh: bool = False) -> Dict[str, Any]:
        """Fetch content from a URL"""
        # Check cache first
        cache_key = self._get_cache_key(url)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if not force_refresh and os.path.exists(cache_path):
            try:
                with open(cache_path, "r", encoding="utf-8") as f:
                    cached_data = json.load(f)
                
                # Check if cache is still valid (less than 24 hours old)
                if time.time() - cached_data["timestamp"] < 86400:  # 24 hours
                    return cached_data
            except Exception:
                # If there's an error reading the cache, fetch again
                pass
        
        try:
            # Fetch the URL
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract title
            title = soup.title.string if soup.title else url
            
            # Extract main content
            content = self._extract_main_content(soup)
            
            # Create result
            result = {
                "url": url,
                "title": title,
                "content": content,
                "timestamp": time.time(),
            }
            
            # Save to cache
            with open(cache_path, "w", encoding="utf-8") as f:
                json.dump(result, f)
            
            return result
        except Exception as e:
            return {
                "url": url,
                "title": url,
                "content": f"Error fetching URL: {str(e)}",
                "timestamp": time.time(),
                "error": str(e),
            }
    
    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """Extract the main content from a webpage"""
        # Remove script and style elements
        for script in soup(["script", "style", "header", "footer", "nav"]):
            script.extract()
        
        # Get text
        text = soup.get_text()
        
        # Break into lines and remove leading and trailing space on each
        lines = (line.strip() for line in text.splitlines())
        
        # Break multi-headlines into a line each
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        
        # Drop blank lines
        text = "\n".join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _get_cache_key(self, url: str) -> str:
        """Generate a cache key for a URL"""
        # Parse URL
        parsed_url = urlparse(url)
        
        # Create a key based on domain and path
        key = f"{parsed_url.netloc}{parsed_url.path}"
        
        # Hash the key
        return hashlib.md5(key.encode()).hexdigest()
    
    def search_web(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search the web for information"""
        try:
            # Use DuckDuckGo as a fallback search engine
            search_url = f"https://html.duckduckgo.com/html/?q={query}"
            
            response = requests.get(search_url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract search results
            results = []
            for result in soup.select(".result"):
                title_elem = result.select_one(".result__title")
                url_elem = result.select_one(".result__url")
                snippet_elem = result.select_one(".result__snippet")
                
                if title_elem and url_elem:
                    title = title_elem.get_text().strip()
                    url = url_elem.get_text().strip()
                    snippet = snippet_elem.get_text().strip() if snippet_elem else ""
                    
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet,
                    })
                    
                    if len(results) >= num_results:
                        break
            
            return results
        except Exception as e:
            print(f"Error searching web: {e}")
            return []
