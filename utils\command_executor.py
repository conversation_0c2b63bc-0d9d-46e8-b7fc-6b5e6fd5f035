"""
Command execution utilities for WarpAI with enhanced capabilities
"""
import os
import subprocess
import sys
import re
import json
import time
import platform
import shutil
from typing import Dict, List, Optional, Tuple, Union, Any
import logging

from warpai.config.settings import settings

# Set up logging
logger = logging.getLogger(__name__)

class CommandExecutor:
    """Utility for executing shell commands"""

    def __init__(self):
        """Initialize the command executor"""
        self.platform = platform.system()
        self.shell = "powershell" if self.platform == "Windows" else "/bin/bash"

    def is_command_allowed(self, command: str) -> bool:
        """Check if a command is allowed to be executed"""
        return settings.is_command_allowed(command)

    def execute_command(self, command: str) -> Tuple[str, int]:
        """Execute a shell command and return the output and exit code"""
        if not self.is_command_allowed(command):
            return (
                f"Command '{command}' is not allowed for security reasons. "
                "Please try a different command or contact the administrator.",
                1
            )

        try:
            # Execute the command
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                executable=self.shell if self.platform != "Windows" else None
            )

            # Get the output
            stdout, stderr = process.communicate()

            # Return the output and exit code
            if process.returncode != 0:
                return stderr, process.returncode
            else:
                return stdout, process.returncode
        except Exception as e:
            return str(e), 1

    def execute_script(self, script: str, script_type: str) -> Tuple[str, int]:
        """Execute a script and return the output and exit code"""
        # Create a temporary file
        import tempfile

        with tempfile.NamedTemporaryFile(
            mode="w",
            suffix=f".{script_type}",
            delete=False
        ) as temp_file:
            temp_file.write(script)
            temp_file_path = temp_file.name

        try:
            # Execute the script based on its type
            if script_type == "py":
                command = f"python {temp_file_path}"
            elif script_type == "js":
                command = f"node {temp_file_path}"
            elif script_type in ["sh", "bash"]:
                command = f"bash {temp_file_path}"
            elif script_type == "ps1":
                command = f"powershell -File {temp_file_path}"
            else:
                return f"Unsupported script type: {script_type}", 1

            # Execute the command
            output, exit_code = self.execute_command(command)

            # Return the output and exit code
            return output, exit_code
        finally:
            # Remove the temporary file
            os.unlink(temp_file_path)

    def get_current_directory(self) -> str:
        """Get the current working directory"""
        return os.getcwd()

    def change_directory(self, directory: str) -> Tuple[str, int]:
        """Change the current working directory"""
        try:
            os.chdir(directory)
            return f"Changed directory to {directory}", 0
        except Exception as e:
            return str(e), 1

    def run_file(self, file_path: str) -> Tuple[str, int]:
        """Run a file based on its extension or content"""
        if not os.path.exists(file_path):
            return f"File not found: {file_path}", 1

        # Get the file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower().lstrip('.')

        # If no extension, try to detect file type from content
        if not ext:
            detected_ext = self._detect_file_type_from_content(file_path)
            if detected_ext:
                ext = detected_ext
                logger.info(f"Detected file type: {ext} for {file_path}")

        # Map file extensions to commands
        extension_map = {
            # Python
            'py': f"python {file_path}",
            'pyw': f"pythonw {file_path}",
            'pyc': f"python {file_path}",

            # JavaScript/TypeScript
            'js': f"node {file_path}",
            'mjs': f"node {file_path}",
            'cjs': f"node {file_path}",
            'ts': f"ts-node {file_path}",
            'jsx': f"node {file_path}",
            'tsx': f"ts-node {file_path}",

            # Shell scripts
            'sh': f"bash {file_path}",
            'bash': f"bash {file_path}",
            'zsh': f"zsh {file_path}",
            'bat': f"{file_path}",
            'cmd': f"{file_path}",
            'ps1': f"powershell -File {file_path}",

            # Compiled languages
            'java': f"java {file_path}",
            'c': f"gcc {file_path} -o {os.path.splitext(file_path)[0]} && {os.path.splitext(file_path)[0]}",
            'cpp': f"g++ {file_path} -o {os.path.splitext(file_path)[0]} && {os.path.splitext(file_path)[0]}",
            'cs': f"dotnet run {file_path}",

            # Other interpreted languages
            'go': f"go run {file_path}",
            'rb': f"ruby {file_path}",
            'php': f"php {file_path}",
            'pl': f"perl {file_path}",
            'r': f"Rscript {file_path}",
            'swift': f"swift {file_path}",
            'lua': f"lua {file_path}",
            'groovy': f"groovy {file_path}",
            'scala': f"scala {file_path}",
            'dart': f"dart {file_path}",
            'kt': f"kotlin {file_path}",

            # Web files
            'html': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",
            'htm': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",
            'xhtml': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",

            # Document files
            'md': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",
            'pdf': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",
            'docx': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",
            'xlsx': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",
            'pptx': f"start {file_path}" if platform.system() == "Windows" else f"open {file_path}",

            # Text files
            'txt': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'log': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'csv': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'tsv': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",

            # Data files
            'json': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'yaml': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'yml': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'xml': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'toml': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'ini': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
            'conf': f"type {file_path}" if platform.system() == "Windows" else f"cat {file_path}",
        }

        # Check if the extension is supported
        if ext not in extension_map:
            # Try to run as executable if the file has execute permissions
            if os.access(file_path, os.X_OK):
                command = f"{file_path}"
            else:
                return f"Unsupported file type: {ext}", 1
        else:
            # Get the command to run the file
            command = extension_map[ext]

        # Execute the command
        logger.info(f"Running file: {file_path} with command: {command}")
        output, exit_code = self.execute_command(command)

        # If the command failed, try to fix the issues
        if exit_code != 0:
            logger.warning(f"Failed to run file: {file_path} with error: {output}")
            fixed_output, fixed_exit_code = self._try_fix_and_run(file_path, ext, output)
            if fixed_exit_code == 0:
                return fixed_output, fixed_exit_code

        return output, exit_code

    def _detect_file_type_from_content(self, file_path: str) -> Optional[str]:
        """Detect file type from content"""
        try:
            # Read the first few lines of the file
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(4096)  # Read first 4KB

            # Check for shebang
            if content.startswith('#!/bin/bash') or content.startswith('#!/usr/bin/bash'):
                return 'sh'
            elif content.startswith('#!/bin/sh'):
                return 'sh'
            elif content.startswith('#!/usr/bin/env bash'):
                return 'sh'
            elif content.startswith('#!/usr/bin/env python'):
                return 'py'
            elif content.startswith('#!/usr/bin/python'):
                return 'py'
            elif content.startswith('#!/usr/bin/env node'):
                return 'js'
            elif content.startswith('#!/usr/bin/node'):
                return 'js'
            elif content.startswith('#!/usr/bin/env ruby'):
                return 'rb'
            elif content.startswith('#!/usr/bin/ruby'):
                return 'rb'
            elif content.startswith('#!/usr/bin/env perl'):
                return 'pl'
            elif content.startswith('#!/usr/bin/perl'):
                return 'pl'

            # Check for language-specific patterns
            if re.search(r'import\s+[a-zA-Z0-9_]+|from\s+[a-zA-Z0-9_]+\s+import', content):
                return 'py'
            elif re.search(r'function\s+[a-zA-Z0-9_]+\s*\(|const\s+[a-zA-Z0-9_]+\s*=|let\s+[a-zA-Z0-9_]+\s*=|var\s+[a-zA-Z0-9_]+\s*=', content):
                return 'js'
            elif re.search(r'<html|<!DOCTYPE html', content, re.IGNORECASE):
                return 'html'
            elif re.search(r'public\s+class|public\s+static\s+void\s+main', content):
                return 'java'
            elif re.search(r'#include\s+<[a-zA-Z0-9_]+\.h>', content):
                if re.search(r'class\s+[a-zA-Z0-9_]+|namespace\s+[a-zA-Z0-9_]+|std::', content):
                    return 'cpp'
                else:
                    return 'c'
            elif re.search(r'package\s+main|func\s+[a-zA-Z0-9_]+\s*\(', content):
                return 'go'
            elif re.search(r'require\s+[\'"][a-zA-Z0-9_]+[\'"]|def\s+[a-zA-Z0-9_]+|class\s+[a-zA-Z0-9_]+\s*<', content):
                return 'rb'
            elif re.search(r'<?php', content):
                return 'php'

            # Check for data formats
            if re.search(r'^\s*{', content) and re.search(r'}\s*$', content):
                return 'json'
            elif re.search(r'^\s*<\?xml', content):
                return 'xml'
            elif re.search(r'^\s*[a-zA-Z0-9_]+:\s*$', content):
                return 'yaml'

            # Default to text if we can't determine the type
            return 'txt'

        except Exception as e:
            logger.error(f"Error detecting file type: {str(e)}")
            return None

    def _try_fix_and_run(self, file_path: str, ext: str, error_output: str) -> Tuple[str, int]:
        """Try to fix issues and run the file again"""
        logger.info(f"Attempting to fix issues with file: {file_path}")

        # Check for missing dependencies
        if "ModuleNotFoundError" in error_output or "ImportError" in error_output or "Error: Cannot find module" in error_output:
            # Extract the missing module name
            missing_module = None

            if "ModuleNotFoundError: No module named" in error_output:
                match = re.search(r"ModuleNotFoundError: No module named '([^']+)'", error_output)
                if match:
                    missing_module = match.group(1)
            elif "ImportError: No module named" in error_output:
                match = re.search(r"ImportError: No module named ([^\s]+)", error_output)
                if match:
                    missing_module = match.group(1)
            elif "Error: Cannot find module" in error_output:
                match = re.search(r"Error: Cannot find module '([^']+)'", error_output)
                if match:
                    missing_module = match.group(1)

            if missing_module:
                # Install the missing module
                install_output, install_exit_code = self._install_dependency(missing_module, ext)
                if install_exit_code == 0:
                    logger.info(f"Successfully installed missing dependency: {missing_module}")
                    # Try running the file again
                    return self.run_file(file_path)
                else:
                    logger.error(f"Failed to install missing dependency: {missing_module}")
                    return f"Failed to install missing dependency: {missing_module}\n{install_output}", install_exit_code

        # Check for syntax errors in Python files
        if ext == 'py' and "SyntaxError" in error_output:
            # Try to fix the syntax error
            fixed_output, fixed_exit_code = self._fix_python_syntax_error(file_path, error_output)
            if fixed_exit_code == 0:
                logger.info(f"Successfully fixed syntax error in file: {file_path}")
                # Try running the file again
                return self.run_file(file_path)
            else:
                logger.error(f"Failed to fix syntax error in file: {file_path}")
                return f"Failed to fix syntax error in file: {file_path}\n{fixed_output}", fixed_exit_code

        # If we couldn't fix the issue, return the original error
        return f"Failed to run file and couldn't automatically fix the issues:\n{error_output}", 1

    def _install_dependency(self, module_name: str, file_ext: str) -> Tuple[str, int]:
        """Install a dependency based on the file type"""
        logger.info(f"Installing dependency: {module_name} for file type: {file_ext}")

        # Map file extensions to package managers
        package_managers = {
            'py': f"pip install {module_name}",
            'js': f"npm install {module_name}",
            'ts': f"npm install {module_name} @types/{module_name}",
        }

        # Check if the file extension is supported
        if file_ext not in package_managers:
            return f"Unsupported file type for dependency installation: {file_ext}", 1

        # Get the command to install the dependency
        command = package_managers[file_ext]

        # Execute the command
        logger.info(f"Installing dependency with command: {command}")
        return self.execute_command(command)

    def _fix_python_syntax_error(self, file_path: str, error_output: str) -> Tuple[str, int]:
        """Try to fix Python syntax errors"""
        logger.info(f"Attempting to fix Python syntax error in file: {file_path}")

        try:
            # Read the file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract the line number from the error message
            match = re.search(r"line (\d+)", error_output)
            if not match:
                return f"Could not determine the line number from error: {error_output}", 1

            line_number = int(match.group(1))

            # Extract the error message
            error_message = error_output.split("SyntaxError: ")[-1].strip()

            # Create a backup of the original file
            backup_path = f"{file_path}.bak"
            shutil.copy2(file_path, backup_path)

            # Split the content into lines
            lines = content.split('\n')

            # Get the problematic line
            problematic_line = lines[line_number - 1] if line_number <= len(lines) else ""

            # Common syntax fixes
            fixes = {
                "invalid syntax": self._fix_invalid_syntax,
                "unexpected indent": self._fix_indentation,
                "expected an indented block": self._fix_missing_indentation,
                "unexpected EOF while parsing": self._fix_unexpected_eof,
                "EOL while scanning string literal": self._fix_unclosed_string,
                "unmatched ')'": self._fix_unmatched_parenthesis,
                "unmatched ']'": self._fix_unmatched_bracket,
                "unmatched '}'": self._fix_unmatched_brace,
            }

            # Try to apply a fix
            for error_type, fix_function in fixes.items():
                if error_type in error_message.lower():
                    fixed_content = fix_function(content, line_number, problematic_line)
                    if fixed_content:
                        # Write the fixed content to the file
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(fixed_content)
                        return f"Fixed syntax error: {error_message}", 0

            # If we couldn't fix the issue, restore the backup
            shutil.copy2(backup_path, file_path)
            return f"Could not automatically fix the syntax error: {error_message}", 1

        except Exception as e:
            logger.error(f"Error fixing Python syntax error: {str(e)}")
            return f"Error fixing Python syntax error: {str(e)}", 1

    def _fix_invalid_syntax(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix invalid syntax errors"""
        lines = content.split('\n')

        # Check for missing colons in if/for/while/def statements
        if re.search(r"(if|for|while|def|class|with|try|except|finally)\s+.*[^\s:]\s*$", problematic_line):
            lines[line_number - 1] = problematic_line + ":"
            return '\n'.join(lines)

        # Check for mismatched quotes
        quote_chars = ["'", '"']
        for quote in quote_chars:
            if problematic_line.count(quote) % 2 != 0:
                # Find the last occurrence of the quote
                last_index = problematic_line.rfind(quote)
                if last_index != -1:
                    # Add a matching quote at the end
                    lines[line_number - 1] = problematic_line + quote
                    return '\n'.join(lines)

        return None

    def _fix_indentation(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix indentation errors"""
        lines = content.split('\n')

        # Check the indentation of the previous line
        if line_number > 1:
            prev_line = lines[line_number - 2]
            prev_indent = len(prev_line) - len(prev_line.lstrip())
            curr_indent = len(problematic_line) - len(problematic_line.lstrip())

            # If the current line is indented more than the previous line
            if curr_indent > prev_indent:
                # Check if the previous line ends with a colon
                if prev_line.rstrip().endswith(':'):
                    # Use the same indentation as the previous line plus 4 spaces
                    lines[line_number - 1] = ' ' * (prev_indent + 4) + problematic_line.lstrip()
                else:
                    # Use the same indentation as the previous line
                    lines[line_number - 1] = ' ' * prev_indent + problematic_line.lstrip()
                return '\n'.join(lines)

        return None

    def _fix_missing_indentation(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix missing indentation errors"""
        lines = content.split('\n')

        # Check if the previous line ends with a colon
        if line_number > 1:
            prev_line = lines[line_number - 2]
            if prev_line.rstrip().endswith(':'):
                prev_indent = len(prev_line) - len(prev_line.lstrip())
                # Add indentation to the current line
                lines[line_number - 1] = ' ' * (prev_indent + 4) + problematic_line.lstrip()
                return '\n'.join(lines)

        return None

    def _fix_unexpected_eof(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix unexpected EOF errors"""
        # Check for unclosed parentheses, brackets, or braces
        open_chars = ['(', '[', '{']
        close_chars = [')', ']', '}']
        char_pairs = dict(zip(open_chars, close_chars))

        stack = []
        for char in content:
            if char in open_chars:
                stack.append(char)
            elif char in close_chars:
                if not stack or close_chars[open_chars.index(stack.pop())] != char:
                    # Mismatched closing character
                    return None

        # If there are unclosed characters, add them at the end
        if stack:
            for char in reversed(stack):
                content += char_pairs[char]
            return content

        return None

    def _fix_unclosed_string(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix unclosed string errors"""
        lines = content.split('\n')

        # Check for unclosed quotes
        quote_chars = ["'", '"']
        for quote in quote_chars:
            if problematic_line.count(quote) % 2 != 0:
                # Add a matching quote at the end
                lines[line_number - 1] = problematic_line + quote
                return '\n'.join(lines)

        return None

    def _fix_unmatched_parenthesis(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix unmatched parenthesis errors"""
        # Count opening and closing parentheses
        open_count = content.count('(')
        close_count = content.count(')')

        if open_count > close_count:
            # Add missing closing parentheses
            return content + ')' * (open_count - close_count)
        elif close_count > open_count:
            # Remove extra closing parentheses
            lines = content.split('\n')
            for i in range(len(lines) - 1, -1, -1):
                while ')' in lines[i] and close_count > open_count:
                    lines[i] = lines[i].replace(')', '', 1)
                    close_count -= 1
            return '\n'.join(lines)

        return None

    def _fix_unmatched_bracket(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix unmatched bracket errors"""
        # Count opening and closing brackets
        open_count = content.count('[')
        close_count = content.count(']')

        if open_count > close_count:
            # Add missing closing brackets
            return content + ']' * (open_count - close_count)
        elif close_count > open_count:
            # Remove extra closing brackets
            lines = content.split('\n')
            for i in range(len(lines) - 1, -1, -1):
                while ']' in lines[i] and close_count > open_count:
                    lines[i] = lines[i].replace(']', '', 1)
                    close_count -= 1
            return '\n'.join(lines)

        return None

    def _fix_unmatched_brace(self, content: str, line_number: int, problematic_line: str) -> Optional[str]:
        """Fix unmatched brace errors"""
        # Count opening and closing braces
        open_count = content.count('{')
        close_count = content.count('}')

        if open_count > close_count:
            # Add missing closing braces
            return content + '}' * (open_count - close_count)
        elif close_count > open_count:
            # Remove extra closing braces
            lines = content.split('\n')
            for i in range(len(lines) - 1, -1, -1):
                while '}' in lines[i] and close_count > open_count:
                    lines[i] = lines[i].replace('}', '', 1)
                    close_count -= 1
            return '\n'.join(lines)

        return None
