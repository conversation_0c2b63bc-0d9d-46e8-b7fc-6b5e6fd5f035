"""
File operation utilities for WarpAI with enhanced file type detection and handling
"""
import os
import re
import logging
import mimetypes
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class FileOperations:
    """Utility for file operations with enhanced file type detection and handling"""

    def __init__(self):
        """Initialize the file operations utility"""
        # Initialize mimetypes
        mimetypes.init()

        # Language extension mappings
        self.language_extensions = {
            # Python
            'python': 'py',
            'py': 'py',
            'python3': 'py',
            'python2': 'py',
            'jupyter': 'ipynb',

            # JavaScript/TypeScript
            'javascript': 'js',
            'js': 'js',
            'typescript': 'ts',
            'ts': 'ts',
            'jsx': 'jsx',
            'tsx': 'tsx',
            'node': 'js',

            # Web
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'sass': 'sass',
            'less': 'less',
            'xml': 'xml',
            'svg': 'svg',

            # Shell
            'bash': 'sh',
            'shell': 'sh',
            'sh': 'sh',
            'zsh': 'zsh',
            'powershell': 'ps1',
            'batch': 'bat',
            'cmd': 'cmd',

            # C-family
            'c': 'c',
            'cpp': 'cpp',
            'c++': 'cpp',
            'csharp': 'cs',
            'c#': 'cs',
            'objective-c': 'm',
            'objectivec': 'm',

            # JVM
            'java': 'java',
            'kotlin': 'kt',
            'scala': 'scala',
            'groovy': 'groovy',

            # Other languages
            'go': 'go',
            'golang': 'go',
            'rust': 'rs',
            'ruby': 'rb',
            'php': 'php',
            'perl': 'pl',
            'r': 'r',
            'swift': 'swift',
            'dart': 'dart',
            'lua': 'lua',
            'haskell': 'hs',
            'elixir': 'ex',
            'erlang': 'erl',
            'clojure': 'clj',

            # Data formats
            'json': 'json',
            'yaml': 'yaml',
            'yml': 'yml',
            'toml': 'toml',
            'csv': 'csv',
            'tsv': 'tsv',
            'ini': 'ini',
            'conf': 'conf',

            # Markup
            'markdown': 'md',
            'md': 'md',
            'rst': 'rst',
            'tex': 'tex',
            'latex': 'tex',

            # Other
            'sql': 'sql',
            'dockerfile': 'Dockerfile',
            'makefile': 'Makefile',
        }

    def read_file(self, file_path: str) -> Tuple[str, int]:
        """Read a file and return its contents"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            return content, 0
        except Exception as e:
            return str(e), 1

    def write_file(self, file_path: str, content: str, detect_language: bool = True) -> Tuple[str, int]:
        """
        Write content to a file with automatic language detection

        Args:
            file_path: Path to the file
            content: Content to write
            detect_language: Whether to detect language and add extension if missing

        Returns:
            Tuple of (message, exit_code)
        """
        try:
            # Handle empty or invalid file paths
            if not file_path or not file_path.strip():
                return "File path cannot be empty", 1

            # Normalize the file path
            file_path = os.path.normpath(file_path)

            # If detect_language is enabled and the file has no extension, try to detect it
            _, ext = os.path.splitext(file_path)
            if detect_language and not ext:
                detected_ext = self.detect_language_from_content(content)
                if detected_ext:
                    file_path = f"{file_path}.{detected_ext}"
                    logger.info(f"Detected language extension: {detected_ext} for file: {file_path}")

            # If the file path doesn't have a directory component, save in current directory
            if os.path.dirname(file_path) == '':
                file_path = os.path.join(os.getcwd(), file_path)

            # Create the directory if it doesn't exist
            directory = os.path.dirname(file_path)
            if directory:
                os.makedirs(directory, exist_ok=True)

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return f"File written to {file_path}", 0
        except Exception as e:
            logger.error(f"Error writing file: {str(e)}")
            return str(e), 1

    def detect_language_from_content(self, content: str) -> Optional[str]:
        """
        Detect programming language from content

        Args:
            content: The content to analyze

        Returns:
            File extension for the detected language or None if not detected
        """
        # Skip if content is empty
        if not content or not content.strip():
            return None

        # Check for language-specific patterns
        patterns = {
            # Python
            'py': [
                r'import\s+[a-zA-Z0-9_]+',
                r'from\s+[a-zA-Z0-9_]+\s+import',
                r'def\s+[a-zA-Z0-9_]+\s*\([^)]*\):',
                r'class\s+[a-zA-Z0-9_]+\s*(\([^)]*\))?:',
                r'if\s+__name__\s*==\s*[\'"]__main__[\'"]:'
            ],

            # JavaScript
            'js': [
                r'const\s+[a-zA-Z0-9_]+\s*=',
                r'let\s+[a-zA-Z0-9_]+\s*=',
                r'var\s+[a-zA-Z0-9_]+\s*=',
                r'function\s+[a-zA-Z0-9_]+\s*\(',
                r'export\s+(default\s+)?(class|function|const)',
                r'import\s+.*\s+from\s+[\'"][^\'"]',
                r'document\.getElementById',
                r'window\.',
                r'=>\s*{',
                r'}\)\s*\(',  # Common in jQuery and other JS libraries
            ],

            # TypeScript
            'ts': [
                r'interface\s+[a-zA-Z0-9_]+\s*{',
                r'type\s+[a-zA-Z0-9_]+\s*=',
                r'(const|let|var)\s+[a-zA-Z0-9_]+\s*:\s*[a-zA-Z0-9_<>]+',
                r'function\s+[a-zA-Z0-9_]+\s*\([^)]*\)\s*:\s*[a-zA-Z0-9_<>]+',
                r'class\s+[a-zA-Z0-9_]+\s*implements',
                r'class\s+[a-zA-Z0-9_]+\s*extends',
                r'namespace\s+[a-zA-Z0-9_]+'
            ],

            # HTML
            'html': [
                r'<!DOCTYPE\s+html',
                r'<html',
                r'<head>',
                r'<body>',
                r'<div',
                r'<span',
                r'<p>',
                r'<a\s+href',
                r'<img\s+src',
                r'<script',
                r'<style',
                r'<link\s+rel="stylesheet"'
            ],

            # CSS
            'css': [
                r'body\s*{',
                r'\.[a-zA-Z0-9_-]+\s*{',
                r'#[a-zA-Z0-9_-]+\s*{',
                r'@media\s+',
                r'@import\s+url',
                r'@keyframes',
                r'display:\s*(block|flex|grid|none)',
                r'margin:\s*\d',
                r'padding:\s*\d',
                r'color:\s*#',
                r'background-color:'
            ],

            # Shell/Bash
            'sh': [
                r'#!/bin/(bash|sh)',
                r'#!/usr/bin/(bash|sh)',
                r'#!/usr/bin/env\s+(bash|sh)',
                r'export\s+[A-Z_]+=',
                r'if\s+\[\s+.*\s+\];\s+then',
                r'for\s+[a-zA-Z0-9_]+\s+in',
                r'while\s+\[\s+.*\s+\];\s+do',
                r'case\s+.*\s+in',
                r'echo\s+["\'`]',
                r'\$\([^)]+\)',  # Command substitution
                r'\$\{[^}]+\}'   # Variable substitution
            ],

            # PowerShell
            'ps1': [
                r'function\s+[a-zA-Z0-9_-]+\s*{',
                r'\$[a-zA-Z0-9_]+\s*=',
                r'Get-[a-zA-Z]+',
                r'Set-[a-zA-Z]+',
                r'New-[a-zA-Z]+',
                r'Remove-[a-zA-Z]+',
                r'Write-Host',
                r'Write-Output',
                r'if\s*\(\$[a-zA-Z0-9_]+\s*-',
                r'foreach\s*\(\$[a-zA-Z0-9_]+\s+in\s+'
            ],

            # C/C++
            'cpp': [
                r'#include\s+<[a-zA-Z0-9_]+\.h>',
                r'#include\s+"[a-zA-Z0-9_]+\.h"',
                r'int\s+main\s*\(',
                r'std::',
                r'namespace\s+[a-zA-Z0-9_]+\s*{',
                r'class\s+[a-zA-Z0-9_]+\s*{',
                r'template\s*<',
                r'public:|private:|protected:',
                r'#define\s+[A-Z_]+'
            ],

            # Java
            'java': [
                r'public\s+class\s+[a-zA-Z0-9_]+',
                r'public\s+static\s+void\s+main',
                r'import\s+java\.',
                r'extends\s+[a-zA-Z0-9_]+',
                r'implements\s+[a-zA-Z0-9_]+',
                r'@Override',
                r'System\.out\.println',
                r'new\s+[A-Z][a-zA-Z0-9_]*\s*\('
            ],

            # Go
            'go': [
                r'package\s+main',
                r'import\s+\(',
                r'func\s+[a-zA-Z0-9_]+\s*\(',
                r'type\s+[a-zA-Z0-9_]+\s+struct\s*{',
                r'func\s+\([a-zA-Z0-9_]+\s+\*?[a-zA-Z0-9_]+\)\s+[a-zA-Z0-9_]+',
                r'fmt\.'
            ],

            # Ruby
            'rb': [
                r'require\s+[\'"][^\'"]',
                r'def\s+[a-zA-Z0-9_]+',
                r'class\s+[A-Z][a-zA-Z0-9_]*(\s+<\s+[A-Z][a-zA-Z0-9_]*)?',
                r'module\s+[A-Z][a-zA-Z0-9_]*',
                r'attr_accessor\s+:',
                r'puts\s+',
                r'end$'
            ],

            # PHP
            'php': [
                r'<\?php',
                r'\$[a-zA-Z0-9_]+\s*=',
                r'function\s+[a-zA-Z0-9_]+\s*\(',
                r'class\s+[a-zA-Z0-9_]+',
                r'namespace\s+[a-zA-Z0-9_\\]+;',
                r'use\s+[a-zA-Z0-9_\\]+;',
                r'echo\s+',
                r'require(_once)?\s*\(',
                r'include(_once)?\s*\('
            ],

            # JSON
            'json': [
                r'^\s*{[\s\S]*}\s*$',  # Entire content is a JSON object
                r'^\s*\[[\s\S]*\]\s*$'  # Entire content is a JSON array
            ],

            # YAML
            'yaml': [
                r'^\s*[a-zA-Z0-9_-]+:\s*$',  # Key with nested structure
                r'^\s*- [a-zA-Z0-9_]',  # List item
                r'^\s*[a-zA-Z0-9_-]+:\s+[a-zA-Z0-9_]'  # Key-value pair
            ],

            # Markdown
            'md': [
                r'^#\s+',  # Heading
                r'^\*\*[^*]+\*\*',  # Bold text
                r'^\*[^*]+\*',  # Italic text
                r'^>\s+',  # Blockquote
                r'^\d+\.\s+',  # Ordered list
                r'^\*\s+',  # Unordered list
                r'^\[.+\]\(.+\)',  # Link
                r'^!\[.+\]\(.+\)'  # Image
            ],

            # SQL
            'sql': [
                r'SELECT\s+.+\s+FROM\s+[a-zA-Z0-9_]+',
                r'INSERT\s+INTO\s+[a-zA-Z0-9_]+',
                r'UPDATE\s+[a-zA-Z0-9_]+\s+SET',
                r'DELETE\s+FROM\s+[a-zA-Z0-9_]+',
                r'CREATE\s+TABLE\s+[a-zA-Z0-9_]+',
                r'ALTER\s+TABLE\s+[a-zA-Z0-9_]+',
                r'DROP\s+TABLE\s+[a-zA-Z0-9_]+',
                r'JOIN\s+[a-zA-Z0-9_]+\s+ON',
                r'WHERE\s+[a-zA-Z0-9_]+\s*=',
                r'GROUP\s+BY\s+[a-zA-Z0-9_]+',
                r'ORDER\s+BY\s+[a-zA-Z0-9_]+'
            ]
        }

        # Count matches for each language
        scores = {}
        for lang, patterns_list in patterns.items():
            score = 0
            for pattern in patterns_list:
                matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                score += len(matches)
            if score > 0:
                scores[lang] = score

        # Return the language with the highest score
        if scores:
            best_lang = max(scores.items(), key=lambda x: x[1])[0]
            return best_lang

        # If no patterns matched, try to guess based on content structure
        if '{' in content and '}' in content and ':' in content:
            # Could be JSON or a C-family language
            if '"' in content and ':' in content and re.search(r'"[^"]+"\s*:', content):
                return 'json'
            else:
                return 'js'  # Default to JavaScript

        # Default to text if we can't determine the language
        return 'txt'

    def append_file(self, file_path: str, content: str) -> Tuple[str, int]:
        """Append content to a file"""
        try:
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, "a", encoding="utf-8") as f:
                f.write(content)
            return f"Content appended to {file_path}", 0
        except Exception as e:
            return str(e), 1

    def list_directory(self, directory: str = ".") -> Tuple[List[str], int]:
        """List the contents of a directory"""
        try:
            contents = os.listdir(directory)
            return contents, 0
        except Exception as e:
            return [str(e)], 1

    def get_file_info(self, file_path: str) -> Tuple[Dict[str, Union[str, int]], int]:
        """Get information about a file"""
        try:
            stat = os.stat(file_path)
            info = {
                "size": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "accessed": stat.st_atime,
                "is_directory": os.path.isdir(file_path),
                "is_file": os.path.isfile(file_path),
                "exists": os.path.exists(file_path),
            }
            return info, 0
        except Exception as e:
            return {"error": str(e)}, 1

    def find_files(self, directory: str, pattern: str) -> Tuple[List[str], int]:
        """Find files matching a pattern in a directory"""
        try:
            import glob

            # Construct the pattern
            search_pattern = os.path.join(directory, pattern)

            # Find files matching the pattern
            files = glob.glob(search_pattern, recursive=True)

            return files, 0
        except Exception as e:
            return [str(e)], 1
