"""
Iterative Analyzer for WarpAI

This module provides iterative analysis capabilities that execute, analyze, and adapt based on results.
"""

import json
import logging
import re
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from warpai.intelligent_tools.tool_selector import IntelligentToolSelector, ToolSelection
from warpai.gemini_client import GeminiClient
from warpai.config.settings import settings

logger = logging.getLogger(__name__)

class AnalysisStatus(Enum):
    """Status of analysis steps"""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    ANALYZING = "analyzing"
    ADAPTING = "adapting"

@dataclass
class AnalysisStep:
    """Represents a single step in the iterative analysis process"""
    step_id: int
    tool_selection: ToolSelection
    status: AnalysisStatus = AnalysisStatus.PENDING
    execution_result: Optional[Dict[str, Any]] = None
    analysis_result: Optional[Dict[str, Any]] = None
    next_actions: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    error_message: Optional[str] = None

@dataclass
class IterativeSession:
    """Represents an iterative analysis session"""
    session_id: str
    user_input: str
    goal: str
    steps: List[AnalysisStep] = field(default_factory=list)
    current_step: int = 0
    status: AnalysisStatus = AnalysisStatus.PENDING
    context: Dict[str, Any] = field(default_factory=dict)
    max_iterations: int = 10
    success_criteria: List[str] = field(default_factory=list)

class IterativeAnalyzer:
    """
    Iterative analyzer that implements the execute → analyze → plan → execute cycle
    """

    def __init__(self, api_key: str = None, model: str = None, tool_registry=None):
        """Initialize the iterative analyzer"""
        self.tool_registry = tool_registry if tool_registry else ToolRegistry(api_key=api_key, model=model)
        self.tool_selector = IntelligentToolSelector(api_key=api_key, model=model, tool_registry=self.tool_registry)
        self.gemini_client = GeminiClient(api_key=api_key, model=model or settings.model)

        self.active_sessions: Dict[str, IterativeSession] = {}
        self.session_counter = 0

        logger.info("Iterative analyzer initialized")

    def start_analysis(self, user_input: str, context: Dict[str, Any] = None) -> str:
        """
        Start a new iterative analysis session

        Args:
            user_input: The user's input/request
            context: Additional context information

        Returns:
            Session ID for tracking the analysis
        """
        if context is None:
            context = {}

        # Create new session
        session_id = f"session_{self.session_counter}"
        self.session_counter += 1

        # Extract goal from user input
        goal = self._extract_goal(user_input)

        # Create session
        session = IterativeSession(
            session_id=session_id,
            user_input=user_input,
            goal=goal,
            context=context,
            success_criteria=self._define_success_criteria(user_input, goal)
        )

        self.active_sessions[session_id] = session

        logger.info(f"Started analysis session {session_id} with goal: {goal}")

        # Start the iterative process
        self._execute_iteration(session_id)

        return session_id

    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get the current status of an analysis session"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {"error": "Session not found"}

        return {
            "session_id": session_id,
            "status": session.status.value,
            "current_step": session.current_step,
            "total_steps": len(session.steps),
            "goal": session.goal,
            "progress": self._calculate_progress(session),
            "last_result": self._get_last_result(session)
        }

    def continue_analysis(self, session_id: str, user_feedback: str = None) -> Dict[str, Any]:
        """Continue analysis with optional user feedback"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {"error": "Session not found"}

        if user_feedback:
            session.context["user_feedback"] = user_feedback
            logger.info(f"Received user feedback for session {session_id}: {user_feedback}")

        # Continue the iterative process
        return self._execute_iteration(session_id)

    def _execute_iteration(self, session_id: str) -> Dict[str, Any]:
        """Execute one iteration of the analyze-execute-adapt cycle"""
        session = self.active_sessions[session_id]

        try:
            # Check if we've reached max iterations
            if session.current_step >= session.max_iterations:
                session.status = AnalysisStatus.FAILED
                return {
                    "status": "failed",
                    "reason": "Maximum iterations reached",
                    "session_id": session_id
                }

            # Step 1: Select tools if this is the first step or we need new tools
            if not session.steps or self._needs_new_tool_selection(session):
                tool_selections = self._select_next_tools(session)
                if not tool_selections:
                    session.status = AnalysisStatus.COMPLETED
                    return {
                        "status": "completed",
                        "reason": "No more tools needed",
                        "session_id": session_id
                    }

                # Create new step
                step = AnalysisStep(
                    step_id=len(session.steps),
                    tool_selection=tool_selections[0]  # Take the first/best selection
                )
                session.steps.append(step)
            else:
                step = session.steps[session.current_step]

            # Step 2: Execute the tool
            execution_result = self._execute_tool(step)
            step.execution_result = execution_result
            step.status = AnalysisStatus.COMPLETED if execution_result.get("success") else AnalysisStatus.FAILED

            # Step 3: Analyze the results
            analysis_result = self._analyze_execution_result(session, step)
            step.analysis_result = analysis_result

            # Step 4: Plan next actions based on analysis
            next_actions = self._plan_next_actions(session, step, analysis_result)
            step.next_actions = next_actions

            # Step 5: Check if goal is achieved
            if self._is_goal_achieved(session):
                session.status = AnalysisStatus.COMPLETED
                return {
                    "status": "completed",
                    "result": self._compile_final_result(session),
                    "session_id": session_id
                }

            # Step 6: Prepare for next iteration
            session.current_step += 1

            # If there are immediate next actions, continue automatically
            if next_actions and any("continue" in action.lower() for action in next_actions):
                return self._execute_iteration(session_id)

            return {
                "status": "in_progress",
                "step_result": {
                    "step_id": step.step_id,
                    "tool_used": step.tool_selection.tool.name,
                    "execution_result": execution_result,
                    "analysis": analysis_result,
                    "next_actions": next_actions
                },
                "session_id": session_id
            }

        except Exception as e:
            logger.error(f"Error in iteration for session {session_id}: {e}")
            session.status = AnalysisStatus.FAILED
            return {
                "status": "failed",
                "error": str(e),
                "session_id": session_id
            }

    def _extract_goal(self, user_input: str) -> str:
        """Extract the main goal from user input"""
        prompt = f"""
        Analyze the following user input and extract the main goal or objective:

        User Input: {user_input}

        Return a clear, concise statement of what the user wants to achieve.
        Focus on the end result, not the specific steps.

        Example:
        Input: "Create a Python script that reads a CSV file and generates a report"
        Goal: "Generate a report from CSV data using Python"

        Goal:"""

        try:
            response = self.gemini_client.generate_response(prompt)
            return response.strip()
        except Exception as e:
            logger.error(f"Error extracting goal: {e}")
            return user_input  # Fallback to original input

    def _define_success_criteria(self, user_input: str, goal: str) -> List[str]:
        """Define success criteria for the goal"""
        prompt = f"""
        Define 3-5 specific, measurable success criteria for the following goal:

        Goal: {goal}
        Original Request: {user_input}

        Return criteria as a JSON array of strings. Each criterion should be specific and verifiable.

        Example:
        ["Code executes without errors", "Output matches expected format", "All requirements are implemented"]

        Success Criteria:"""

        try:
            response = self.gemini_client.generate_response(prompt)
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
        except Exception as e:
            logger.error(f"Error defining success criteria: {e}")

        return ["Task completed successfully"]  # Fallback

    def _select_next_tools(self, session: IterativeSession) -> List[ToolSelection]:
        """Select the next tools to use based on current session state"""

        # Build context for tool selection
        context = session.context.copy()
        context["goal"] = session.goal
        context["previous_steps"] = [
            {
                "tool": step.tool_selection.tool.name,
                "result": step.execution_result,
                "analysis": step.analysis_result
            }
            for step in session.steps
        ]

        # If this is the first step, use original input
        if not session.steps:
            return self.tool_selector.select_tools(session.user_input, context)

        # Otherwise, determine next tools based on analysis
        last_step = session.steps[-1]
        if last_step.next_actions:
            # Convert next actions to tool selection request
            next_action_text = " ".join(last_step.next_actions)
            return self.tool_selector.select_tools(next_action_text, context)

        return []

    def _execute_tool(self, step: AnalysisStep) -> Dict[str, Any]:
        """Execute the selected tool"""
        step.status = AnalysisStatus.EXECUTING

        try:
            tool = step.tool_selection.tool
            parameters = step.tool_selection.parameters

            # Execute the tool function
            result = tool.function(**parameters)

            return {
                "success": True,
                "result": result,
                "tool": tool.name,
                "parameters": parameters
            }

        except Exception as e:
            logger.error(f"Error executing tool {step.tool_selection.tool.name}: {e}")
            step.error_message = str(e)
            return {
                "success": False,
                "error": str(e),
                "tool": step.tool_selection.tool.name,
                "parameters": step.tool_selection.parameters
            }

    def _analyze_execution_result(self, session: IterativeSession, step: AnalysisStep) -> Dict[str, Any]:
        """Analyze the execution result and determine what it means"""
        step.status = AnalysisStatus.ANALYZING

        prompt = f"""
        Analyze the execution result of a tool and determine:
        1. Was the execution successful?
        2. What was accomplished?
        3. What issues or errors occurred?
        4. How does this contribute to the overall goal?
        5. What should be done next?

        Goal: {session.goal}
        Tool Used: {step.tool_selection.tool.name}
        Tool Parameters: {json.dumps(step.tool_selection.parameters, indent=2)}
        Execution Result: {json.dumps(step.execution_result, indent=2)}

        Return your analysis as a JSON object:
        {{
            "success": true/false,
            "accomplishments": ["what was achieved"],
            "issues": ["any problems found"],
            "goal_progress": "how this contributes to the goal",
            "recommendations": ["what to do next"]
        }}

        Analysis:"""

        try:
            response = self.gemini_client.generate_response(prompt, temperature=0.3)
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
        except Exception as e:
            logger.error(f"Error analyzing execution result: {e}")

        # Fallback analysis
        analysis_result = {
            "success": step.execution_result.get("success", False),
            "accomplishments": ["Tool executed"],
            "issues": [step.execution_result.get("error", "Unknown error")] if not step.execution_result.get("success") else [],
            "goal_progress": "Step completed",
            "recommendations": ["Continue with next step"]
        }

        # Special handling for code generation: recommend saving the file
        if step.tool_selection.tool.name == "generateCode" and analysis_result["success"]:
            analysis_result["recommendations"].insert(0, "save the generated code to a file (e.g., 'save my_file.html')")
            analysis_result["goal_progress"] = "Code generated, ready for saving."

        return analysis_result

    def _plan_next_actions(self, session: IterativeSession, step: AnalysisStep, analysis: Dict[str, Any]) -> List[str]:
        """Plan the next actions based on analysis results"""

        # Extract recommendations from analysis
        recommendations = analysis.get("recommendations", [])

        # If there are specific recommendations, use them
        if recommendations:
            return recommendations

        # Otherwise, determine next actions based on success/failure
        if analysis.get("success", False):
            return ["continue", "proceed to next step"]
        else:
            return ["retry", "fix errors", "try alternative approach"]

    def _needs_new_tool_selection(self, session: IterativeSession) -> bool:
        """Determine if we need to select new tools"""
        if not session.steps:
            return True

        last_step = session.steps[-1]

        # If last step failed, we might need a different tool
        if last_step.status == AnalysisStatus.FAILED:
            return True

        # If analysis recommends new tools
        if last_step.analysis_result:
            recommendations = last_step.analysis_result.get("recommendations", [])
            if any("new tool" in rec.lower() or "different tool" in rec.lower() for rec in recommendations):
                return True

        return False

    def _is_goal_achieved(self, session: IterativeSession) -> bool:
        """Check if the goal has been achieved"""

        # Check against success criteria
        achieved_criteria = 0
        for criterion in session.success_criteria:
            if self._check_criterion(session, criterion):
                achieved_criteria += 1

        # Goal is achieved if most criteria are met
        return achieved_criteria >= len(session.success_criteria) * 0.8

    def _check_criterion(self, session: IterativeSession, criterion: str) -> bool:
        """Check if a specific success criterion is met"""

        # Simple keyword-based checking (can be enhanced with AI)
        if "error" in criterion.lower():
            # Check if recent steps had no errors
            recent_steps = session.steps[-3:] if len(session.steps) >= 3 else session.steps
            return all(step.execution_result.get("success", False) for step in recent_steps)

        if "complete" in criterion.lower():
            # Check if we have made significant progress
            return len(session.steps) > 0 and session.steps[-1].status == AnalysisStatus.COMPLETED

        # Default: assume criterion is met if we have successful steps
        return any(step.execution_result.get("success", False) for step in session.steps)

    def _compile_final_result(self, session: IterativeSession) -> Dict[str, Any]:
        """Compile the final result of the analysis session"""
        return {
            "goal": session.goal,
            "steps_completed": len(session.steps),
            "success_criteria_met": [
                criterion for criterion in session.success_criteria
                if self._check_criterion(session, criterion)
            ],
            "final_outputs": [
                step.execution_result for step in session.steps
                if step.execution_result and step.execution_result.get("success")
            ],
            "summary": f"Successfully completed {session.goal} in {len(session.steps)} steps"
        }

    def _calculate_progress(self, session: IterativeSession) -> float:
        """Calculate progress percentage for the session"""
        if not session.success_criteria:
            return 0.0

        achieved = sum(1 for criterion in session.success_criteria if self._check_criterion(session, criterion))
        return achieved / len(session.success_criteria)

    def _get_last_result(self, session: IterativeSession) -> Optional[Dict[str, Any]]:
        """Get the result of the last completed step"""
        if session.steps:
            return session.steps[-1].execution_result
        return None
