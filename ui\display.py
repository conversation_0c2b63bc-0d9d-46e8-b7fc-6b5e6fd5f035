"""
Display utilities for WarpAI
"""
import re
from typing import Dict, List, Optional, Any, Union
from rich.console import <PERSON>sole
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.panel import Panel
from rich.text import Text

from warpai.ui.colors import Colors

class Display:
    """Display utilities for WarpAI"""

    def __init__(self):
        """Initialize the display utility"""
        self.console = Console()

    def print(self, text: str, style: Optional[str] = None) -> None:
        """Print text to the console"""
        if style:
            self.console.print(text, style=style)
        else:
            self.console.print(text)

    def print_markdown(self, text: str) -> None:
        """Print markdown text to the console"""
        # Process code blocks with syntax highlighting
        code_block_pattern = r"```(\w+)?\n([\s\S]+?)\n```"

        def replace_code_block(match):
            language = match.group(1) or "text"
            code = match.group(2)

            # Create a syntax highlighted code block
            syntax = Syntax(code, language, theme="monokai", line_numbers=True)

            # Print the syntax highlighted code block
            self.console.print(syntax)

            # Return an empty string to remove the code block from the markdown
            return ""

        # Replace code blocks with syntax highlighted code blocks
        processed_text = re.sub(code_block_pattern, replace_code_block, text)

        # Print the remaining markdown
        if processed_text.strip():
            self.console.print(Markdown(processed_text))

    def print_code(self, code: str, language: str = "text") -> None:
        """Print code with syntax highlighting"""
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        self.console.print(syntax)

    def print_panel(self, text: str, title: Optional[str] = None, style: Optional[str] = None) -> None:
        """Print text in a panel"""
        panel = Panel(text, title=title, style=style)
        self.console.print(panel)

    def print_error(self, text: str) -> None:
        """Print an error message"""
        self.print_panel(text, title="Error", style="red")

    def print_success(self, text: str) -> None:
        """Print a success message"""
        self.print_panel(text, title="Success", style="green")

    def print_warning(self, text: str) -> None:
        """Print a warning message"""
        self.print_panel(text, title="Warning", style="yellow")

    def print_info(self, text: str) -> None:
        """Print an info message"""
        self.print_panel(text, title="Info", style="blue")

    def print_command(self, command: str) -> None:
        """Print a command"""
        self.console.print(f"$ {command}", style="bold cyan")

    def print_command_output(self, output: str) -> None:
        """Print command output"""
        self.console.print(output)

    def print_ai_response(self, response: str) -> None:
        """Print an AI response"""
        # Check if the response contains code blocks
        if "```" in response:
            self.print_markdown(response)
        else:
            self.console.print(response)

    def print_welcome(self) -> None:
        """Print the welcome message"""
        welcome_text = """
╔═══════════════════════════════════════════════════════════════════════════╗
║                                                                           ║
║                            ✨ WarpAI Pro ✨                               ║
║                                                                           ║
║           A powerful full-stack AI coding agent powered by                ║
║                             Gemini AI                                     ║
║                                                                           ║
║  Type your questions or commands in natural language.                     ║
║  Use Alt+A to toggle Agent Mode for natural language processing.          ║
║  Use Ctrl+D to toggle Dispatch Mode for autonomous task execution.        ║
║  Use Ctrl+F to toggle Follow-up Mode for conversation context.            ║
║                                                                           ║
║  Type 'help' for more information or 'exit' to quit.                      ║
║                                                                           ║
╚═══════════════════════════════════════════════════════════════════════════╝
"""
        self.console.print(welcome_text, style="bold blue")

    def print_help(self) -> None:
        """Print the help message"""
        help_text = """
# WarpAI Pro Help

## Modes
- **Agent Mode** - Natural language processing with command suggestions
  - Toggle with `agent` command or `Alt+A`
  - Indicated by ✨ in prompt
- **Dispatch Mode** - Autonomous task planning and execution
  - Toggle with `dispatch` command or `Ctrl+D`
  - Indicated by 🚀 in prompt
- **Follow-up Mode** - Maintain conversation context for follow-up queries
  - Toggle with `follow` command or `Ctrl+F`
  - Indicated by ↳ in prompt

## Basic Commands
- `help` - Show this help message
- `exit` - Exit WarpAI Pro
- `clear` - Clear the screen
- `reset` - Reset the conversation history
- `run` - Execute the last suggested command
- `save [filename]` - Save the last generated code to a file (optional filename)

## Advanced Features
- **Natural Language Detection** - Automatically detect natural language vs. commands
- **Command Safety** - Model-based command safety verification
- **Code Generation** - Ask WarpAI Pro to write code for you
- **Code Execution** - Automatically execute code in multiple languages
- **Auto-Dependencies** - Automatically install missing dependencies
- **Auto-Error Fixing** - Automatically fix common errors
- **Code Explanation** - Ask WarpAI Pro to explain code
- **Web Search** - Ask WarpAI Pro to search the web for information
- **Code Search** - Ask WarpAI Pro to search your codebase
- **Live File Editing** - Use `edit filename.py` to edit files with AI assistance

## Examples
- "write a python function to calculate fibonacci numbers"
- "explain how async/await works in JavaScript"
- "fix this error: TypeError: cannot read property of undefined"
- "search for information about React hooks"
- "find code related to user authentication"
- "create a React component for a login form"
- "design a database schema for a blog"
- "edit app.py" (enters file editing mode)
- "run this code" (executes the last generated code)
- "install numpy and pandas" (installs dependencies)
- "create a new project for a web scraper" (creates a new project)

## Keyboard Shortcuts
- `Alt+A` - Toggle Agent Mode
- `Ctrl+D` - Toggle Dispatch Mode
- `Ctrl+F` - Toggle Follow-up Mode
- `Ctrl+H` - Show keyboard help
- `Esc` - Clear the last command
- `Enter` - Execute the last command (when buffer is empty)
- `Tab` - Auto-complete command
- `↑/↓` - Navigate command history

Type `keys`, `shortcuts`, or `keyboard` to show all keyboard shortcuts.

## Command Execution
- Agent Mode will suggest commands based on your natural language input
- Commands will be executed automatically if Auto-Execute is enabled
- The command output will be displayed in the terminal

## Dispatch Mode
- Dispatch Mode will break down complex tasks into a series of steps
- Each step will have a description and a command to execute
- Steps will be executed automatically if Auto-Execute is enabled
- You can see the plan and execution results in the terminal
"""
        self.print_markdown(help_text)
