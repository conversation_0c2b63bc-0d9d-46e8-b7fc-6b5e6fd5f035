"""
Plugin manager for WarpAI Enhanced
"""
import os
import sys
import importlib
import inspect
import pkgutil
from typing import Dict, List, Any, Callable, Optional, Type
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("plugin_manager")

class Plugin:
    """Base class for plugins"""
    
    # Plugin metadata
    name: str = "base_plugin"
    description: str = "Base plugin class"
    version: str = "0.1.0"
    author: str = "WarpAI Team"
    
    def __init__(self):
        """Initialize the plugin"""
        self.enabled = True
    
    def initialize(self) -> bool:
        """Initialize the plugin"""
        return True
    
    def shutdown(self) -> bool:
        """Shutdown the plugin"""
        return True
    
    def get_commands(self) -> Dict[str, Callable]:
        """Get the commands provided by this plugin"""
        return {}
    
    def get_hooks(self) -> Dict[str, Callable]:
        """Get the hooks provided by this plugin"""
        return {}

class PluginManager:
    """Manager for WarpAI plugins"""
    
    def __init__(self, plugin_dirs: Optional[List[str]] = None):
        """Initialize the plugin manager"""
        self.plugins: Dict[str, Plugin] = {}
        self.commands: Dict[str, Callable] = {}
        self.hooks: Dict[str, List[Callable]] = {}
        
        # Default plugin directories
        if plugin_dirs is None:
            self.plugin_dirs = [
                os.path.join(os.path.dirname(__file__), "builtin"),  # Built-in plugins
                os.path.join(os.path.expanduser("~"), ".warpai", "plugins"),  # User plugins
            ]
        else:
            self.plugin_dirs = plugin_dirs
        
        # Create user plugin directory if it doesn't exist
        os.makedirs(os.path.join(os.path.expanduser("~"), ".warpai", "plugins"), exist_ok=True)
    
    def discover_plugins(self) -> None:
        """Discover plugins in the plugin directories"""
        for plugin_dir in self.plugin_dirs:
            if not os.path.exists(plugin_dir):
                continue
            
            # Add the plugin directory to the path
            if plugin_dir not in sys.path:
                sys.path.insert(0, plugin_dir)
            
            # Discover plugins
            for _, name, is_pkg in pkgutil.iter_modules([plugin_dir]):
                if not is_pkg:
                    continue
                
                try:
                    # Import the plugin module
                    module = importlib.import_module(name)
                    
                    # Find plugin classes
                    for item_name in dir(module):
                        item = getattr(module, item_name)
                        
                        # Check if it's a plugin class
                        if (inspect.isclass(item) and 
                            issubclass(item, Plugin) and 
                            item is not Plugin):
                            
                            # Create an instance of the plugin
                            plugin = item()
                            
                            # Add the plugin to the list
                            self.plugins[plugin.name] = plugin
                            
                            logger.info(f"Discovered plugin: {plugin.name} v{plugin.version} by {plugin.author}")
                except Exception as e:
                    logger.error(f"Error loading plugin {name}: {e}")
    
    def initialize_plugins(self) -> None:
        """Initialize all discovered plugins"""
        for name, plugin in self.plugins.items():
            try:
                if plugin.initialize():
                    logger.info(f"Initialized plugin: {name}")
                    
                    # Register commands
                    for cmd_name, cmd_func in plugin.get_commands().items():
                        self.commands[cmd_name] = cmd_func
                        logger.info(f"Registered command: {cmd_name} from plugin {name}")
                    
                    # Register hooks
                    for hook_name, hook_func in plugin.get_hooks().items():
                        if hook_name not in self.hooks:
                            self.hooks[hook_name] = []
                        self.hooks[hook_name].append(hook_func)
                        logger.info(f"Registered hook: {hook_name} from plugin {name}")
                else:
                    logger.warning(f"Failed to initialize plugin: {name}")
                    plugin.enabled = False
            except Exception as e:
                logger.error(f"Error initializing plugin {name}: {e}")
                plugin.enabled = False
    
    def shutdown_plugins(self) -> None:
        """Shutdown all plugins"""
        for name, plugin in self.plugins.items():
            if plugin.enabled:
                try:
                    if plugin.shutdown():
                        logger.info(f"Shutdown plugin: {name}")
                    else:
                        logger.warning(f"Failed to shutdown plugin: {name}")
                except Exception as e:
                    logger.error(f"Error shutting down plugin {name}: {e}")
    
    def execute_command(self, command: str, *args, **kwargs) -> Any:
        """Execute a command provided by a plugin"""
        if command in self.commands:
            try:
                return self.commands[command](*args, **kwargs)
            except Exception as e:
                logger.error(f"Error executing command {command}: {e}")
                return None
        else:
            logger.warning(f"Command not found: {command}")
            return None
    
    def execute_hook(self, hook_name: str, *args, **kwargs) -> List[Any]:
        """Execute all hooks with the given name"""
        results = []
        
        if hook_name in self.hooks:
            for hook_func in self.hooks[hook_name]:
                try:
                    result = hook_func(*args, **kwargs)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error executing hook {hook_name}: {e}")
        
        return results
    
    def get_plugin(self, name: str) -> Optional[Plugin]:
        """Get a plugin by name"""
        return self.plugins.get(name)
    
    def get_plugins(self) -> Dict[str, Plugin]:
        """Get all plugins"""
        return self.plugins
    
    def get_commands(self) -> Dict[str, Callable]:
        """Get all commands"""
        return self.commands
    
    def get_hooks(self) -> Dict[str, List[Callable]]:
        """Get all hooks"""
        return self.hooks
