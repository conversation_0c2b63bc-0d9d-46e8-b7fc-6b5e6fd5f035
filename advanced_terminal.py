"""
Advanced terminal interface for WarpAI
"""
import os
import sys
from typing import Dict, List, Optional, Any
import platform
import threading
import time

from prompt_toolkit import PromptSession
from prompt_toolkit.history import FileHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import Word<PERSON>ompleter
from prompt_toolkit.styles import Style
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.keys import Keys

from warpai.advanced_agent import AdvancedAgent
from warpai.ui.display import Display
from warpai.config.settings import settings

class AdvancedTerminal:
    """Advanced terminal interface for WarpAI"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None,
                 enable_rag: bool = True, enable_web: bool = True,
                 enable_multi_agent: bool = True, enable_file_watcher: bool = True,
                 enable_plugins: bool = True):
        """Initialize the terminal interface"""
        self.agent = AdvancedAgent(
            api_key=api_key,
            model=model,
            enable_rag=enable_rag,
            enable_web=enable_web,
            enable_multi_agent=enable_multi_agent,
            enable_file_watcher=enable_file_watcher,
            enable_plugins=enable_plugins
        )
        self.display = Display()

        # Set up prompt session
        history_file = os.path.expanduser("~/.warpai_history")
        self.session = PromptSession(
            history=FileHistory(history_file),
            auto_suggest=AutoSuggestFromHistory(),
            style=self._get_prompt_style(),
            key_bindings=self._get_key_bindings(),
        )

        # Set up command completer with enhanced commands
        self.command_completer = WordCompleter([
            # Basic commands
            "help", "exit", "clear", "reset", "index",

            # Enhanced commands
            "edit", "web", "search", "show", "done", "save",

            # Plugin commands
            "plugins", "analyze",

            # New execution commands
            "run", "execute", "r", "install",

            # Package managers
            "pip:", "npm:", "gem:", "apt:", "brew:", "choco:",

            # Shell commands
            "ls", "dir", "cd", "pwd", "echo", "cat", "type",
            "git", "npm", "yarn", "pip", "python", "node",
            "docker", "kubectl", "terraform",
            "aws", "azure", "gcp",
            "vim", "nano", "code",
            "ssh", "scp", "rsync",
            "curl", "wget", "httpie",
            "grep", "sed", "awk",
            "find", "xargs", "sort",
            "ps", "top", "htop",
            "systemctl", "journalctl",
            "ifconfig", "netstat", "ping",
            "tar", "zip", "unzip",
            "chmod", "chown", "sudo",

            # Common packages
            "numpy", "pandas", "matplotlib", "tensorflow", "pytorch", "scikit-learn",
            "requests", "flask", "django", "fastapi", "express", "react", "vue", "angular",
        ])

        # Feature flags
        self.enable_rag = enable_rag
        self.enable_web = enable_web
        self.enable_multi_agent = enable_multi_agent
        self.enable_file_watcher = enable_file_watcher
        self.enable_plugins = enable_plugins

    def _get_prompt_style(self) -> Style:
        """Get the prompt style"""
        return Style.from_dict({
            "prompt": "bold cyan",
        })

    def _get_key_bindings(self) -> KeyBindings:
        """Get key bindings for the prompt"""
        kb = KeyBindings()

        @kb.add(Keys.Enter)
        def _(event):
            """Handle Enter key"""
            # Check if there's a command to execute
            if self.agent.get_last_command():
                # Get the buffer text
                buffer_text = event.current_buffer.text

                # If the buffer is empty, execute the last command
                if not buffer_text.strip():
                    event.current_buffer.text = self.agent.get_last_command()

            # Accept the input
            event.current_buffer.validate_and_handle()

        @kb.add(Keys.Escape)
        def _(event):
            """Handle Escape key"""
            # Clear the last command
            self.agent.clear_last_command()

        return kb

    def run(self) -> None:
        """Run the terminal interface"""
        # Print welcome message with enabled features
        self._print_welcome_with_features()

        # Main loop
        while True:
            try:
                # Get user input
                user_input = self.session.prompt(
                    settings.prompt_prefix,
                    completer=self.command_completer,
                )

                # Check if it's a run command without arguments (to run the last suggested command)
                if user_input.lower() in ["run", "execute", "r"]:
                    last_command = self.agent.get_last_command()
                    if last_command:
                        self.display.print_command(last_command)

                        # Execute the command
                        output, exit_code = self.agent.execute_last_command()

                        # Print the output
                        if exit_code == 0:
                            self.display.print_command_output(output)
                        else:
                            self.display.print_error(output)
                        continue
                    else:
                        self.display.print_error("No command to execute. Generate a command first.")
                        continue

                # Process user input
                response = self.agent.process_input(user_input)

                # Handle special responses
                if response == "exit":
                    break
                elif response == "help":
                    self._print_help_with_features()
                elif response == "clear":
                    os.system("cls" if platform.system() == "Windows" else "clear")
                else:
                    # Print AI response
                    self.display.print_ai_response(f"{settings.response_prefix}{response}")

                    # Check if there's a command to execute
                    last_command = self.agent.get_last_command()
                    if last_command:
                        self.display.print_command(last_command)

                        # Check if auto-execute is enabled
                        if settings.auto_execute_commands:
                            # Auto-execute the command
                            self.display.print_info("Auto-executing command...")
                            output, exit_code = self.agent.execute_last_command()

                            # Print the output
                            if exit_code == 0:
                                self.display.print_command_output(output)
                            else:
                                self.display.print_error(output)

                                # Try to fix the error if auto-fix is enabled
                                if settings.auto_fix_errors:
                                    self.display.print_info("Attempting to fix error and retry...")
                                    fixed_output, fixed_exit_code = self.agent.fix_and_retry_command(last_command, output)

                                    if fixed_exit_code == 0:
                                        self.display.print_info("Command fixed and executed successfully!")
                                        self.display.print_command_output(fixed_output)
                        else:
                            # Ask for confirmation
                            confirm = input("Execute this command? (y/n): ")
                            if confirm.lower() in ["y", "yes"]:
                                # Execute the command
                                output, exit_code = self.agent.execute_last_command()

                                # Print the output
                                if exit_code == 0:
                                    self.display.print_command_output(output)
                                else:
                                    self.display.print_error(output)

            except KeyboardInterrupt:
                # Handle Ctrl+C
                self.display.print("\nUse 'exit' to exit WarpAI.")
            except EOFError:
                # Handle Ctrl+D
                break
            except Exception as e:
                # Handle other exceptions
                self.display.print_error(f"Error: {str(e)}")
                import traceback
                traceback.print_exc()

        # Print goodbye message
        self.display.print("\nGoodbye! 👋")

    def _print_welcome_with_features(self) -> None:
        """Print welcome message with enabled features"""
        self.display.print("\n" + "=" * 60)
        self.display.print("Welcome to WarpAI - Advanced AI CLI Agent", style="bold blue")
        self.display.print("Powered by Gemini AI", style="blue")
        self.display.print("=" * 60)

        # Print enabled features
        self.display.print("\nEnabled Features:", style="bold green")

        features = [
            ("RAG (Code Retrieval)", self.enable_rag),
            ("Web Search", self.enable_web),
            ("Multi-Agent System", self.enable_multi_agent),
            ("File Watcher", self.enable_file_watcher),
            ("Plugin System", self.enable_plugins)
        ]

        for feature, enabled in features:
            status = "✅ Enabled" if enabled else "❌ Disabled"
            style = "green" if enabled else "red"
            self.display.print(f"  • {feature}: {status}", style=style)

        self.display.print("\nType 'help' to see available commands.")
        self.display.print("=" * 60 + "\n")

    def _print_help_with_features(self) -> None:
        """Print help message with enabled features"""
        self.display.print("\n" + "=" * 60)
        self.display.print("WarpAI Help", style="bold blue")
        self.display.print("=" * 60)

        # Basic commands
        self.display.print("\nBasic Commands:", style="bold green")
        self.display.print("  • help - Show this help message")
        self.display.print("  • exit - Exit WarpAI")
        self.display.print("  • clear - Clear the screen")
        self.display.print("  • reset - Reset the conversation history")

        # Enhanced commands
        self.display.print("\nEnhanced Commands:", style="bold green")

        if self.enable_rag:
            self.display.print("  • search <query> - Search the codebase for code")
            self.display.print("  • index - Re-index the codebase")

        if self.enable_web:
            self.display.print("  • web <query> - Search the web for information")

        if self.enable_plugins:
            self.display.print("  • plugins - List available plugins and their commands")
            self.display.print("  • analyze <filename> - Analyze code for quality, security, and performance issues")

        self.display.print("  • edit <filename> - Edit or create a file with AI assistance")
        self.display.print("  • run <filename> - Run a file based on its extension")
        self.display.print("  • run - Execute the last suggested command")
        self.display.print("  • install <package> - Install a package using the appropriate package manager")
        self.display.print("  • save [filename] - Save the last generated code to a file")

        # File execution
        self.display.print("\nFile Execution:", style="bold green")
        self.display.print("WarpAI can run files with these extensions:")
        self.display.print("  • Python (.py), JavaScript (.js), TypeScript (.ts), Shell (.sh), Batch (.bat)")
        self.display.print("  • Java (.java), C (.c), C++ (.cpp), Go (.go), Ruby (.rb), PHP (.php)")

        # Package installation
        self.display.print("\nPackage Installation:", style="bold green")
        self.display.print("WarpAI can install packages using these package managers:")
        self.display.print("  • pip (Python), npm (Node.js), gem (Ruby), apt (Ubuntu/Debian)")
        self.display.print("  • brew (Homebrew), choco (Chocolatey)")
        self.display.print("Examples: 'install numpy', 'install pip:pandas', 'install npm:express'")

        # Natural language examples
        self.display.print("\nNatural Language Examples:", style="bold green")
        self.display.print("  • \"Write a Python function to calculate Fibonacci numbers\"")
        self.display.print("  • \"How do I use async/await in JavaScript?\"")
        self.display.print("  • \"Fix this error: TypeError: cannot read property of undefined\"")
        self.display.print("  • \"Run the file main.py\"")
        self.display.print("  • \"Install the numpy package\"")

        if self.enable_rag:
            self.display.print("  • \"Search for code that handles file operations\"")

        if self.enable_web:
            self.display.print("  • \"Look up how to set up a React project\"")

        if self.enable_plugins:
            self.display.print("  • \"Analyze the code in main.py for issues\"")
            self.display.print("  • \"Check if there are any security vulnerabilities in my code\"")

        self.display.print("  • \"Edit the app.py file\"")

        self.display.print("\nYou can also run any shell command directly.")
        self.display.print("=" * 60 + "\n")

    def _handle_edit_command(self, filename: str) -> None:
        """Handle the edit command for live file editing"""
        filename = filename.strip()

        if not filename:
            self.display.print_error("Please specify a filename to edit.")
            return

        try:
            # Create a simple file with basic content
            self.display.print_info(f"Creating/editing file: {filename}")

            # Determine the language from the filename
            language = self._get_language_from_filename(filename)

            # Create initial content based on the language
            initial_content = self._get_initial_content(filename, language)

            # Write the file directly using file operations
            with open(filename, "w", encoding="utf-8") as f:
                f.write(initial_content)

            # Show the file content
            self.display.print_code(initial_content, language=language)

            # Enter edit mode
            while True:
                try:
                    # Get user input
                    edit_input = input(f"Edit {filename}> ")

                    # Check for special commands
                    if edit_input.lower() == "exit" or edit_input.lower() == "done":
                        break
                    elif edit_input.lower() == "show":
                        # Show the current file content
                        try:
                            with open(filename, "r", encoding="utf-8") as f:
                                current_content = f.read()
                            self.display.print_code(current_content, language=language)
                        except Exception as e:
                            self.display.print_error(f"Failed to read file: {str(e)}")
                    else:
                        # Generate code based on the edit input
                        code_prompt = f"Write {language} code for {filename} that {edit_input}. Return only the code without explanations."
                        code_response = self.agent.process_input(code_prompt)

                        # Extract code from the response
                        import re
                        code_blocks = re.findall(r"```(?:\w+)?\n([\s\S]+?)\n```", code_response)

                        if code_blocks:
                            new_code = code_blocks[0]

                            # Update the file
                            try:
                                with open(filename, "w", encoding="utf-8") as f:
                                    f.write(new_code)
                                print(f"Updated {filename} successfully.")
                                self.display.print_code(new_code, language=language)
                            except Exception as e:
                                self.display.print_error(f"Failed to update {filename}: {str(e)}")
                        else:
                            self.display.print_error("No code was generated. Please try again with a different prompt.")

                except KeyboardInterrupt:
                    # Handle Ctrl+C
                    break
                except Exception as e:
                    self.display.print_error(f"Error: {str(e)}")

            self.display.print_info(f"Finished editing: {filename}")

        except Exception as e:
            self.display.print_error(f"Error editing {filename}: {str(e)}")

    def _get_initial_content(self, filename: str, language: str) -> str:
        """Get initial content for a file based on its language"""
        if language == "python":
            return '"""\n' + filename + '\n"""\n\n# Your code here\n'
        elif language == "javascript" or language == "typescript":
            return '/**\n * ' + filename + '\n */\n\n// Your code here\n'
        elif language == "html":
            return '<!DOCTYPE html>\n<html>\n<head>\n    <title>' + filename + '</title>\n</head>\n<body>\n    <!-- Your content here -->\n</body>\n</html>\n'
        elif language == "css":
            return '/* ' + filename + ' */\n\n/* Your styles here */\n'
        elif language == "java":
            class_name = os.path.splitext(os.path.basename(filename))[0]
            return 'public class ' + class_name + ' {\n    public static void main(String[] args) {\n        // Your code here\n    }\n}\n'
        else:
            return '/* ' + filename + ' */\n\n// Your code here\n'

    def _start_file_watching(self) -> None:
        """Start watching the live file for changes"""
        if self.is_watching_file:
            return

        self.is_watching_file = True
        self.live_file_thread = threading.Thread(target=self._watch_live_file, daemon=True)
        self.live_file_thread.start()

    def _stop_file_watching(self) -> None:
        """Stop watching the live file for changes"""
        self.is_watching_file = False
        if self.live_file_thread:
            self.live_file_thread.join(timeout=1.0)
            self.live_file_thread = None

    def _watch_live_file(self) -> None:
        """Watch the live file for changes"""
        last_content = None

        while self.is_watching_file and self.live_file:
            try:
                # Get the current file content
                current_content = self.agent.get_live_file_content(self.live_file)

                # Check if the content has changed
                if current_content != last_content and current_content is not None:
                    # Update the last content
                    last_content = current_content

                    # Print the updated content
                    self.display.print_info(f"File updated: {self.live_file}")
            except Exception as e:
                print(f"Error watching file: {e}")

            # Sleep for a short time
            time.sleep(1.0)

    def _get_language_from_filename(self, filename: str) -> str:
        """Get the language from a filename"""
        extension = os.path.splitext(filename)[1].lower()

        language_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".html": "html",
            ".css": "css",
            ".java": "java",
            ".c": "c",
            ".cpp": "cpp",
            ".cs": "csharp",
            ".go": "go",
            ".rs": "rust",
            ".rb": "ruby",
            ".php": "php",
            ".swift": "swift",
            ".kt": "kotlin",
            ".sh": "bash",
            ".ps1": "powershell",
            ".sql": "sql",
            ".json": "json",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".xml": "xml",
            ".md": "markdown",
        }

        return language_map.get(extension, "text")

    def clear_screen(self) -> None:
        """Clear the screen"""
        os.system("cls" if platform.system() == "Windows" else "clear")
