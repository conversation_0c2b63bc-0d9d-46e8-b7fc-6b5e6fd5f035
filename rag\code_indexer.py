"""
Code indexing and retrieval for WarpAI
"""
import os
import re
from typing import Dict, List, Optional, Tuple, Union, Any
import hashlib
from pathlib import Path
import json

try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False

class CodeIndexer:
    """Code indexing and retrieval system"""

    def __init__(self, root_dir: str = ".", cache_dir: str = ".warpai_cache"):
        """Initialize the code indexer"""
        self.root_dir = os.path.abspath(root_dir)
        self.cache_dir = os.path.join(self.root_dir, cache_dir)
        self.index = {}
        self.vectors = None
        self.vectorizer = None
        self.file_paths = []

        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)

        # Check if sklearn is available
        if not HAS_SKLEARN:
            print("Warning: scikit-learn is not installed. Using basic search instead.")

    def index_codebase(self, file_extensions: List[str] = None, exclude_dirs: List[str] = None) -> None:
        """Index the codebase"""
        if file_extensions is None:
            file_extensions = [".py", ".js", ".ts", ".html", ".css", ".java", ".c", ".cpp", ".go", ".rs", ".rb", ".php", ".jsx", ".tsx", ".vue", ".svelte", ".dart", ".kt", ".scala", ".groovy", ".pl", ".sh", ".ps1", ".bat", ".cmd", ".sql", ".r", ".m", ".swift", ".f", ".f90", ".hs", ".lua", ".jl", ".clj", ".ex", ".exs", ".erl", ".elm", ".ml", ".fs", ".cs", ".vb", ".pas", ".asm", ".s", ".json", ".xml", ".yaml", ".yml", ".toml", ".ini", ".cfg", ".md", ".rst", ".tex"]

        if exclude_dirs is None:
            exclude_dirs = [".git", "node_modules", "venv", ".venv", "__pycache__", ".pytest_cache", ".warpai_cache"]

        # Walk through the directory and index files
        self.file_paths = []
        self.index = {}

        for root, dirs, files in os.walk(self.root_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in exclude_dirs]

            for file in files:
                # Check if file has a valid extension
                if any(file.endswith(ext) for ext in file_extensions):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.root_dir)

                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()

                        # Add to index
                        self.index[rel_path] = {
                            "content": content,
                            "size": len(content),
                            "path": rel_path,
                            "abs_path": file_path,
                        }

                        self.file_paths.append(rel_path)
                    except Exception as e:
                        print(f"Error indexing {file_path}: {e}")

        # Create vectors for search
        if HAS_SKLEARN:
            self._create_vectors()

        # Save index to cache
        self._save_index()

    def _create_vectors(self) -> None:
        """Create TF-IDF vectors for search"""
        if not HAS_SKLEARN:
            return

        # Extract content from index
        documents = [self.index[path]["content"] for path in self.file_paths]

        # Create TF-IDF vectors
        self.vectorizer = TfidfVectorizer(
            lowercase=True,
            stop_words="english",
            ngram_range=(1, 2),
            max_features=10000,
        )

        self.vectors = self.vectorizer.fit_transform(documents)

    def _save_index(self) -> None:
        """Save index to cache"""
        try:
            # Create a copy of the index to avoid dictionary changed size during iteration
            index_copy = dict(self.index)

            # Create a simplified index without content for saving
            simplified_index = {}
            for path, data in index_copy.items():
                # Calculate hash of content
                content_hash = hashlib.md5(data["content"].encode()).hexdigest()

                simplified_index[path] = {
                    "size": data["size"],
                    "path": data["path"],
                    "abs_path": data["abs_path"],
                    "content_hash": content_hash,
                }

            # Save simplified index
            index_path = os.path.join(self.cache_dir, "index.json")
            with open(index_path, "w", encoding="utf-8") as f:
                json.dump(simplified_index, f)

            # Save file paths (make a copy to avoid concurrent modification)
            file_paths_copy = list(self.file_paths)
            paths_path = os.path.join(self.cache_dir, "paths.json")
            with open(paths_path, "w", encoding="utf-8") as f:
                json.dump(file_paths_copy, f)
        except Exception as e:
            print(f"Error saving index: {e}")

    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search the codebase for relevant files"""
        if not self.index:
            return []

        if HAS_SKLEARN and self.vectors is not None:
            return self._search_with_vectors(query, top_k)
        else:
            return self._search_basic(query, top_k)

    def _search_with_vectors(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Search using TF-IDF vectors"""
        # Transform query to vector
        query_vector = self.vectorizer.transform([query])

        # Calculate similarity
        similarities = cosine_similarity(query_vector, self.vectors).flatten()

        # Get top k results
        top_indices = similarities.argsort()[-top_k:][::-1]

        results = []
        for idx in top_indices:
            path = self.file_paths[idx]
            similarity = similarities[idx]

            if similarity > 0:
                results.append({
                    "path": path,
                    "abs_path": self.index[path]["abs_path"],
                    "similarity": float(similarity),
                    "content": self.index[path]["content"],
                })

        return results

    def _search_basic(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Basic search using string matching"""
        results = []

        # Split query into terms
        terms = query.lower().split()

        for path, data in self.index.items():
            content = data["content"].lower()

            # Calculate a simple score based on term frequency
            score = sum(content.count(term) for term in terms)

            if score > 0:
                results.append({
                    "path": path,
                    "abs_path": data["abs_path"],
                    "similarity": score,
                    "content": data["content"],
                })

        # Sort by score and get top k
        results.sort(key=lambda x: x["similarity"], reverse=True)
        return results[:top_k]

    def get_file_content(self, path: str) -> Optional[str]:
        """Get the content of a file"""
        if path in self.index:
            return self.index[path]["content"]
        return None
