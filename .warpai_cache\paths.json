["advanced_agent.py", "advanced_cli.py", "advanced_terminal.py", "agent.py", "cli.py", "file_watcher.py", "gemini_client.py", "terminal.py", "__init__.py", "agents\\agent_coordinator.py", "agents\\base_agent.py", "agents\\code_explainer.py", "agents\\code_generator.py", "agents\\error_fixer.py", "agents\\__init__.py", "agent_mode\\agent.py", "agent_mode\\autonomy.py", "agent_mode\\conversation.py", "agent_mode\\detector.py", "agent_mode\\dispatch.py", "agent_mode\\__init__.py", "auto_execution\\dependency_manager.py", "auto_execution\\error_handler.py", "auto_execution\\executor.py", "auto_execution\\language_detector.py", "auto_execution\\__init__.py", "config\\enhanced_settings.py", "config\\prompts.py", "config\\settings.py", "config\\__init__.py", "intelligent_tools\\iterative_analyzer.py", "intelligent_tools\\tool_implementations.py", "intelligent_tools\\tool_registry.py", "intelligent_tools\\tool_selector.py", "intelligent_tools\\__init__.py", "models\\model_manager.py", "nlp\\entity_extraction.py", "nlp\\intent_recognition.py", "nlp\\processor.py", "nlp\\semantic_understanding.py", "nlp\\__init__.py", "plugins\\plugin_manager.py", "plugins\\builtin\\code_analyzer\\__init__.py", "plugins\\builtin\\dependency_manager\\__init__.py", "plugins\\builtin\\git_helper\\__init__.py", "rag\\advanced_retriever.py", "rag\\code_indexer.py", "rag\\web_retriever.py", "rag\\__init__.py", "ui\\colors.py", "ui\\display.py", "ui\\keyboard_help.py", "ui\\__init__.py", "utils\\command_executor.py", "utils\\command_parser.py", "utils\\context_manager.py", "utils\\file_operations.py", "utils\\__init__.py"]