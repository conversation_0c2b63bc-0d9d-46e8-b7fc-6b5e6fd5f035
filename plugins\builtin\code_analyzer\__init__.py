"""
Code Analyzer plugin for WarpAI
"""
import os
import re
import ast
import subprocess
from typing import Dict, List, Any, Callable, Optional, Tuple, Set

from warpai.plugins.plugin_manager import Plugin

class CodeAnalyzerPlugin(Plugin):
    """Code Analyzer plugin for WarpAI"""
    
    # Plugin metadata
    name = "code_analyzer"
    description = "Analyzes code for quality, security, and performance issues"
    version = "1.0.0"
    author = "WarpAI Team"
    
    def __init__(self):
        """Initialize the plugin"""
        super().__init__()
        self.analyzers = {
            "python": self._analyze_python,
            "javascript": self._analyze_javascript,
            "typescript": self._analyze_typescript,
            "html": self._analyze_html,
            "css": self._analyze_css,
        }
        
        # File extension to language mapping
        self.extension_map = {
            ".py": "python",
            ".js": "javascript",
            ".jsx": "javascript",
            ".ts": "typescript",
            ".tsx": "typescript",
            ".html": "html",
            ".htm": "html",
            ".css": "css",
            ".scss": "css",
            ".sass": "css",
        }
    
    def initialize(self) -> bool:
        """Initialize the plugin"""
        return True
    
    def get_commands(self) -> Dict[str, Callable]:
        """Get the commands provided by this plugin"""
        return {
            "analyze_code": self.analyze_code,
            "analyze_file": self.analyze_file,
            "analyze_directory": self.analyze_directory,
        }
    
    def get_hooks(self) -> Dict[str, Callable]:
        """Get the hooks provided by this plugin"""
        return {
            "after_save_file": self.after_save_file,
        }
    
    def analyze_code(self, code: str, language: str) -> str:
        """
        Analyze code for quality, security, and performance issues
        
        Args:
            code: The code to analyze
            language: The programming language
            
        Returns:
            Analysis results
        """
        if language not in self.analyzers:
            return f"Unsupported language: {language}"
            
        # Analyze the code
        issues = self.analyzers[language](code)
        
        # Format the results
        if not issues:
            return "No issues found."
            
        return "Issues found:\n" + "\n".join([f"- {issue}" for issue in issues])
    
    def analyze_file(self, file_path: str) -> str:
        """
        Analyze a file for quality, security, and performance issues
        
        Args:
            file_path: The file path
            
        Returns:
            Analysis results
        """
        if not os.path.exists(file_path):
            return f"File not found: {file_path}"
            
        # Get the file extension
        _, ext = os.path.splitext(file_path)
        if ext not in self.extension_map:
            return f"Unsupported file type: {ext}"
            
        # Get the language
        language = self.extension_map[ext]
        
        # Read the file
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                code = f.read()
        except Exception as e:
            return f"Error reading file: {e}"
            
        # Analyze the code
        return self.analyze_code(code, language)
    
    def analyze_directory(self, directory: str) -> str:
        """
        Analyze all supported files in a directory
        
        Args:
            directory: The directory path
            
        Returns:
            Analysis results
        """
        if not os.path.exists(directory):
            return f"Directory not found: {directory}"
            
        # Get all files in the directory
        results = []
        for root, _, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                _, ext = os.path.splitext(file_path)
                if ext in self.extension_map:
                    # Analyze the file
                    result = self.analyze_file(file_path)
                    if "Issues found:" in result:
                        results.append(f"File: {file_path}\n{result}")
        
        # Format the results
        if not results:
            return "No issues found in any files."
            
        return "\n\n".join(results)
    
    def after_save_file(self, file_path: str) -> None:
        """
        Hook that runs after a file is saved
        
        Args:
            file_path: The file path
        """
        # Check if the file is supported
        _, ext = os.path.splitext(file_path)
        if ext in self.extension_map:
            # Analyze the file
            result = self.analyze_file(file_path)
            if "Issues found:" in result:
                print(f"\nCode analysis for {file_path}:\n{result}")
    
    def _analyze_python(self, code: str) -> List[str]:
        """
        Analyze Python code
        
        Args:
            code: The code to analyze
            
        Returns:
            List of issues
        """
        issues = []
        
        # Check for syntax errors
        try:
            tree = ast.parse(code)
        except SyntaxError as e:
            issues.append(f"Syntax error: {e}")
            return issues
        
        # Check for common issues
        
        # 1. Long lines
        lines = code.split("\n")
        for i, line in enumerate(lines):
            if len(line) > 100:
                issues.append(f"Line {i+1} is too long ({len(line)} characters)")
        
        # 2. Too many nested blocks
        class BlockVisitor(ast.NodeVisitor):
            def __init__(self):
                self.max_depth = 0
                self.current_depth = 0
            
            def generic_visit(self, node):
                if isinstance(node, (ast.For, ast.While, ast.If, ast.With, ast.Try, ast.FunctionDef, ast.ClassDef)):
                    self.current_depth += 1
                    if self.current_depth > self.max_depth:
                        self.max_depth = self.current_depth
                    super().generic_visit(node)
                    self.current_depth -= 1
                else:
                    super().generic_visit(node)
        
        visitor = BlockVisitor()
        visitor.visit(tree)
        if visitor.max_depth > 5:
            issues.append(f"Code has too many nested blocks (depth: {visitor.max_depth})")
        
        # 3. Too many function arguments
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                args_count = len(node.args.args)
                if args_count > 5:
                    issues.append(f"Function '{node.name}' has too many arguments ({args_count})")
        
        # 4. Unused imports
        imported = set()
        used = set()
        
        class ImportVisitor(ast.NodeVisitor):
            def visit_Import(self, node):
                for name in node.names:
                    imported.add(name.name)
                self.generic_visit(node)
            
            def visit_ImportFrom(self, node):
                for name in node.names:
                    if name.name == "*":
                        continue
                    if node.module:
                        imported.add(f"{node.module}.{name.name}")
                    else:
                        imported.add(name.name)
                self.generic_visit(node)
        
        class NameVisitor(ast.NodeVisitor):
            def visit_Name(self, node):
                used.add(node.id)
                self.generic_visit(node)
            
            def visit_Attribute(self, node):
                if isinstance(node.value, ast.Name):
                    used.add(f"{node.value.id}.{node.attr}")
                self.generic_visit(node)
        
        ImportVisitor().visit(tree)
        NameVisitor().visit(tree)
        
        for imp in imported:
            if "." in imp:
                base = imp.split(".")[0]
                if base not in used and imp not in used:
                    issues.append(f"Unused import: {imp}")
            elif imp not in used:
                issues.append(f"Unused import: {imp}")
        
        return issues
    
    def _analyze_javascript(self, code: str) -> List[str]:
        """
        Analyze JavaScript code
        
        Args:
            code: The code to analyze
            
        Returns:
            List of issues
        """
        issues = []
        
        # Check for common issues
        
        # 1. Long lines
        lines = code.split("\n")
        for i, line in enumerate(lines):
            if len(line) > 100:
                issues.append(f"Line {i+1} is too long ({len(line)} characters)")
        
        # 2. Console.log statements
        console_log_pattern = r"console\.log\("
        matches = re.finditer(console_log_pattern, code)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: Console.log statement found")
        
        # 3. Alert statements
        alert_pattern = r"alert\("
        matches = re.finditer(alert_pattern, code)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: Alert statement found")
        
        # 4. Eval usage
        eval_pattern = r"eval\("
        matches = re.finditer(eval_pattern, code)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: Eval usage found (security risk)")
        
        return issues
    
    def _analyze_typescript(self, code: str) -> List[str]:
        """
        Analyze TypeScript code
        
        Args:
            code: The code to analyze
            
        Returns:
            List of issues
        """
        # TypeScript analysis includes JavaScript analysis
        issues = self._analyze_javascript(code)
        
        # Additional TypeScript-specific checks
        
        # 1. Any type usage
        any_type_pattern = r":\s*any"
        matches = re.finditer(any_type_pattern, code)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: 'any' type used (consider using a more specific type)")
        
        return issues
    
    def _analyze_html(self, code: str) -> List[str]:
        """
        Analyze HTML code
        
        Args:
            code: The code to analyze
            
        Returns:
            List of issues
        """
        issues = []
        
        # Check for common issues
        
        # 1. Missing doctype
        if "<!DOCTYPE" not in code:
            issues.append("Missing DOCTYPE declaration")
        
        # 2. Missing alt attribute in img tags
        img_pattern = r"<img[^>]*?(?:alt=(['\"])([^'\"]*)\1)?[^>]*?>"
        matches = re.finditer(img_pattern, code, re.IGNORECASE)
        for match in matches:
            if not match.group(2):
                line_no = code[:match.start()].count("\n") + 1
                issues.append(f"Line {line_no}: Image missing alt attribute")
        
        # 3. Inline styles
        inline_style_pattern = r"<[^>]*?style=['\"][^'\"]*['\"][^>]*?>"
        matches = re.finditer(inline_style_pattern, code, re.IGNORECASE)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: Inline style used (consider using CSS classes)")
        
        return issues
    
    def _analyze_css(self, code: str) -> List[str]:
        """
        Analyze CSS code
        
        Args:
            code: The code to analyze
            
        Returns:
            List of issues
        """
        issues = []
        
        # Check for common issues
        
        # 1. !important usage
        important_pattern = r"!important"
        matches = re.finditer(important_pattern, code)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: !important used (consider using more specific selectors)")
        
        # 2. Too specific selectors
        specific_selector_pattern = r"[.#][a-zA-Z0-9_-]+(?:\s+[.#][a-zA-Z0-9_-]+){3,}"
        matches = re.finditer(specific_selector_pattern, code)
        for match in matches:
            line_no = code[:match.start()].count("\n") + 1
            issues.append(f"Line {line_no}: Overly specific selector (consider simplifying)")
        
        return issues
