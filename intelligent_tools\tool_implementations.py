"""
Tool Implementations for WarpAI Intelligent Tools

This module provides actual implementations for all the tools in the registry.
"""

import os
import re
import json
import subprocess
import shutil
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

try:
    import requests
except ImportError:
    requests = None

from warpai.utils.command_executor import CommandExecutor
from warpai.utils.file_operations import FileOperations
from warpai.gemini_client import GeminiClient
from warpai.config.settings import settings

logger = logging.getLogger(__name__)

class ToolImplementations:
    """Actual implementations for all tools in the registry"""

    def __init__(self, api_key: str = None, model: str = None):
        """Initialize tool implementations"""
        self.command_executor = CommandExecutor()
        self.file_operations = FileOperations()
        self.gemini_client = GeminiClient(api_key=api_key, model=model or settings.model)

    # Shell Operations
    def execute_shell_command(self, command: str, working_dir: str = None) -> Dict[str, Any]:
        """Execute shell command"""
        try:
            if working_dir:
                original_dir = os.getcwd()
                os.chdir(working_dir)

            output, exit_code = self.command_executor.execute_command(command)

            if working_dir:
                os.chdir(original_dir)

            return {
                "success": exit_code == 0,
                "output": output,
                "exit_code": exit_code,
                "command": command
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": command
            }

    # File Operations
    def read_file(self, file_path: str, encoding: str = "utf-8") -> Dict[str, Any]:
        """Read file content"""
        try:
            content, status = self.file_operations.read_file(file_path)
            return {
                "success": status == 0,
                "content": content if status == 0 else "",
                "error": content if status != 0 else "",
                "file_path": file_path
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }

    def save_file(self, file_path: str, content: str, encoding: str = "utf-8") -> Dict[str, Any]:
        """Save file content"""
        try:
            result, status = self.file_operations.write_file(file_path, content)
            return {
                "success": status == 0,
                "message": result if status == 0 else "",
                "error": result if status != 0 else "",
                "file_path": file_path
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }

    def edit_file(self, file_path: str, changes: dict) -> Dict[str, Any]:
        """Edit file content"""
        try:
            # Read current content
            current_content, status = self.file_operations.read_file(file_path)
            if status != 0:
                return {
                    "success": False,
                    "error": f"Could not read file: {current_content}",
                    "file_path": file_path
                }

            # Apply changes based on change type
            if "line_number" in changes and "new_content" in changes:
                # Replace specific line
                lines = current_content.split('\n')
                line_num = changes["line_number"] - 1  # Convert to 0-based
                if 0 <= line_num < len(lines):
                    lines[line_num] = changes["new_content"]
                    new_content = '\n'.join(lines)
                else:
                    return {
                        "success": False,
                        "error": f"Line number {changes['line_number']} out of range",
                        "file_path": file_path
                    }
            elif "search" in changes and "replace" in changes:
                # Search and replace
                new_content = current_content.replace(changes["search"], changes["replace"])
            else:
                # Full content replacement
                new_content = changes.get("content", current_content)

            # Save the modified content
            result, status = self.file_operations.write_file(file_path, new_content)
            return {
                "success": status == 0,
                "message": result if status == 0 else "",
                "error": result if status != 0 else "",
                "file_path": file_path
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }

    def remove_files(self, paths: list) -> Dict[str, Any]:
        """Remove files or directories"""
        try:
            removed_files = []
            errors = []

            for path in paths:
                try:
                    if os.path.isfile(path):
                        os.remove(path)
                        removed_files.append(path)
                    elif os.path.isdir(path):
                        shutil.rmtree(path)
                        removed_files.append(path)
                    else:
                        errors.append(f"Path not found: {path}")
                except Exception as e:
                    errors.append(f"Error removing {path}: {str(e)}")

            return {
                "success": len(errors) == 0,
                "removed_files": removed_files,
                "errors": errors
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "paths": paths
            }

    # Web Operations
    def web_fetch(self, url: str, headers: dict = None) -> Dict[str, Any]:
        """Fetch content from web URLs"""
        try:
            if requests is None:
                return {
                    "success": False,
                    "error": "requests library not available. Install with: pip install requests",
                    "url": url
                }

            if headers is None:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }

            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            return {
                "success": True,
                "content": response.text,
                "status_code": response.status_code,
                "url": url
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }

    # Code Analysis Tools
    def analyze_code(self, code: str, language: str, analysis_type: str = "full") -> Dict[str, Any]:
        """Analyze code for issues, patterns, and improvements"""
        try:
            prompt = f"""
Analyze the following {language} code and provide insights:

Code:
```{language}
{code}
```

Analysis Type: {analysis_type}

Please provide:
1. Code quality assessment
2. Potential issues or bugs
3. Performance considerations
4. Best practice recommendations
5. Security concerns (if any)

Return your analysis in a structured format.
"""

            analysis = self.gemini_client.generate_response(
                prompt=prompt,
                system_prompt="You are an expert code reviewer. Provide detailed, actionable feedback."
            )

            return {
                "success": True,
                "analysis": analysis,
                "language": language,
                "analysis_type": analysis_type
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "language": language
            }

    def debug_code(self, code: str, error_message: str, language: str) -> Dict[str, Any]:
        """Debug code and find issues"""
        try:
            prompt = f"""
Debug the following {language} code that is producing an error:

Code:
```{language}
{code}
```

Error Message:
{error_message}

Please:
1. Identify the root cause of the error
2. Provide a fixed version of the code
3. Explain what was wrong and why the fix works
4. Suggest ways to prevent similar issues

Return the fixed code and explanation.
"""

            debug_result = self.gemini_client.generate_response(
                prompt=prompt,
                system_prompt="You are an expert debugger. Provide clear explanations and working fixes."
            )

            return {
                "success": True,
                "debug_result": debug_result,
                "language": language,
                "original_error": error_message
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "language": language
            }

    # Code Generation Tools
    def generate_code(self, requirements: str, language: str, style: str = "clean") -> Dict[str, Any]:
        """Generate code based on requirements"""
        try:
            prompt = f"""
Generate {language} code based on the following requirements:

Requirements: {requirements}

Style: {style}

Please provide:
1. Clean, well-commented code
2. Proper error handling
3. Best practices for {language}
4. Documentation/comments explaining the code

Code:
"""

            generated_code = self.gemini_client.generate_response(
                prompt=prompt,
                system_prompt="You are an expert programmer. Generate production-ready, well-documented code."
            )

            return {
                "success": True,
                "code": generated_code,
                "language": language,
                "requirements": requirements
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "language": language
            }

    def generate_tests(self, code: str, language: str, test_framework: str = "auto") -> Dict[str, Any]:
        """Generate unit tests for code"""
        try:
            # Auto-detect test framework if needed
            if test_framework == "auto":
                framework_map = {
                    "python": "pytest",
                    "javascript": "jest",
                    "java": "junit",
                    "csharp": "nunit",
                    "go": "testing"
                }
                test_framework = framework_map.get(language.lower(), "unittest")

            prompt = f"""
Generate comprehensive unit tests for the following {language} code using {test_framework}:

Code to test:
```{language}
{code}
```

Please provide:
1. Complete test suite with multiple test cases
2. Edge case testing
3. Error condition testing
4. Proper test structure and naming
5. Setup and teardown if needed

Test Framework: {test_framework}

Tests:
"""

            test_code = self.gemini_client.generate_response(
                prompt=prompt,
                system_prompt="You are an expert in test-driven development. Generate comprehensive, maintainable tests."
            )

            return {
                "success": True,
                "test_code": test_code,
                "language": language,
                "test_framework": test_framework
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "language": language
            }

    # Package Management
    def install_package(self, package_name: str, package_manager: str = "auto", version: str = "latest") -> Dict[str, Any]:
        """Install packages and dependencies"""
        try:
            # Auto-detect package manager if needed
            if package_manager == "auto":
                if os.path.exists("package.json"):
                    package_manager = "npm"
                elif os.path.exists("requirements.txt") or os.path.exists("pyproject.toml"):
                    package_manager = "pip"
                elif os.path.exists("Cargo.toml"):
                    package_manager = "cargo"
                elif os.path.exists("go.mod"):
                    package_manager = "go"
                else:
                    package_manager = "pip"  # Default

            # Build install command
            commands = {
                "pip": f"pip install {package_name}" + (f"=={version}" if version != "latest" else ""),
                "npm": f"npm install {package_name}" + (f"@{version}" if version != "latest" else ""),
                "yarn": f"yarn add {package_name}" + (f"@{version}" if version != "latest" else ""),
                "cargo": f"cargo add {package_name}" + (f"@{version}" if version != "latest" else ""),
                "go": f"go get {package_name}" + (f"@{version}" if version != "latest" else "")
            }

            command = commands.get(package_manager, f"pip install {package_name}")

            # Execute the install command
            output, exit_code = self.command_executor.execute_command(command)

            return {
                "success": exit_code == 0,
                "output": output,
                "package_name": package_name,
                "package_manager": package_manager,
                "version": version
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "package_name": package_name
            }

    def list_packages(self, package_manager: str = "auto") -> Dict[str, Any]:
        """List installed packages"""
        try:
            # Auto-detect package manager if needed
            if package_manager == "auto":
                if os.path.exists("package.json"):
                    package_manager = "npm"
                elif os.path.exists("requirements.txt"):
                    package_manager = "pip"
                else:
                    package_manager = "pip"  # Default

            # Build list command
            commands = {
                "pip": "pip list",
                "npm": "npm list --depth=0",
                "yarn": "yarn list --depth=0",
                "cargo": "cargo tree --depth=1"
            }

            command = commands.get(package_manager, "pip list")

            # Execute the list command
            output, exit_code = self.command_executor.execute_command(command)

            return {
                "success": exit_code == 0,
                "packages": output,
                "package_manager": package_manager
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "package_manager": package_manager
            }

    # Git Operations
    def git_status(self, repo_path: str = ".") -> Dict[str, Any]:
        """Check git repository status"""
        try:
            original_dir = os.getcwd()
            if repo_path != ".":
                os.chdir(repo_path)

            output, exit_code = self.command_executor.execute_command("git status --porcelain")

            if repo_path != ".":
                os.chdir(original_dir)

            return {
                "success": exit_code == 0,
                "status": output,
                "repo_path": repo_path,
                "has_changes": bool(output.strip())
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "repo_path": repo_path
            }

    # System Operations
    def show_directory(self, path: str = ".", recursive: bool = False) -> Dict[str, Any]:
        """Show directory contents"""
        try:
            if not os.path.exists(path):
                return {
                    "success": False,
                    "error": f"Path does not exist: {path}",
                    "path": path
                }

            if recursive:
                items = []
                for root, dirs, files in os.walk(path):
                    level = root.replace(path, '').count(os.sep)
                    indent = ' ' * 2 * level
                    items.append(f"{indent}{os.path.basename(root)}/")
                    subindent = ' ' * 2 * (level + 1)
                    for file in files:
                        items.append(f"{subindent}{file}")
                content = '\n'.join(items)
            else:
                items = os.listdir(path)
                items.sort()
                content = '\n'.join(items)

            return {
                "success": True,
                "content": content,
                "path": path,
                "recursive": recursive
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "path": path
            }

    def create_folder(self, path: str, recursive: bool = True) -> Dict[str, Any]:
        """Create new directories"""
        try:
            if recursive:
                os.makedirs(path, exist_ok=True)
            else:
                os.mkdir(path)

            return {
                "success": True,
                "message": f"Directory created: {path}",
                "path": path
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "path": path
            }

    # AI Operations
    def remember(self, information: str, category: str = "general") -> Dict[str, Any]:
        """Store information in memory for future reference"""
        try:
            # For now, just return success - can be enhanced with actual memory storage
            return {
                "success": True,
                "message": f"Remembered: {information[:50]}..." if len(information) > 50 else f"Remembered: {information}",
                "category": category
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "information": information
            }
