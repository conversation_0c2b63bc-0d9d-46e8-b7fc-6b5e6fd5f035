"""
Model manager for WarpAI - Manages multiple AI model providers
"""
from typing import Dict, List, Optional, Any, Union, Type
import os
import json
import time
import logging
from pathlib import Path
import importlib

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseModelClient:
    """Base class for all model clients"""
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """Initialize the base model client"""
        self.api_key = api_key
        self.model_name = model
        self.provider_name = "base"
        self.available_models = []
        self.history = []
    
    def generate_response(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate a response from the model"""
        raise NotImplementedError("Subclasses must implement this method")
    
    def generate_streaming_response(self, prompt: str, system_prompt: Optional[str] = None) -> Any:
        """Generate a streaming response from the model"""
        raise NotImplementedError("Subclasses must implement this method")
    
    def reset_chat(self) -> None:
        """Reset the chat history"""
        self.history = []
    
    def add_to_history(self, role: str, content: str) -> None:
        """Add a message to the chat history"""
        self.history.append({"role": role, "content": content})
    
    def get_available_models(self) -> List[str]:
        """Get the list of available models"""
        return self.available_models

class ModelManager:
    """Manager for multiple AI model providers"""
    
    def __init__(self):
        """Initialize the model manager"""
        self.clients: Dict[str, BaseModelClient] = {}
        self.default_provider = "gemini"
        self.fallback_order = ["gemini", "openai", "anthropic"]
        self.config_dir = os.path.join(os.path.expanduser("~"), ".warpai")
        self.config_path = os.path.join(self.config_dir, "models_config.json")
        
        # Create config directory if it doesn't exist
        os.makedirs(self.config_dir, exist_ok=True)
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize clients
        self._initialize_clients()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading model configuration: {e}")
        
        # Return default configuration
        return {
            "default_provider": "gemini",
            "fallback_order": ["gemini", "openai", "anthropic"],
            "providers": {
                "gemini": {
                    "enabled": True,
                    "api_key_env": "GEMINI_API_KEY",
                    "default_model": "gemini-2.0-flash",
                    "temperature": 0.7,
                    "max_tokens": 8192
                },
                "openai": {
                    "enabled": False,
                    "api_key_env": "OPENAI_API_KEY",
                    "default_model": "gpt-4o",
                    "temperature": 0.7,
                    "max_tokens": 4096
                },
                "anthropic": {
                    "enabled": False,
                    "api_key_env": "ANTHROPIC_API_KEY",
                    "default_model": "claude-3-opus-20240229",
                    "temperature": 0.7,
                    "max_tokens": 4096
                }
            }
        }
    
    def _save_config(self) -> None:
        """Save configuration to file"""
        try:
            with open(self.config_path, "w") as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving model configuration: {e}")
    
    def _initialize_clients(self) -> None:
        """Initialize model clients"""
        for provider, config in self.config["providers"].items():
            if config["enabled"]:
                try:
                    # Get API key from environment variable
                    api_key = os.getenv(config["api_key_env"])
                    
                    if not api_key:
                        logger.warning(f"API key for {provider} not found in environment variable {config['api_key_env']}")
                        continue
                    
                    # Import the client class
                    try:
                        if provider == "gemini":
                            from warpai.gemini_client import GeminiClient
                            client_class = GeminiClient
                        else:
                            module = importlib.import_module(f"warpai.models.{provider}_client")
                            client_class = getattr(module, f"{provider.capitalize()}Client")
                    except ImportError:
                        logger.warning(f"Client module for {provider} not found")
                        continue
                    
                    # Initialize the client
                    self.clients[provider] = client_class(
                        api_key=api_key,
                        model=config["default_model"]
                    )
                    
                    logger.info(f"Initialized {provider} client with model {config['default_model']}")
                except Exception as e:
                    logger.error(f"Error initializing {provider} client: {e}")
    
    def get_client(self, provider: Optional[str] = None) -> Optional[BaseModelClient]:
        """Get a model client by provider name"""
        if provider is None:
            provider = self.default_provider
        
        return self.clients.get(provider)
    
    def generate_response(self, prompt: str, system_prompt: Optional[str] = None, provider: Optional[str] = None) -> str:
        """Generate a response using the specified provider with fallback"""
        if provider is None:
            provider = self.default_provider
        
        # Try the specified provider first
        if provider in self.clients:
            try:
                return self.clients[provider].generate_response(prompt, system_prompt)
            except Exception as e:
                logger.error(f"Error generating response with {provider}: {e}")
        
        # Try fallback providers
        for fallback_provider in self.fallback_order:
            if fallback_provider != provider and fallback_provider in self.clients:
                try:
                    logger.info(f"Falling back to {fallback_provider}")
                    return self.clients[fallback_provider].generate_response(prompt, system_prompt)
                except Exception as e:
                    logger.error(f"Error generating response with fallback {fallback_provider}: {e}")
        
        return f"Error: Failed to generate response with all available providers"
    
    def generate_streaming_response(self, prompt: str, system_prompt: Optional[str] = None, provider: Optional[str] = None) -> Any:
        """Generate a streaming response using the specified provider with fallback"""
        if provider is None:
            provider = self.default_provider
        
        # Try the specified provider first
        if provider in self.clients:
            try:
                return self.clients[provider].generate_streaming_response(prompt, system_prompt)
            except Exception as e:
                logger.error(f"Error generating streaming response with {provider}: {e}")
        
        # Try fallback providers
        for fallback_provider in self.fallback_order:
            if fallback_provider != provider and fallback_provider in self.clients:
                try:
                    logger.info(f"Falling back to {fallback_provider}")
                    return self.clients[fallback_provider].generate_streaming_response(prompt, system_prompt)
                except Exception as e:
                    logger.error(f"Error generating streaming response with fallback {fallback_provider}: {e}")
        
        return None
    
    def add_provider(self, provider: str, api_key: str, default_model: str) -> bool:
        """Add a new provider configuration"""
        if provider not in self.config["providers"]:
            self.config["providers"][provider] = {
                "enabled": True,
                "api_key_env": f"{provider.upper()}_API_KEY",
                "default_model": default_model,
                "temperature": 0.7,
                "max_tokens": 4096
            }
            
            # Set environment variable
            os.environ[f"{provider.upper()}_API_KEY"] = api_key
            
            # Save configuration
            self._save_config()
            
            # Re-initialize clients
            self._initialize_clients()
            
            return True
        
        return False
    
    def enable_provider(self, provider: str) -> bool:
        """Enable a provider"""
        if provider in self.config["providers"]:
            self.config["providers"][provider]["enabled"] = True
            self._save_config()
            self._initialize_clients()
            return True
        
        return False
    
    def disable_provider(self, provider: str) -> bool:
        """Disable a provider"""
        if provider in self.config["providers"]:
            self.config["providers"][provider]["enabled"] = False
            self._save_config()
            
            # Remove client
            if provider in self.clients:
                del self.clients[provider]
            
            return True
        
        return False
    
    def set_default_provider(self, provider: str) -> bool:
        """Set the default provider"""
        if provider in self.clients:
            self.default_provider = provider
            self.config["default_provider"] = provider
            self._save_config()
            return True
        
        return False
    
    def get_available_providers(self) -> List[str]:
        """Get the list of available providers"""
        return list(self.clients.keys())
    
    def get_all_available_models(self) -> Dict[str, List[str]]:
        """Get all available models from all providers"""
        models = {}
        
        for provider, client in self.clients.items():
            models[provider] = client.get_available_models()
        
        return models
