"""
Git Helper plugin for WarpAI Enhanced
"""
import os
import subprocess
from typing import Dict, List, Any, Callable, Optional, Tuple

from warpai.plugins.plugin_manager import Plugin

class GitHelperPlugin(Plugin):
    """Git Helper plugin for WarpAI Enhanced"""
    
    # Plugin metadata
    name = "git_helper"
    description = "Git helper plugin for WarpAI Enhanced"
    version = "1.0.0"
    author = "WarpAI Team"
    
    def __init__(self):
        """Initialize the plugin"""
        super().__init__()
        self.git_available = self._check_git_available()
    
    def _check_git_available(self) -> bool:
        """Check if git is available"""
        try:
            subprocess.run(["git", "--version"], capture_output=True, check=True)
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            return False
    
    def initialize(self) -> bool:
        """Initialize the plugin"""
        if not self.git_available:
            print("Git is not available. Git Helper plugin will be disabled.")
            return False
        return True
    
    def get_commands(self) -> Dict[str, Callable]:
        """Get the commands provided by this plugin"""
        return {
            "git_status": self.git_status,
            "git_log": self.git_log,
            "git_branch": self.git_branch,
            "git_diff": self.git_diff,
            "git_commit": self.git_commit,
        }
    
    def get_hooks(self) -> Dict[str, Callable]:
        """Get the hooks provided by this plugin"""
        return {
            "before_prompt": self.before_prompt,
            "after_response": self.after_response,
        }
    
    def before_prompt(self) -> None:
        """Hook that runs before the prompt is displayed"""
        # Check if we're in a git repository
        if self._is_git_repo():
            # Get the current branch
            branch = self._get_current_branch()
            if branch:
                print(f"Git branch: {branch}")
    
    def after_response(self, response: str) -> str:
        """Hook that runs after a response is generated"""
        # Check if the response mentions git
        if "git" in response.lower():
            # Add a note about the git helper commands
            return response + "\n\nNote: You can use the following git helper commands:\n" + \
                "- git_status: Show the git status\n" + \
                "- git_log: Show the git log\n" + \
                "- git_branch: Show the git branches\n" + \
                "- git_diff: Show the git diff\n" + \
                "- git_commit: Commit changes"
        return response
    
    def _is_git_repo(self, repo_path: Optional[str] = None) -> bool:
        """Check if the specified directory is a git repository"""
        try:
            subprocess.run(["git", "rev-parse", "--is-inside-work-tree"],
                          capture_output=True, check=True, cwd=repo_path)
            return True
        except subprocess.SubprocessError:
            return False
    
    def _get_current_branch(self, repo_path: Optional[str] = None) -> Optional[str]:
        """Get the current git branch for the specified repository"""
        try:
            result = subprocess.run(["git", "branch", "--show-current"],
                                   capture_output=True, check=True, text=True, cwd=repo_path)
            return result.stdout.strip()
        except subprocess.SubprocessError:
            return None
    
    def git_status(self, repo_path: Optional[str] = None) -> str:
        """Show the git status for the specified repository"""
        if not self._is_git_repo(repo_path):
            return "Not a git repository"
        
        try:
            result = subprocess.run(["git", "status"],
                                   capture_output=True, check=True, text=True, cwd=repo_path)
            return result.stdout
        except subprocess.SubprocessError as e:
            return f"Error: {e}"
    
    def git_log(self, n: int = 5, repo_path: Optional[str] = None) -> str:
        """Show the git log for the specified repository"""
        if not self._is_git_repo(repo_path):
            return "Not a git repository"
        
        try:
            result = subprocess.run(["git", "log", f"-n{n}", "--oneline"],
                                   capture_output=True, check=True, text=True, cwd=repo_path)
            return result.stdout
        except subprocess.SubprocessError as e:
            return f"Error: {e}"
    
    def git_branch(self, repo_path: Optional[str] = None) -> str:
        """Show the git branches for the specified repository"""
        if not self._is_git_repo(repo_path):
            return "Not a git repository"
        
        try:
            result = subprocess.run(["git", "branch"],
                                   capture_output=True, check=True, text=True, cwd=repo_path)
            return result.stdout
        except subprocess.SubprocessError as e:
            return f"Error: {e}"
    
    def git_diff(self, file: Optional[str] = None, repo_path: Optional[str] = None) -> str:
        """Show the git diff for the specified repository"""
        if not self._is_git_repo(repo_path):
            return "Not a git repository"
        
        try:
            if file:
                result = subprocess.run(["git", "diff", file],
                                       capture_output=True, check=True, text=True, cwd=repo_path)
            else:
                result = subprocess.run(["git", "diff"],
                                       capture_output=True, check=True, text=True, cwd=repo_path)
            return result.stdout
        except subprocess.SubprocessError as e:
            return f"Error: {e}"
    
    def git_commit(self, message: str, repo_path: Optional[str] = None) -> str:
        """Commit changes for the specified repository"""
        if not self._is_git_repo(repo_path):
            return "Not a git repository"
        
        try:
            # Add all changes
            subprocess.run(["git", "add", "."],
                          capture_output=True, check=True, cwd=repo_path)
            
            # Commit changes
            result = subprocess.run(["git", "commit", "-m", message],
                                   capture_output=True, check=True, text=True, cwd=repo_path)
            return result.stdout
        except subprocess.SubprocessError as e:
            return f"Error: {e}"
