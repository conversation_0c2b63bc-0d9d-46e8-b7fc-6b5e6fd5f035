"""
Natural language detection for Agent Mode

This module provides the NaturalLanguageDetector class for detecting
whether input is natural language or a shell command.
"""
import re
import logging
from typing import List, Set

from warpai.config.settings import settings

# Set up logging
logger = logging.getLogger(__name__)

class NaturalLanguageDetector:
    """
    Natural Language Detector

    This class provides methods for detecting whether input is natural language
    or a shell command.
    """

    def __init__(self):
        """Initialize the natural language detector"""
        # Common shell command patterns
        self.command_patterns = [
            r"^[a-zA-Z0-9_\-\.]+(\s+(-{1,2}[a-zA-Z0-9_\-]+|\S+))*$",  # Basic command with args
            r"^cd(\s+\S+)?$",  # cd command
            r"^ls(\s+(-[a-zA-Z]+\s+)*(\S+)?)*$",  # ls command
            r"^git(\s+\S+)+$",  # git commands
            r"^npm(\s+\S+)+$",  # npm commands
            r"^python(\d+)?(\s+\S+)+$",  # python commands
            r"^node(\s+\S+)+$",  # node commands
            r"^docker(\s+\S+)+$",  # docker commands
            r"^kubectl(\s+\S+)+$",  # kubectl commands
            r"^ssh(\s+\S+)+$",  # ssh commands
            r"^curl(\s+\S+)+$",  # curl commands
            r"^wget(\s+\S+)+$",  # wget commands
            r"^cat(\s+\S+)+$",  # cat commands
            r"^grep(\s+\S+)+$",  # grep commands
            r"^find(\s+\S+)+$",  # find commands
            r"^mkdir(\s+\S+)+$",  # mkdir commands
            r"^rm(\s+\S+)+$",  # rm commands
            r"^cp(\s+\S+)+$",  # cp commands
            r"^mv(\s+\S+)+$",  # mv commands
            r"^echo(\s+\S+)+$",  # echo commands
            r"^touch(\s+\S+)+$",  # touch commands
            r"^chmod(\s+\S+)+$",  # chmod commands
            r"^chown(\s+\S+)+$",  # chown commands
            r"^ps(\s+\S+)*$",  # ps commands
            r"^kill(\s+\S+)+$",  # kill commands
            r"^tar(\s+\S+)+$",  # tar commands
            r"^zip(\s+\S+)+$",  # zip commands
            r"^unzip(\s+\S+)+$",  # unzip commands
            r"^gzip(\s+\S+)+$",  # gzip commands
            r"^gunzip(\s+\S+)+$",  # gunzip commands
            r"^ping(\s+\S+)+$",  # ping commands
            r"^traceroute(\s+\S+)+$",  # traceroute commands
            r"^netstat(\s+\S+)*$",  # netstat commands
            r"^ifconfig(\s+\S+)*$",  # ifconfig commands
            r"^ipconfig(\s+\S+)*$",  # ipconfig commands
            r"^nslookup(\s+\S+)+$",  # nslookup commands
            r"^dig(\s+\S+)+$",  # dig commands
            r"^whois(\s+\S+)+$",  # whois commands
            r"^telnet(\s+\S+)+$",  # telnet commands
            r"^ftp(\s+\S+)+$",  # ftp commands
            r"^scp(\s+\S+)+$",  # scp commands
            r"^rsync(\s+\S+)+$",  # rsync commands
            r"^ssh-keygen(\s+\S+)*$",  # ssh-keygen commands
            r"^openssl(\s+\S+)+$",  # openssl commands
            r"^gpg(\s+\S+)+$",  # gpg commands
            r"^systemctl(\s+\S+)+$",  # systemctl commands
            r"^service(\s+\S+)+$",  # service commands
            r"^journalctl(\s+\S+)*$",  # journalctl commands
            r"^apt(\s+\S+)+$",  # apt commands
            r"^apt-get(\s+\S+)+$",  # apt-get commands
            r"^yum(\s+\S+)+$",  # yum commands
            r"^dnf(\s+\S+)+$",  # dnf commands
            r"^brew(\s+\S+)+$",  # brew commands
            r"^pacman(\s+\S+)+$",  # pacman commands
            r"^zypper(\s+\S+)+$",  # zypper commands
            r"^pip(\d+)?(\s+\S+)+$",  # pip commands
            r"^gem(\s+\S+)+$",  # gem commands
            r"^composer(\s+\S+)+$",  # composer commands
            r"^cargo(\s+\S+)+$",  # cargo commands
            r"^go(\s+\S+)+$",  # go commands
            r"^make(\s+\S+)*$",  # make commands
            r"^cmake(\s+\S+)*$",  # cmake commands
            r"^gcc(\s+\S+)+$",  # gcc commands
            r"^g\+\+(\s+\S+)+$",  # g++ commands
            r"^javac(\s+\S+)+$",  # javac commands
            r"^java(\s+\S+)+$",  # java commands
            r"^mvn(\s+\S+)+$",  # mvn commands
            r"^gradle(\s+\S+)+$",  # gradle commands
            r"^ant(\s+\S+)+$",  # ant commands
            r"^dotnet(\s+\S+)+$",  # dotnet commands
            r"^nuget(\s+\S+)+$",  # nuget commands
            r"^msbuild(\s+\S+)+$",  # msbuild commands
            r"^xcodebuild(\s+\S+)+$",  # xcodebuild commands
            r"^swift(\s+\S+)+$",  # swift commands
            r"^rustc(\s+\S+)+$",  # rustc commands
            r"^perl(\s+\S+)+$",  # perl commands
            r"^ruby(\s+\S+)+$",  # ruby commands
            r"^php(\s+\S+)+$",  # php commands
            r"^lua(\s+\S+)+$",  # lua commands
            r"^powershell(\s+\S+)*$",  # powershell commands
            r"^cmd(\s+\S+)*$",  # cmd commands
            r"^bash(\s+\S+)*$",  # bash commands
            r"^sh(\s+\S+)*$",  # sh commands
            r"^zsh(\s+\S+)*$",  # zsh commands
            r"^fish(\s+\S+)*$",  # fish commands
            r"^vim(\s+\S+)*$",  # vim commands
            r"^nano(\s+\S+)*$",  # nano commands
            r"^emacs(\s+\S+)*$",  # emacs commands
            r"^code(\s+\S+)*$",  # code commands
            r"^notepad(\s+\S+)*$",  # notepad commands
            r"^gedit(\s+\S+)*$",  # gedit commands
            r"^subl(\s+\S+)*$",  # sublime text commands
            r"^atom(\s+\S+)*$",  # atom commands
            r"^pycharm(\s+\S+)*$",  # pycharm commands
            r"^intellij(\s+\S+)*$",  # intellij commands
            r"^eclipse(\s+\S+)*$",  # eclipse commands
            r"^vscode(\s+\S+)*$",  # vscode commands
            r"^xcode(\s+\S+)*$",  # xcode commands
            r"^android-studio(\s+\S+)*$",  # android studio commands
            r"^flutter(\s+\S+)+$",  # flutter commands
            r"^dart(\s+\S+)+$",  # dart commands
            r"^ionic(\s+\S+)+$",  # ionic commands
            r"^cordova(\s+\S+)+$",  # cordova commands
            r"^react-native(\s+\S+)+$",  # react-native commands
            r"^expo(\s+\S+)+$",  # expo commands
            r"^heroku(\s+\S+)+$",  # heroku commands
            r"^aws(\s+\S+)+$",  # aws commands
            r"^gcloud(\s+\S+)+$",  # gcloud commands
            r"^az(\s+\S+)+$",  # azure commands
            r"^terraform(\s+\S+)+$",  # terraform commands
            r"^ansible(\s+\S+)+$",  # ansible commands
            r"^vagrant(\s+\S+)+$",  # vagrant commands
            r"^virtualbox(\s+\S+)+$",  # virtualbox commands
            r"^vmware(\s+\S+)+$",  # vmware commands
            r"^qemu(\s+\S+)+$",  # qemu commands
            r"^kvm(\s+\S+)+$",  # kvm commands
            r"^xen(\s+\S+)+$",  # xen commands
            r"^hyper-v(\s+\S+)+$",  # hyper-v commands
            r"^podman(\s+\S+)+$",  # podman commands
            r"^containerd(\s+\S+)+$",  # containerd commands
            r"^cri-o(\s+\S+)+$",  # cri-o commands
            r"^rkt(\s+\S+)+$",  # rkt commands
            r"^lxc(\s+\S+)+$",  # lxc commands
            r"^lxd(\s+\S+)+$",  # lxd commands
            r"^systemd(\s+\S+)+$",  # systemd commands
            r"^upstart(\s+\S+)+$",  # upstart commands
            r"^init(\s+\S+)+$",  # init commands
            r"^cron(\s+\S+)+$",  # cron commands
            r"^at(\s+\S+)+$",  # at commands
            r"^batch(\s+\S+)+$",  # batch commands
            r"^anacron(\s+\S+)+$",  # anacron commands
            r"^logrotate(\s+\S+)+$",  # logrotate commands
            r"^rsyslog(\s+\S+)+$",  # rsyslog commands
            r"^syslog(\s+\S+)+$",  # syslog commands
            r"^logger(\s+\S+)+$",  # logger commands
            r"^dmesg(\s+\S+)*$",  # dmesg commands
            r"^journald(\s+\S+)*$",  # journald commands
            r"^logwatch(\s+\S+)*$",  # logwatch commands
            r"^logcheck(\s+\S+)*$",  # logcheck commands
            r"^fail2ban(\s+\S+)*$",  # fail2ban commands
            r"^iptables(\s+\S+)*$",  # iptables commands
            r"^firewalld(\s+\S+)*$",  # firewalld commands
            r"^ufw(\s+\S+)*$",  # ufw commands
            r"^selinux(\s+\S+)*$",  # selinux commands
            r"^apparmor(\s+\S+)*$",  # apparmor commands
            r"^chroot(\s+\S+)+$",  # chroot commands
            r"^jail(\s+\S+)+$",  # jail commands
            r"^sandbox(\s+\S+)+$",  # sandbox commands
            r"^firejail(\s+\S+)+$",  # firejail commands
            r"^bubblewrap(\s+\S+)+$",  # bubblewrap commands
            r"^snap(\s+\S+)+$",  # snap commands
            r"^flatpak(\s+\S+)+$",  # flatpak commands
            r"^appimage(\s+\S+)+$",  # appimage commands
            r"^dpkg(\s+\S+)+$",  # dpkg commands
            r"^rpm(\s+\S+)+$",  # rpm commands
            r"^yast(\s+\S+)+$",  # yast commands
            r"^zypper(\s+\S+)+$",  # zypper commands
            r"^dnf(\s+\S+)+$",  # dnf commands
            r"^yum(\s+\S+)+$",  # yum commands
            r"^apt(\s+\S+)+$",  # apt commands
            r"^apt-get(\s+\S+)+$",  # apt-get commands
            r"^aptitude(\s+\S+)+$",  # aptitude commands
            r"^synaptic(\s+\S+)+$",  # synaptic commands
            r"^pacman(\s+\S+)+$",  # pacman commands
            r"^pamac(\s+\S+)+$",  # pamac commands
            r"^yaourt(\s+\S+)+$",  # yaourt commands
            r"^aur(\s+\S+)+$",  # aur commands
            r"^brew(\s+\S+)+$",  # brew commands
            r"^port(\s+\S+)+$",  # port commands
            r"^pkg(\s+\S+)+$",  # pkg commands
            r"^pkgin(\s+\S+)+$",  # pkgin commands
            r"^pkgadd(\s+\S+)+$",  # pkgadd commands
            r"^pkgrm(\s+\S+)+$",  # pkgrm commands
            r"^pkginfo(\s+\S+)+$",  # pkginfo commands
            r"^pkgutil(\s+\S+)+$",  # pkgutil commands
            r"^pkgmk(\s+\S+)+$",  # pkgmk commands
            r"^pkgtrans(\s+\S+)+$",  # pkgtrans commands
            r"^pkgchk(\s+\S+)+$",  # pkgchk commands
            r"^pkgparam(\s+\S+)+$",  # pkgparam commands
            r"^pkgask(\s+\S+)+$",  # pkgask commands
            r"^pkgproto(\s+\S+)+$",  # pkgproto commands
            r"^pkgmk(\s+\S+)+$",  # pkgmk commands
            r"^pkgtrans(\s+\S+)+$",  # pkgtrans commands
            r"^pkgchk(\s+\S+)+$",  # pkgchk commands
            r"^pkgparam(\s+\S+)+$",  # pkgparam commands
            r"^pkgask(\s+\S+)+$",  # pkgask commands
            r"^pkgproto(\s+\S+)+$",  # pkgproto commands
        ]

        # Load denylist from settings
        self.denylist = set(settings.agent_mode_auto_detection_denylist)

        logger.info("Natural language detector initialized")

    def is_natural_language(self, text: str) -> bool:
        """
        Check if text is natural language

        Args:
            text: Text to check

        Returns:
            bool: True if text is natural language, False otherwise
        """
        # Skip empty text
        if not text or not text.strip():
            return False

        # New: Check for commands embedded after a common shell prompt
        # This handles cases where a command is part of a larger console output or preceded by natural language
        # It looks for a common prompt ending with '>' or '$' followed by a space, and then captures the rest.
        prompt_command_match = re.search(r"(?:.*(?:>|\$)\s*)(.*)", text.strip())
        if prompt_command_match:
            potential_command = prompt_command_match.group(1).strip()
            # Now, check if this extracted part is a valid command
            for pattern in self.command_patterns:
                if re.match(pattern, potential_command):
                    logger.debug(f"Detected command after prompt: '{potential_command}'")
                    return False # It's a command, not natural language

        # Existing: Check if text is in denylist
        if text.strip() in self.denylist:
            return False

        # Existing: Check if text matches any command pattern (for commands at the very beginning of the input)
        for pattern in self.command_patterns:
            if re.match(pattern, text.strip()):
                return False

        # Advanced natural language detection

        # 1. Check for questions (presence of question marks)
        if "?" in text:
            return True

        # 2. Check for sentence structure (multiple words with spaces)
        if re.match(r"^[A-Za-z]+\s+[A-Za-z]+\s+[A-Za-z]+", text.strip()):
            # At least 3 words in sequence
            return True

        # 3. Check for common natural language starters
        nl_starters = [
            # Question words
            r"^(how|what|why|when|where|who|which)",
            # Modal verbs
            r"^(can|could|would|should|may|might|must)",
            # Auxiliary verbs
            r"^(is|are|am|do|does|did|has|have|had|will|shall)",
            # Common request verbs
            r"^(please|help|show|tell|find|search|list)",
            # Creation verbs
            r"^(create|make|build|generate|develop|implement)",
            # Execution verbs
            r"^(run|execute|start|launch|perform|do)",
            # Installation verbs
            r"^(install|setup|configure|deploy|initialize)",
            # Analysis verbs
            r"^(analyze|examine|inspect|investigate|study|review)",
            # Explanation verbs
            r"^(explain|describe|clarify|elaborate|detail)",
            # Comparison verbs
            r"^(compare|contrast|differentiate|distinguish)",
            # Transformation verbs
            r"^(convert|transform|change|modify|alter)",
            # Calculation verbs
            r"^(calculate|compute|determine|solve|evaluate)",
            # Debugging verbs
            r"^(fix|debug|resolve|troubleshoot|repair)",
            # Optimization verbs
            r"^(optimize|improve|enhance|refine|streamline)",
            # Update verbs
            r"^(update|upgrade|patch|refresh|renew)",
            # Removal verbs
            r"^(remove|delete|uninstall|eliminate|erase)",
            # Addition verbs
            r"^(add|insert|append|attach|include)",
            # Modification verbs
            r"^(modify|edit|change|adjust|revise)",
            # Movement verbs
            r"^(move|relocate|transfer|shift|reposition)",
            # Copying verbs
            r"^(copy|duplicate|clone|replicate|backup)",
            # Restoration verbs
            r"^(restore|recover|reinstate|revert|rollback)",
            # Deployment verbs
            r"^(deploy|publish|release|distribute|deliver)",
            # Retrieval verbs
            r"^(fetch|retrieve|get|obtain|acquire)",
            # Processing verbs
            r"^(process|handle|manage|operate|work)",
            # Validation verbs
            r"^(validate|verify|check|confirm|ensure)",
            # Testing verbs
            r"^(test|examine|assess|evaluate|measure)",
            # Monitoring verbs
            r"^(monitor|track|observe|watch|follow)",
            # Reporting verbs
            r"^(report|log|document|record|note)",
            # Summarization verbs
            r"^(summarize|conclude|wrap|finalize|complete)",
            # Recommendation verbs
            r"^(recommend|suggest|advise|propose|offer)",
            # Assistance verbs
            r"^(assist|support|aid|help|guide)",
            # Control verbs
            r"^(enable|disable|activate|deactivate|toggle)",
            # Operation verbs
            r"^(start|stop|restart|pause|resume|continue)",
            # Termination verbs
            r"^(cancel|abort|quit|exit|close|terminate)",
            # Access verbs
            r"^(open|access|read|write|view)",
            # Display verbs
            r"^(print|display|show|render|visualize)",
            # Formatting verbs
            r"^(format|style|arrange|organize|structure)",
            # Parsing verbs
            r"^(parse|interpret|analyze|decode|extract)",
            # Filtering verbs
            r"^(filter|sort|group|join|split|merge)",
            # Mathematical verbs
            r"^(add|subtract|multiply|divide|calculate)",
        ]

        for starter in nl_starters:
            if re.search(starter, text.lower()):
                return True

        # 4. Check for programming task descriptions
        programming_indicators = [
            r"function", r"method", r"class", r"object", r"variable",
            r"array", r"list", r"dictionary", r"map", r"set",
            r"loop", r"iteration", r"recursion", r"algorithm",
            r"database", r"query", r"API", r"endpoint", r"request",
            r"response", r"server", r"client", r"frontend", r"backend",
            r"UI", r"UX", r"interface", r"component", r"module",
            r"library", r"framework", r"package", r"dependency",
            r"compile", r"build", r"deploy", r"test", r"debug",
            r"error", r"exception", r"bug", r"issue", r"problem",
            r"solution", r"implementation", r"code", r"script",
            r"program", r"application", r"app", r"website", r"web",
            r"mobile", r"desktop", r"cloud", r"serverless", r"microservice",
            r"authentication", r"authorization", r"security", r"encryption",
            r"performance", r"optimization", r"scalability", r"reliability",
            r"maintainability", r"readability", r"documentation", r"comment",
            r"version", r"git", r"repository", r"commit", r"branch",
            r"merge", r"pull", r"push", r"clone", r"fork",
            r"agile", r"scrum", r"sprint", r"backlog", r"story",
            r"task", r"feature", r"requirement", r"specification", r"design",
            r"architecture", r"pattern", r"model", r"view", r"controller",
            r"MVC", r"MVP", r"MVVM", r"REST", r"GraphQL",
            r"SOAP", r"HTTP", r"HTTPS", r"TCP", r"UDP",
            r"IP", r"DNS", r"SSL", r"TLS", r"SSH",
            r"FTP", r"SMTP", r"POP", r"IMAP", r"LDAP",
            r"OAuth", r"JWT", r"token", r"cookie", r"session",
            r"cache", r"memory", r"disk", r"storage", r"database",
            r"SQL", r"NoSQL", r"MongoDB", r"MySQL", r"PostgreSQL",
            r"Oracle", r"SQLite", r"Redis", r"Elasticsearch", r"Cassandra",
            r"Docker", r"Kubernetes", r"container", r"orchestration", r"virtualization",
            r"CI", r"CD", r"pipeline", r"automation", r"testing",
            r"unit", r"integration", r"system", r"acceptance", r"regression",
            r"performance", r"load", r"stress", r"security", r"penetration",
            r"monitoring", r"logging", r"alerting", r"metrics", r"analytics",
            r"dashboard", r"report", r"visualization", r"chart", r"graph",
            r"machine learning", r"AI", r"neural network", r"deep learning", r"data science",
            r"big data", r"data mining", r"data analysis", r"data visualization", r"data processing",
            r"ETL", r"extraction", r"transformation", r"loading", r"warehousing",
            r"blockchain", r"cryptocurrency", r"smart contract", r"token", r"wallet",
            r"IoT", r"embedded", r"sensor", r"actuator", r"device",
            r"AR", r"VR", r"XR", r"3D", r"game",
            r"animation", r"graphics", r"rendering", r"shader", r"texture",
            r"audio", r"video", r"streaming", r"encoding", r"decoding",
            r"compression", r"decompression", r"encryption", r"decryption", r"hashing",
            r"authentication", r"authorization", r"identity", r"permission", r"role",
            r"user", r"admin", r"customer", r"client", r"vendor",
            r"payment", r"transaction", r"invoice", r"receipt", r"order",
            r"product", r"service", r"subscription", r"plan", r"pricing",
            r"marketing", r"sales", r"support", r"helpdesk", r"ticket",
            r"CRM", r"ERP", r"CMS", r"LMS", r"HRM",
            r"SCM", r"PLM", r"BPM", r"DMS", r"KMS",
        ]

        for indicator in programming_indicators:
            if re.search(r'\b' + re.escape(indicator) + r'\b', text.lower()):
                return True

        # 5. Check for sentence structure (presence of articles, prepositions, etc.)
        structure_indicators = [
            r'\b(a|an|the)\b',  # Articles
            r'\b(in|on|at|by|for|with|from|to|of|about)\b',  # Prepositions
            r'\b(and|or|but|so|because|if|when|while|although|though)\b',  # Conjunctions
            r'\b(I|you|he|she|it|we|they|me|him|her|us|them)\b',  # Pronouns
            r'\b(my|your|his|her|its|our|their)\b',  # Possessive adjectives
            r'\b(this|that|these|those)\b',  # Demonstratives
            r'\b(very|really|quite|too|so|much|many|few|little|more|most|some|any|all)\b',  # Quantifiers
        ]

        structure_count = 0
        for indicator in structure_indicators:
            if re.search(indicator, text.lower()):
                structure_count += 1

        # If we have at least 2 structural elements, it's likely natural language
        if structure_count >= 2:
            return True

        # 6. Check for command-like patterns (if it starts with a single word followed by arguments)
        if re.match(r'^[a-zA-Z0-9_\-\.]+(\s+(-{1,2}[a-zA-Z0-9_\-]+|\S+))+$', text.strip()):
            return False

        # 7. Check for file paths or URLs (these are often not natural language)
        if re.match(r'^(/|\\|\.{1,2}/|\.{1,2}\\|~/).*', text.strip()) or re.match(r'^[a-zA-Z]:\\.*', text.strip()) or re.match(r'^(https?|ftp)://.*', text.strip()):
            return False

        # 8. Check for length (longer inputs are more likely to be natural language)
        if len(text.split()) >= 5:
            return True

        # Default to command if we're not sure and it's short
        return len(text.split()) >= 3  # Consider it natural language if it's at least 3 words

    def add_to_denylist(self, command: str) -> None:
        """
        Add a command to the denylist

        Args:
            command: Command to add to denylist
        """
        self.denylist.add(command.strip())
        logger.debug(f"Added command to denylist: {command}")

    def remove_from_denylist(self, command: str) -> None:
        """
        Remove a command from the denylist

        Args:
            command: Command to remove from denylist
        """
        if command.strip() in self.denylist:
            self.denylist.remove(command.strip())
            logger.debug(f"Removed command from denylist: {command}")

    def get_denylist(self) -> List[str]:
        """
        Get the current denylist

        Returns:
            List[str]: Current denylist
        """
        return list(self.denylist)
