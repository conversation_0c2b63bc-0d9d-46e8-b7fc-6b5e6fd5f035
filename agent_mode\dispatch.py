"""
Dispatch Mode for WarpAI

This module provides the DispatchMode class for autonomous task execution.
"""
import os
import re
import time
import logging
import json
from typing import Dict, List, Optional, Tuple, Union, Any

from warpai.gemini_client import Gemini<PERSON>lient
from warpai.utils.command_executor import CommandExecutor
from warpai.utils.context_manager import ContextManager
from warpai.config.settings import settings
from warpai.agent_mode.conversation import Conversation
from warpai.agent_mode.autonomy import AutonomyManager

# Set up logging
logger = logging.getLogger(__name__)

class DispatchMode:
    """
    Dispatch Mode for WarpAI

    This class provides functionality for autonomous task execution, allowing
    WarpAI to plan and execute complex tasks without user intervention.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        context_manager: Optional[ContextManager] = None,
        command_executor: Optional[CommandExecutor] = None,
    ):
        """
        Initialize Dispatch Mode

        Args:
            api_key: API key for the LLM provider
            model: Model to use for Dispatch Mode
            context_manager: Context manager instance
            command_executor: Command executor instance
        """
        # Use the model from settings if not provided
        if model is None:
            model = settings.model

        # Initialize clients based on model provider
        self.model = model
        self.api_key = api_key

        # Initialize components
        self.gemini_client = GeminiClient(api_key=api_key, model=model)
        self.context_manager = context_manager or ContextManager()
        self.command_executor = command_executor or CommandExecutor()

        # Initialize Dispatch Mode components
        self.autonomy_manager = AutonomyManager(api_key=api_key, model=model)
        self.current_conversation = Conversation()

        # State tracking
        self.is_active = False
        self.current_task = None
        self.current_plan = []
        self.current_step = 0
        self.task_status = "idle"  # idle, planning, executing, completed, failed

        logger.info(f"Dispatch Mode initialized with model: {model}")

    def activate(self) -> None:
        """Activate Dispatch Mode"""
        self.is_active = True
        logger.info("Dispatch Mode activated")

    def deactivate(self) -> None:
        """Deactivate Dispatch Mode"""
        self.is_active = False
        self.current_task = None
        self.current_plan = []
        self.current_step = 0
        self.task_status = "idle"
        logger.info("Dispatch Mode deactivated")

    def toggle(self) -> bool:
        """
        Toggle Dispatch Mode on/off

        Returns:
            bool: True if Dispatch Mode is now active, False otherwise
        """
        if self.is_active:
            self.deactivate()
        else:
            self.activate()
        return self.is_active

    def dispatch_task(self, task_description: str) -> Dict[str, Any]:
        """
        Dispatch a task for autonomous execution

        Args:
            task_description: Description of the task to execute

        Returns:
            Dict: Task status and plan
        """
        # Check if Dispatch Mode is active
        if not self.is_active:
            return {
                "success": False,
                "error": "Dispatch Mode is not active",
                "task_status": "idle",
            }

        # Set task status
        self.task_status = "planning"
        self.current_task = task_description

        # Create a new conversation
        self.current_conversation = Conversation()
        self.current_conversation.add_user_message(task_description)

        # Generate a plan
        plan = self._generate_plan(task_description)

        if not plan:
            self.task_status = "failed"
            return {
                "success": False,
                "error": "Failed to generate a plan",
                "task_status": "failed",
            }

        # Set the current plan
        self.current_plan = plan
        self.current_step = 0

        # Set task status
        self.task_status = "executing"

        # Execute the plan if auto-execute is enabled
        if settings.dispatch_auto_execute:
            execution_result = self._execute_plan()
            return {
                "success": execution_result["success"],
                "plan": plan,
                "current_step": self.current_step,
                "total_steps": len(plan),
                "task_status": self.task_status,
                "execution_result": execution_result,
            }

        # Return the plan
        return {
            "success": True,
            "plan": plan,
            "current_step": self.current_step,
            "total_steps": len(plan),
            "task_status": self.task_status,
        }

    def execute_next_step(self) -> Dict[str, Any]:
        """
        Execute the next step in the current plan

        Returns:
            Dict: Execution result
        """
        # Check if Dispatch Mode is active
        if not self.is_active:
            return {
                "success": False,
                "error": "Dispatch Mode is not active",
                "task_status": "idle",
            }

        # Check if there is a current plan
        if not self.current_plan:
            return {
                "success": False,
                "error": "No plan to execute",
                "task_status": "idle",
            }

        # Check if we've reached the end of the plan
        if self.current_step >= len(self.current_plan):
            self.task_status = "completed"
            return {
                "success": True,
                "message": "Plan execution completed",
                "task_status": "completed",
            }

        # Get the current step
        step = self.current_plan[self.current_step]

        # Execute the step
        result = self._execute_step(step)

        # Update the current step
        self.current_step += 1

        # Check if we've reached the end of the plan
        if self.current_step >= len(self.current_plan):
            self.task_status = "completed"

        # Return the result
        return {
            "success": result["success"],
            "step": step,
            "step_number": self.current_step,
            "total_steps": len(self.current_plan),
            "result": result,
            "task_status": self.task_status,
        }

    def _generate_plan(self, task_description: str) -> List[Dict[str, Any]]:
        """
        Generate a plan for a task

        Args:
            task_description: Description of the task

        Returns:
            List[Dict[str, Any]]: Plan steps
        """
        # Prepare the prompt
        prompt = f"""
        You are an AI assistant tasked with planning the execution of a complex task.

        Task: {task_description}

        Break down this task into a series of steps that can be executed in sequence.
        For each step, provide:
        1. A description of the step
        2. The command to execute (if applicable)
        3. The expected output or result
        4. Any dependencies or prerequisites

        Format your response as a JSON array of steps, where each step is an object with the following properties:
        - description: A description of the step
        - command: The command to execute (if applicable)
        - expected_output: The expected output or result
        - dependencies: Any dependencies or prerequisites

        Example:
        ```json
        [
            {{
                "description": "Check if Python is installed",
                "command": "python --version",
                "expected_output": "Python 3.x.x",
                "dependencies": []
            }},
            {{
                "description": "Create a virtual environment",
                "command": "python -m venv myenv",
                "expected_output": "Virtual environment created",
                "dependencies": ["Python 3.x.x"]
            }}
        ]
        ```

        Only include the JSON array in your response, no other text.
        """

        # Generate response
        response = self.gemini_client.generate_response(
            prompt=prompt
        )

        # Extract JSON from response
        try:
            # Find JSON array in response
            json_match = re.search(r'\[\s*\{.*\}\s*\]', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                plan = json.loads(json_str)
                return plan
            else:
                logger.error("Failed to extract JSON from response")
                return []
        except Exception as e:
            logger.error(f"Failed to parse plan: {e}")
            return []

    def _execute_plan(self) -> Dict[str, Any]:
        """
        Execute the current plan

        Returns:
            Dict: Execution result
        """
        # Check if there is a current plan
        if not self.current_plan:
            return {
                "success": False,
                "error": "No plan to execute",
            }

        # Execute each step in the plan
        results = []
        success = True

        for i, step in enumerate(self.current_plan):
            # Update current step
            self.current_step = i

            # Execute the step
            result = self._execute_step(step)
            results.append(result)

            # If the step failed, stop execution
            if not result["success"]:
                success = False
                break

        # Update task status
        if success:
            self.task_status = "completed"
        else:
            self.task_status = "failed"

        # Return the results
        return {
            "success": success,
            "results": results,
            "completed_steps": self.current_step + 1,
            "total_steps": len(self.current_plan),
        }

    def _execute_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a single step in the plan

        Args:
            step: Step to execute

        Returns:
            Dict: Execution result
        """
        # Get the command
        command = step.get("command", "")

        # Skip if no command
        if not command:
            return {
                "success": True,
                "output": "No command to execute",
                "error": "",
            }

        # Check if command is allowed
        if not self.autonomy_manager.is_command_allowed(command):
            return {
                "success": False,
                "output": "",
                "error": "Command not allowed for security reasons",
            }

        # Execute the command
        output, exit_code = self.command_executor.execute_command(command)

        # Add command and output to conversation
        self.current_conversation.add_command(command, output, exit_code == 0)

        # Return the result
        return {
            "success": exit_code == 0,
            "output": output,
            "error": "" if exit_code == 0 else output,
        }
