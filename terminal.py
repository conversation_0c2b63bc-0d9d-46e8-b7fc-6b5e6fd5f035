"""
Terminal interface for WarpAI
"""
import os
import sys
from typing import Dict, List, Optional, Any
import platform

from prompt_toolkit import PromptSession
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.history import FileHistory
from prompt_toolkit.auto_suggest import <PERSON>SuggestFromHistory
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.styles import Style
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.keys import Keys

from warpai.agent import Agent
from warpai.advanced_agent import AdvancedAgent
from warpai.ui.display import Display
from warpai.ui.keyboard_help import KeyboardHelp
from warpai.config.settings import settings
from warpai.agent_mode.agent import AgentMode
from warpai.agent_mode.dispatch import DispatchMode
from warpai.agent_mode.detector import NaturalLanguageDetector

class Terminal:
    """Terminal interface for WarpAI"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """Initialize the terminal interface"""
        # Use the model from settings if not provided
        if model is None:
            model = settings.model

        # Use advanced agent if enabled in settings
        if settings.use_advanced_agent:
            self.agent = AdvancedAgent(
                api_key=api_key,
                model=model,
                enable_rag=True,
                enable_web=True,
                enable_multi_agent=True,
                enable_file_watcher=True,
                enable_plugins=True
            )
        else:
            self.agent = Agent(api_key=api_key, model=model)

        # Initialize Agent Mode components
        self.agent_mode = AgentMode(api_key=api_key, model=model)
        self.dispatch_mode = DispatchMode(api_key=api_key, model=model)
        self.nl_detector = NaturalLanguageDetector()

        # State tracking
        self.agent_mode_active = settings.agent_mode_enabled
        self.dispatch_mode_active = False
        self.follow_up_mode = False

        # Initialize display and keyboard help
        self.display = Display()
        self.keyboard_help = KeyboardHelp()

        # Set up prompt session
        history_file = os.path.expanduser("~/.warpai_history")
        self.session = PromptSession(
            history=FileHistory(history_file),
            auto_suggest=AutoSuggestFromHistory(),
            key_bindings=self._get_key_bindings(),
            style=self._get_style(),
        )

        # Set up command completer
        self.command_completer = WordCompleter([
            # Special commands
            "help", "exit", "clear", "agent", "dispatch", "follow",
            "keys", "shortcuts", "keyboard",

            # Common CLI tools
            "python", "node", "npm", "git", "ls", "dir", "cd",
            "cat", "type", "grep", "find", "mkdir", "touch",
            "rm", "del", "cp", "copy", "mv", "move",

            # Programming languages
            "python", "node", "java", "javac", "ruby", "perl", "php",
            "go", "rust", "gcc", "g++", "dotnet", "pwsh",

            # Package managers
            "pip", "npm", "yarn", "gem", "bundle", "cargo", "go",
            "apt", "apt-get", "brew", "choco",

            # Version control
            "git", "svn", "hg",

            # Container tools
            "docker", "docker-compose", "kubectl", "helm",

            # Cloud tools
            "aws", "az", "gcloud", "terraform", "ansible",

            # File operations
            "ls", "dir", "cd", "mkdir", "rmdir", "touch", "rm", "del",
            "cp", "copy", "mv", "move", "cat", "type", "grep", "find",

            # WarpAI commands
            "run", "save", "edit", "install", "search", "explain",
            "fix", "debug", "optimize", "test", "analyze",
        ])

    def _get_style(self) -> Style:
        """Get style for the prompt"""
        return Style.from_dict({
            # User input (default text)
            "": "white",
            # Prompt
            "prompt": "bold cyan",
        })

    def _get_key_bindings(self) -> KeyBindings:
        """Get key bindings for the prompt"""
        kb = KeyBindings()

        @kb.add(Keys.Enter)
        def _(event):
            """Handle Enter key"""
            # Check if there's a command to execute
            if self.agent.get_last_command():
                # Get the buffer text
                buffer_text = event.current_buffer.text

                # If the buffer is empty, execute the last command
                if not buffer_text.strip():
                    event.current_buffer.text = self.agent.get_last_command()

            # Accept the input
            event.current_buffer.validate_and_handle()

        @kb.add(Keys.Escape)
        def _(event):
            """Handle Escape key"""
            # Clear the last command
            self.agent.clear_last_command()

        @kb.add('escape', 'a')  # Alt+A (Escape followed by 'a')
        def _(event):
            """Toggle Agent Mode (Alt+A)"""
            # Toggle Agent Mode
            self.agent_mode_active = self.agent_mode.toggle()
            mode_status = "activated" if self.agent_mode_active else "deactivated"
            self.display.print(f"Agent Mode {mode_status}")

        @kb.add('c-d')  # Ctrl+D
        def _(event):
            """Toggle Dispatch Mode (Ctrl+D)"""
            # Toggle Dispatch Mode
            self.dispatch_mode_active = self.dispatch_mode.toggle()
            mode_status = "activated" if self.dispatch_mode_active else "deactivated"
            self.display.print(f"Dispatch Mode {mode_status}")

        @kb.add('c-f')  # Ctrl+F
        def _(event):
            """Toggle Follow-up Mode (Ctrl+F)"""
            # Toggle follow-up mode
            self.follow_up_mode = not self.follow_up_mode
            self.agent_mode.set_follow_up(self.follow_up_mode)
            mode_status = "activated" if self.follow_up_mode else "deactivated"
            self.display.print(f"Follow-up Mode {mode_status}")

        @kb.add('c-h')  # Ctrl+H
        def _(event):
            """Show keyboard help (Ctrl+H)"""
            # Show keyboard help
            self.keyboard_help.show()

        return kb

    def run(self) -> None:
        """Run the terminal interface"""
        # Print welcome message
        self.display.print_welcome()

        # Main loop
        while True:
            try:
                # Get user input with appropriate prefix
                prompt_prefix = self._get_prompt_prefix()
                user_input = self.session.prompt(
                    prompt_prefix,
                    completer=self.command_completer,
                )

                # Handle special commands
                if user_input.strip() == "exit":
                    break
                elif user_input.strip() == "help":
                    self.display.print_help()
                    continue
                elif user_input.strip() == "clear":
                    os.system("cls" if platform.system() == "Windows" else "clear")
                    continue
                elif user_input.strip() == "agent":
                    # Toggle Agent Mode
                    self.agent_mode_active = self.agent_mode.toggle()
                    mode_status = "activated" if self.agent_mode_active else "deactivated"
                    self.display.print(f"Agent Mode {mode_status}")
                    continue
                elif user_input.strip() == "dispatch":
                    # Toggle Dispatch Mode
                    self.dispatch_mode_active = self.dispatch_mode.toggle()
                    mode_status = "activated" if self.dispatch_mode_active else "deactivated"
                    self.display.print(f"Dispatch Mode {mode_status}")
                    continue
                elif user_input.strip() == "follow":
                    # Toggle follow-up mode
                    self.follow_up_mode = not self.follow_up_mode
                    self.agent_mode.set_follow_up(self.follow_up_mode)
                    mode_status = "activated" if self.follow_up_mode else "deactivated"
                    self.display.print(f"Follow-up Mode {mode_status}")
                    continue
                elif user_input.strip() in ["keys", "shortcuts", "keyboard"]:
                    # Show keyboard shortcuts
                    self.keyboard_help.show()
                    continue

                # Process with Advanced Agent (includes intelligent tool system)
                if isinstance(self.agent, AdvancedAgent):
                    # Use the advanced agent with intelligent tools
                    response = self.agent.process_input(user_input)

                    # Handle special responses
                    if response == "exit":
                        break
                    elif response == "help":
                        self.display.print_help()
                        continue
                    elif response == "clear":
                        os.system("cls" if platform.system() == "Windows" else "clear")
                        continue
                    else:
                        # Print AI response
                        self.display.print_ai_response(f"{settings.response_prefix}{response}")
                        continue

                # Fallback: Check if input is natural language and Agent Mode is enabled
                elif self.agent_mode_active and settings.agent_mode_auto_detection:
                    is_nl = self.nl_detector.is_natural_language(user_input)

                    if is_nl:
                        # Process with Agent Mode
                        result = self.agent_mode.process_query(user_input)

                        # Print AI response
                        self.display.print_ai_response(f"{settings.response_prefix}{result['response']}")

                        # Check if there's a suggested command
                        if result.get("suggested_command"):
                            command = result["suggested_command"]
                            self.display.print_command(command)

                            # Auto-execute the command if enabled
                            if settings.auto_execute_commands:
                                # Execute the command
                                execution_result = self.agent_mode.execute_suggested_command(command)

                                # Print the output
                                if execution_result["success"]:
                                    self.display.print_command_output(execution_result["output"])
                                else:
                                    self.display.print_error(execution_result["error"])

                        continue

                # If Dispatch Mode is active, dispatch the task
                if self.dispatch_mode_active:
                    result = self.dispatch_mode.dispatch_task(user_input)

                    # Print the plan
                    self.display.print(f"Task: {user_input}")
                    self.display.print("\nPlan:")
                    for i, step in enumerate(result.get("plan", [])):
                        self.display.print(f"  {i+1}. {step['description']}")
                        if step.get("command"):
                            self.display.print(f"     Command: {step['command']}")

                    # Execute the plan if auto-execute is enabled
                    if settings.dispatch_auto_execute and result.get("execution_result"):
                        execution_result = result["execution_result"]
                        self.display.print("\nExecution Results:")
                        for i, step_result in enumerate(execution_result.get("results", [])):
                            self.display.print(f"  Step {i+1}: {'Success' if step_result['success'] else 'Failed'}")
                            if step_result.get("output"):
                                self.display.print(f"     Output: {step_result['output']}")
                            if step_result.get("error"):
                                self.display.print(f"     Error: {step_result['error']}")

                    continue

                # Process with regular agent
                response = self.agent.process_input(user_input)

                # Handle special responses
                if response == "exit":
                    break
                elif response == "help":
                    self.display.print_help()
                elif response == "clear":
                    os.system("cls" if platform.system() == "Windows" else "clear")
                else:
                    # Print AI response
                    self.display.print_ai_response(f"{settings.response_prefix}{response}")

                    # Check if there's a command to execute
                    last_command = self.agent.get_last_command()
                    if last_command:
                        self.display.print_command(last_command)

                        # Auto-execute the command without asking for confirmation
                        # Execute the command
                        output, exit_code = self.agent.execute_last_command()

                        # Print the output
                        if exit_code == 0:
                            self.display.print_command_output(output)
                        else:
                            self.display.print_error(output)

                    # Code is now auto-saved in the agent, so we don't need to handle it here

            except KeyboardInterrupt:
                # Handle Ctrl+C
                self.display.print("\nUse 'exit' to exit WarpAI.")
            except EOFError:
                # Handle Ctrl+D
                break

        # Print goodbye message
        self.display.print("\nGoodbye! 👋")

    def clear_screen(self) -> None:
        """Clear the screen"""
        os.system("cls" if platform.system() == "Windows" else "clear")

    def _get_prompt_prefix(self) -> str:
        """
        Get the appropriate prompt prefix based on current mode

        Returns:
            str: Prompt prefix
        """
        prefix = settings.prompt_prefix

        # Add Agent Mode indicator
        if self.agent_mode_active:
            prefix = f"{settings.agent_mode_indicator} {prefix}"

        # Add Dispatch Mode indicator
        if self.dispatch_mode_active:
            prefix = f"{settings.dispatch_mode_indicator} {prefix}"

        # Add follow-up indicator
        if self.follow_up_mode:
            prefix = f"{settings.follow_up_indicator} {prefix}"

        return prefix
