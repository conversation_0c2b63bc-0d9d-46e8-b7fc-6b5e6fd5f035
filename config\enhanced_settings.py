"""
Enhanced settings for WarpAI
"""
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

class ModelSettings(BaseModel):
    """Settings for the AI model"""
    name: str = Field(default="gemini-1.5-pro", description="The name of the model to use")
    temperature: float = Field(default=0.7, description="The temperature to use for generation")
    top_p: float = Field(default=0.95, description="The top_p to use for generation")
    top_k: int = Field(default=40, description="The top_k to use for generation")
    max_output_tokens: int = Field(default=8192, description="The maximum number of tokens to generate")

class UISettings(BaseModel):
    """Settings for the UI"""
    prompt_prefix: str = Field(default="WarpAI> ", description="The prefix to use for the prompt")
    response_prefix: str = Field(default="", description="The prefix to use for the response")
    theme: str = Field(default="monokai", description="The theme to use for syntax highlighting")
    show_line_numbers: bool = Field(default=True, description="Whether to show line numbers in code blocks")
    max_width: int = Field(default=100, description="The maximum width of the terminal output")

class CodeIndexerSettings(BaseModel):
    """Settings for the code indexer"""
    enabled: bool = Field(default=True, description="Whether to enable the code indexer")
    index_on_startup: bool = Field(default=True, description="Whether to index the codebase on startup")
    exclude_dirs: List[str] = Field(
        default=[".git", ".github", "node_modules", "venv", ".venv", "__pycache__", "dist", "build"],
        description="Directories to exclude from indexing"
    )
    file_extensions: List[str] = Field(
        default=[".py", ".js", ".ts", ".jsx", ".tsx", ".html", ".css", ".java", ".c", ".cpp", ".cs", ".go", ".rs", ".rb", ".php", ".swift", ".kt", ".sh", ".ps1", ".sql", ".json", ".yaml", ".yml", ".xml", ".md"],
        description="File extensions to include in indexing"
    )

class WebRetrieverSettings(BaseModel):
    """Settings for the web retriever"""
    enabled: bool = Field(default=True, description="Whether to enable the web retriever")
    num_results: int = Field(default=5, description="The number of search results to return")
    timeout: int = Field(default=10, description="The timeout for web requests in seconds")

class CommandExecutorSettings(BaseModel):
    """Settings for the command executor"""
    enabled: bool = Field(default=True, description="Whether to enable the command executor")
    auto_execute: bool = Field(default=False, description="Whether to automatically execute commands")
    timeout: int = Field(default=30, description="The timeout for command execution in seconds")
    max_output_lines: int = Field(default=100, description="The maximum number of output lines to display")

class EnhancedSettings(BaseModel):
    """Enhanced settings for WarpAI"""
    model: ModelSettings = Field(default_factory=ModelSettings, description="Settings for the AI model")
    ui: UISettings = Field(default_factory=UISettings, description="Settings for the UI")
    code_indexer: CodeIndexerSettings = Field(default_factory=CodeIndexerSettings, description="Settings for the code indexer")
    web_retriever: WebRetrieverSettings = Field(default_factory=WebRetrieverSettings, description="Settings for the web retriever")
    command_executor: CommandExecutorSettings = Field(default_factory=CommandExecutorSettings, description="Settings for the command executor")

    @classmethod
    def load(cls, config_path: Optional[str] = None) -> "EnhancedSettings":
        """Load settings from a config file"""
        if config_path is None:
            # Use default config path
            config_dir = os.path.join(os.path.expanduser("~"), ".warpai")
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, "config.json")
        
        # Create default config if it doesn't exist
        if not os.path.exists(config_path):
            default_settings = cls()
            with open(config_path, "w") as f:
                f.write(default_settings.model_dump_json(indent=2))
            return default_settings
        
        # Load config from file
        with open(config_path, "r") as f:
            config_data = json.load(f)
        
        return cls(**config_data)
    
    def save(self, config_path: Optional[str] = None) -> None:
        """Save settings to a config file"""
        if config_path is None:
            # Use default config path
            config_dir = os.path.join(os.path.expanduser("~"), ".warpai")
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, "config.json")
        
        # Save config to file
        with open(config_path, "w") as f:
            f.write(self.model_dump_json(indent=2))

# Create default settings
settings = EnhancedSettings.load()
