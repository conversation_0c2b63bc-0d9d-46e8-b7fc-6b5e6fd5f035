"""
Settings for WarpAI
"""
import os
from pathlib import Path
from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path.home() / ".env"
load_dotenv(dotenv_path=env_path)

class Settings(BaseModel):
    """Settings for WarpAI"""

    # API settings
    gemini_api_key: str = Field(default_factory=lambda: os.getenv("GEMINI_API_KEY", ""))
    openai_api_key: str = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY", ""))
    anthropic_api_key: str = Field(default_factory=lambda: os.getenv("ANTHROPIC_API_KEY", ""))

    default_model: str = "gemini-2.0-flash"
    available_models: List[str] = [
        # Google models
        "gemini-2.0-flash",
        "gemini-2.5-pro",

        # OpenAI models
        "gpt-4o",
        "gpt-4.1",
        "o3-mini",
        "o3",
        "o4-mini",

        # Anthropic models
        "claude-3.7-sonnet",
        "claude-3.5-sonnet",
        "claude-3.5-haiku",

        # Lite model (fallback)
        "lite",
    ]

    # Model provider mapping
    model_provider: Dict[str, str] = {
        "gemini-2.0-flash": "google",
        "gemini-2.5-pro": "google",
        "gpt-4o": "openai",
        "gpt-4.1": "openai",
        "o3-mini": "openai",
        "o3": "openai",
        "o4-mini": "openai",
        "claude-3.7-sonnet": "anthropic",
        "claude-3.5-sonnet": "anthropic",
        "claude-3.5-haiku": "anthropic",
        "lite": "google",
    }

    # Agent settings
    max_conversation_history: int = 20
    max_tokens: int = 8192
    temperature: float = 0.7

    # Execution settings
    auto_execute_commands: bool = True  # Auto-execute commands by default
    auto_save_code: bool = True  # Auto-save generated code by default
    auto_install_dependencies: bool = True  # Auto-install missing dependencies
    auto_fix_errors: bool = True  # Auto-fix errors when possible

    # Enhanced settings
    model: str = "gemini-2.0-flash"  # Default model to use
    auto_execute: bool = True  # Enable auto-execution of code
    auto_install_deps: bool = True  # Enable auto-installation of dependencies
    auto_fix_errors: bool = True  # Enable auto-fixing of errors
    use_advanced_agent: bool = True  # Use advanced agent with RAG, multi-agent, and plugins

    # Agent Mode settings
    agent_mode_enabled: bool = True  # Enable Agent Mode
    agent_mode_auto_detection: bool = True  # Auto-detect natural language input
    agent_mode_auto_detection_denylist: List[str] = []  # Commands to exclude from auto-detection
    agent_mode_model: str = "gemini-2.0-flash"  # Model to use for Agent Mode

    # Autonomy settings
    autonomy_enabled: bool = True  # Enable autonomous command execution
    autonomy_model_based: bool = True  # Use model to determine if command is safe
    autonomy_read_permissions: bool = True  # Allow reading files without confirmation

    # Dispatch settings
    dispatch_enabled: bool = True  # Enable Dispatch mode
    dispatch_auto_execute: bool = False  # Auto-execute commands in Dispatch mode

    # Conversation settings
    conversation_max_tokens: int = 128000  # Maximum tokens in a conversation
    conversation_timeout: int = 10800  # Timeout for conversations in seconds (3 hours)

    # Command execution settings
    command_allowlist: List[str] = [
        r"which .*",  # Find executable locations
        r"ls(\s.*)?",  # List directory contents
        r"grep(\s.*)?",  # Search file contents
        r"find .*",  # Search for files
        r"echo(\s.*)?",  # Print text output
        r"cat(\s.*)?",  # Display file contents
        r"head(\s.*)?",  # Display beginning of file
        r"tail(\s.*)?",  # Display end of file
        r"pwd(\s.*)?",  # Print working directory
        r"whoami(\s.*)?",  # Display current user
        r"type(\s.*)?",  # Windows equivalent of cat
        r"dir(\s.*)?",  # Windows equivalent of ls
    ]

    command_denylist: List[str] = [
        r"sudo(\s.*)?",  # Block sudo commands for security
        r"rm\s+(-[a-z]+\s+)*(/|~)",  # Block dangerous rm commands
        r"rmdir\s+(/|~)",  # Block dangerous rmdir commands
        r"format\s+[a-zA-Z]:.*",  # Block format commands
        r"del\s+(/|~|[a-zA-Z]:\\)",  # Block dangerous del commands
        r"rd\s+(/|~|[a-zA-Z]:\\)",  # Block dangerous rd commands
        r"wget(\s.*)?",  # Network downloads
        r"curl(\s.*)?",  # Network requests
        r"rm(\s.*)?",  # File deletion
        r"eval(\s.*)?",  # Shell code execution
        r"exec(\s.*)?",  # Shell code execution
        r"source(\s.*)?",  # Shell script execution
        r"\.\s+.*",  # Shell script execution (dot command)
        r"ssh(\s.*)?",  # SSH connections
        r"scp(\s.*)?",  # Secure copy
        r"ftp(\s.*)?",  # FTP connections
        r"telnet(\s.*)?",  # Telnet connections
        r"nc(\s.*)?",  # Netcat
        r"ncat(\s.*)?",  # Netcat
        r"netcat(\s.*)?",  # Netcat
        r"dd(\s.*)?",  # Disk operations
        r"mkfs(\s.*)?",  # Make filesystem
        r"fdisk(\s.*)?",  # Disk partitioning
        r"shutdown(\s.*)?",  # System shutdown
        r"reboot(\s.*)?",  # System reboot
        r"halt(\s.*)?",  # System halt
        r"poweroff(\s.*)?",  # System power off
    ]

    # UI settings
    prompt_prefix: str = "WarpAI> "
    response_prefix: str = "🤖 "
    agent_mode_indicator: str = "✨"  # Sparkles icon for Agent Mode
    dispatch_mode_indicator: str = "🚀"  # Rocket icon for Dispatch Mode
    follow_up_indicator: str = "↳"  # Bent arrow for follow-up queries

    # Theme settings
    theme: str = "dark"  # Default theme (dark or light)
    input_position: str = "bottom"  # Input position (bottom or top)
    show_input_hint: bool = True  # Show input hints
    show_block_actions: bool = True  # Show block actions
    show_block_context: bool = True  # Show block context

    # Block settings
    block_sharing_enabled: bool = True  # Enable block sharing
    block_filtering_enabled: bool = True  # Enable block filtering
    background_blocks_enabled: bool = True  # Enable background blocks
    sticky_command_header: bool = True  # Enable sticky command header

    # Paths
    history_file: Path = Path.home() / ".warpai_history"

    def is_valid_shell_command(self, command: str) -> bool:
        """Check if a string is likely a valid shell command"""
        import re

        # Skip empty commands
        if not command or not isinstance(command, str) or not command.strip():
            return False

        # Common shell commands
        common_commands = [
            # File operations
            "ls", "dir", "cd", "pwd", "mkdir", "rmdir", "touch", "rm", "cp", "mv", "cat", "type",
            # System info
            "ps", "top", "htop", "free", "df", "du", "uname", "systeminfo", "whoami", "hostname",
            # Network
            "ping", "tracert", "traceroute", "netstat", "ifconfig", "ipconfig", "curl", "wget", "ssh",
            # Package managers
            "apt", "apt-get", "yum", "dnf", "brew", "pip", "npm", "gem", "conda", "choco",
            # Version control
            "git", "svn", "hg",
            # Programming languages
            "python", "python3", "node", "java", "javac", "gcc", "g++", "make", "dotnet", "go", "ruby", "perl",
            # Text editors
            "vim", "nano", "emacs", "code", "notepad",
            # Process management
            "kill", "taskkill", "bg", "fg", "jobs", "nohup",
            # Compression
            "tar", "zip", "unzip", "gzip", "gunzip",
            # File permissions
            "chmod", "chown", "attrib",
            # Search
            "find", "grep", "findstr", "select-string",
            # Other common tools
            "echo", "printf", "set", "export", "env", "man", "help", "clear", "cls", "history",
            # Windows specific
            "powershell", "cmd", "tasklist", "schtasks", "net", "reg",
        ]

        # Check if the command starts with any common command
        first_word = command.split()[0].lower()
        if first_word in common_commands:
            return True

        # Check for command patterns
        command_patterns = [
            # Path to executable
            r"^\.{0,2}/[a-zA-Z0-9_\-\./]+$",
            r"^[a-zA-Z]:\\[a-zA-Z0-9_\-\\\s\.]+$",
            # Command with options
            r"^[a-zA-Z0-9_\-\.]+\s+\-{1,2}[a-zA-Z0-9]+",
            # Executable with specific arguments (not just any arguments)
            r"^[a-zA-Z0-9_\-\.]+\s+(--?[a-zA-Z0-9_\-]+|/[a-zA-Z]|[a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)",
        ]

        for pattern in command_patterns:
            if re.match(pattern, command):
                return True

        return False

    def is_command_allowed(self, command: str) -> bool:
        """Check if a command is allowed to be executed"""
        import re

        # Check if command is None or not a string
        if command is None or not isinstance(command, str):
            return False

        # Skip empty commands
        if not command.strip():
            return False

        # Check if it's a valid shell command
        if not self.is_valid_shell_command(command):
            return False

        # Check if command is in denylist
        for pattern in self.command_denylist:
            if re.match(pattern, command):
                return False

        # Check if command is in allowlist
        for pattern in self.command_allowlist:
            if re.match(pattern, command):
                return True

        # Default to not allowed
        return False

# Create settings instance
settings = Settings()
