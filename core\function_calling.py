"""
Modern Function Calling System for WarpAI
Based on 2025 AI Agent Architecture Standards
"""
from typing import Dict, Any, List, Optional, Callable, Union, Set
from pydantic import BaseModel, Field, validator
from enum import Enum
import json
import asyncio
import time
import uuid
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ParameterType(Enum):
    STRING = "string"
    INTEGER = "integer"
    NUMBER = "number"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
    NULL = "null"

class FunctionParameter(BaseModel):
    """Function parameter definition with JSON Schema support"""
    type: ParameterType
    description: str
    enum: Optional[List[Any]] = None
    required: bool = True
    default: Any = None
    minimum: Optional[Union[int, float]] = None
    maximum: Optional[Union[int, float]] = None
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    items: Optional[Dict[str, Any]] = None  # For array types
    properties: Optional[Dict[str, Any]] = None  # For object types
    
    @validator('default')
    def validate_default(cls, v, values):
        if v is not None and 'required' in values and values['required']:
            raise ValueError("Required parameters cannot have default values")
        return v

class FunctionCategory(Enum):
    FILE_OPERATIONS = "file_operations"
    CODE_GENERATION = "code_generation"
    SYSTEM_COMMANDS = "system_commands"
    WEB_SEARCH = "web_search"
    DATA_ANALYSIS = "data_analysis"
    COMMUNICATION = "communication"
    MEMORY = "memory"
    TASK_MANAGEMENT = "task_management"
    GENERAL = "general"

class FunctionDefinition(BaseModel):
    """Modern function definition with comprehensive metadata"""
    name: str
    description: str
    parameters: Dict[str, FunctionParameter]
    returns: Dict[str, Any]
    examples: List[Dict[str, Any]] = []
    category: FunctionCategory = FunctionCategory.GENERAL
    tags: List[str] = []
    version: str = "1.0.0"
    author: str = "WarpAI"
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    deprecated: bool = False
    async_execution: bool = False
    timeout: Optional[int] = None  # Timeout in seconds
    rate_limit: Optional[int] = None  # Max calls per minute
    
    def to_openai_schema(self) -> Dict[str, Any]:
        """Convert to OpenAI function calling schema"""
        properties = {}
        required = []
        
        for param_name, param in self.parameters.items():
            prop = {
                "type": param.type.value,
                "description": param.description
            }
            
            if param.enum:
                prop["enum"] = param.enum
            if param.minimum is not None:
                prop["minimum"] = param.minimum
            if param.maximum is not None:
                prop["maximum"] = param.maximum
            if param.min_length is not None:
                prop["minLength"] = param.min_length
            if param.max_length is not None:
                prop["maxLength"] = param.max_length
            if param.pattern:
                prop["pattern"] = param.pattern
            if param.items:
                prop["items"] = param.items
            if param.properties:
                prop["properties"] = param.properties
                
            properties[param_name] = prop
            
            if param.required:
                required.append(param_name)
        
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": properties,
                "required": required
            }
        }

class FunctionCall(BaseModel):
    """Function call request"""
    name: str
    arguments: Dict[str, Any]
    call_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    context: Optional[Dict[str, Any]] = None

class FunctionResult(BaseModel):
    """Function call result"""
    call_id: str
    function_name: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = {}

class ValidationResult(BaseModel):
    """Parameter validation result"""
    success: bool
    error: Optional[str] = None
    validated_params: Dict[str, Any] = {}

class ModernFunctionCaller:
    """Modern function calling system with validation and async support"""
    
    def __init__(self):
        self.functions: Dict[str, FunctionDefinition] = {}
        self.implementations: Dict[str, Callable] = {}
        self.call_history: List[FunctionResult] = []
        self.rate_limits: Dict[str, List[datetime]] = {}
        self.max_history = 1000
        
    def register_function(self, func_def: FunctionDefinition, implementation: Callable):
        """Register a function with its implementation"""
        self.functions[func_def.name] = func_def
        self.implementations[func_def.name] = implementation
        logger.info(f"Registered function: {func_def.name} (category: {func_def.category.value})")
    
    def unregister_function(self, name: str):
        """Unregister a function"""
        if name in self.functions:
            del self.functions[name]
            del self.implementations[name]
            logger.info(f"Unregistered function: {name}")
    
    def get_function_schema(self, name: str) -> Optional[Dict[str, Any]]:
        """Get OpenAI-compatible schema for a function"""
        if name not in self.functions:
            return None
        return self.functions[name].to_openai_schema()
    
    def get_all_schemas(self) -> List[Dict[str, Any]]:
        """Get all function schemas in OpenAI format"""
        return [func_def.to_openai_schema() for func_def in self.functions.values() if not func_def.deprecated]
    
    def search_functions(self, query: str, category: Optional[FunctionCategory] = None) -> List[FunctionDefinition]:
        """Search functions by name, description, or tags"""
        query_lower = query.lower()
        results = []
        
        for func_def in self.functions.values():
            if func_def.deprecated:
                continue
                
            if category and func_def.category != category:
                continue
                
            # Search in name, description, and tags
            if (query_lower in func_def.name.lower() or 
                query_lower in func_def.description.lower() or
                any(query_lower in tag.lower() for tag in func_def.tags)):
                results.append(func_def)
        
        return results
    
    def _validate_parameters(self, func_def: FunctionDefinition, arguments: Dict[str, Any]) -> ValidationResult:
        """Validate function parameters"""
        validated_params = {}
        
        # Check required parameters
        for param_name, param in func_def.parameters.items():
            if param.required and param_name not in arguments:
                return ValidationResult(
                    success=False,
                    error=f"Missing required parameter: {param_name}"
                )
        
        # Validate each parameter
        for param_name, value in arguments.items():
            if param_name not in func_def.parameters:
                return ValidationResult(
                    success=False,
                    error=f"Unknown parameter: {param_name}"
                )
            
            param = func_def.parameters[param_name]
            
            # Type validation
            if not self._validate_type(value, param):
                return ValidationResult(
                    success=False,
                    error=f"Parameter '{param_name}' has invalid type. Expected {param.type.value}, got {type(value).__name__}"
                )
            
            # Additional validations
            validation_error = self._validate_constraints(param_name, value, param)
            if validation_error:
                return ValidationResult(
                    success=False,
                    error=validation_error
                )
            
            validated_params[param_name] = value
        
        # Add default values for missing optional parameters
        for param_name, param in func_def.parameters.items():
            if not param.required and param_name not in validated_params and param.default is not None:
                validated_params[param_name] = param.default
        
        return ValidationResult(
            success=True,
            validated_params=validated_params
        )
    
    def _validate_type(self, value: Any, param: FunctionParameter) -> bool:
        """Validate parameter type"""
        if param.type == ParameterType.STRING:
            return isinstance(value, str)
        elif param.type == ParameterType.INTEGER:
            return isinstance(value, int)
        elif param.type == ParameterType.NUMBER:
            return isinstance(value, (int, float))
        elif param.type == ParameterType.BOOLEAN:
            return isinstance(value, bool)
        elif param.type == ParameterType.ARRAY:
            return isinstance(value, list)
        elif param.type == ParameterType.OBJECT:
            return isinstance(value, dict)
        elif param.type == ParameterType.NULL:
            return value is None
        return False
    
    def _validate_constraints(self, param_name: str, value: Any, param: FunctionParameter) -> Optional[str]:
        """Validate parameter constraints"""
        # Enum validation
        if param.enum and value not in param.enum:
            return f"Parameter '{param_name}' must be one of: {param.enum}"
        
        # Numeric constraints
        if param.type in [ParameterType.INTEGER, ParameterType.NUMBER]:
            if param.minimum is not None and value < param.minimum:
                return f"Parameter '{param_name}' must be >= {param.minimum}"
            if param.maximum is not None and value > param.maximum:
                return f"Parameter '{param_name}' must be <= {param.maximum}"
        
        # String constraints
        if param.type == ParameterType.STRING:
            if param.min_length is not None and len(value) < param.min_length:
                return f"Parameter '{param_name}' must be at least {param.min_length} characters"
            if param.max_length is not None and len(value) > param.max_length:
                return f"Parameter '{param_name}' must be at most {param.max_length} characters"
            if param.pattern:
                import re
                if not re.match(param.pattern, value):
                    return f"Parameter '{param_name}' does not match required pattern"
        
        return None
    
    def _check_rate_limit(self, func_name: str) -> bool:
        """Check if function call is within rate limits"""
        func_def = self.functions.get(func_name)
        if not func_def or not func_def.rate_limit:
            return True
        
        now = datetime.now()
        if func_name not in self.rate_limits:
            self.rate_limits[func_name] = []
        
        # Remove calls older than 1 minute
        self.rate_limits[func_name] = [
            call_time for call_time in self.rate_limits[func_name]
            if (now - call_time).total_seconds() < 60
        ]
        
        # Check if we're within the rate limit
        return len(self.rate_limits[func_name]) < func_def.rate_limit
    
    async def call_function(self, call: FunctionCall) -> FunctionResult:
        """Execute a function call with full validation and error handling"""
        start_time = time.time()
        
        try:
            # Validate function exists
            if call.name not in self.functions:
                return FunctionResult(
                    call_id=call.call_id,
                    function_name=call.name,
                    success=False,
                    error=f"Function '{call.name}' not found"
                )
            
            func_def = self.functions[call.name]
            
            # Check if function is deprecated
            if func_def.deprecated:
                return FunctionResult(
                    call_id=call.call_id,
                    function_name=call.name,
                    success=False,
                    error=f"Function '{call.name}' is deprecated"
                )
            
            # Check rate limits
            if not self._check_rate_limit(call.name):
                return FunctionResult(
                    call_id=call.call_id,
                    function_name=call.name,
                    success=False,
                    error=f"Rate limit exceeded for function '{call.name}'"
                )
            
            # Validate parameters
            validation_result = self._validate_parameters(func_def, call.arguments)
            if not validation_result.success:
                return FunctionResult(
                    call_id=call.call_id,
                    function_name=call.name,
                    success=False,
                    error=validation_result.error
                )
            
            # Record rate limit call
            if func_def.rate_limit:
                if call.name not in self.rate_limits:
                    self.rate_limits[call.name] = []
                self.rate_limits[call.name].append(datetime.now())
            
            # Execute function
            implementation = self.implementations[call.name]
            
            if func_def.async_execution or asyncio.iscoroutinefunction(implementation):
                if func_def.timeout:
                    result = await asyncio.wait_for(
                        implementation(**validation_result.validated_params),
                        timeout=func_def.timeout
                    )
                else:
                    result = await implementation(**validation_result.validated_params)
            else:
                result = implementation(**validation_result.validated_params)
            
            execution_time = time.time() - start_time
            
            function_result = FunctionResult(
                call_id=call.call_id,
                function_name=call.name,
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "category": func_def.category.value,
                    "version": func_def.version
                }
            )
            
            # Add to history
            self.call_history.append(function_result)
            if len(self.call_history) > self.max_history:
                self.call_history = self.call_history[-self.max_history:]
            
            return function_result
            
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            return FunctionResult(
                call_id=call.call_id,
                function_name=call.name,
                success=False,
                error=f"Function '{call.name}' timed out after {func_def.timeout} seconds",
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error executing function {call.name}: {e}")
            return FunctionResult(
                call_id=call.call_id,
                function_name=call.name,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def call_multiple_functions(self, calls: List[FunctionCall]) -> List[FunctionResult]:
        """Execute multiple function calls concurrently"""
        tasks = [self.call_function(call) for call in calls]
        return await asyncio.gather(*tasks)
    
    def get_call_history(self, function_name: Optional[str] = None, limit: int = 100) -> List[FunctionResult]:
        """Get function call history"""
        history = self.call_history
        
        if function_name:
            history = [result for result in history if result.function_name == function_name]
        
        return history[-limit:]
    
    def get_function_stats(self, function_name: str) -> Dict[str, Any]:
        """Get statistics for a function"""
        history = self.get_call_history(function_name)
        
        if not history:
            return {"total_calls": 0}
        
        successful_calls = [r for r in history if r.success]
        failed_calls = [r for r in history if not r.success]
        
        avg_execution_time = sum(r.execution_time for r in successful_calls) / len(successful_calls) if successful_calls else 0
        
        return {
            "total_calls": len(history),
            "successful_calls": len(successful_calls),
            "failed_calls": len(failed_calls),
            "success_rate": len(successful_calls) / len(history) if history else 0,
            "average_execution_time": avg_execution_time,
            "last_call": history[-1].timestamp if history else None
        }
