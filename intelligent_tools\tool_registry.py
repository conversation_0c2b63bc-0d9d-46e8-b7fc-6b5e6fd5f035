"""
Tool Registry for WarpAI Intelligent Tools

This module provides a comprehensive registry of all available tools and their capabilities.
"""

import os
import re
import json
import subprocess
import shutil
import logging
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from warpai.intelligent_tools.tool_implementations import ToolImplementations

logger = logging.getLogger(__name__)

class ToolCategory(Enum):
    """Categories of tools available in the system"""
    SHELL = "shell"
    FILE_OPERATIONS = "file_operations"
    WEB_OPERATIONS = "web_operations"
    CODE_ANALYSIS = "code_analysis"
    CODE_GENERATION = "code_generation"
    PACKAGE_MANAGEMENT = "package_management"
    GIT_OPERATIONS = "git_operations"
    SYSTEM_OPERATIONS = "system_operations"
    AI_OPERATIONS = "ai_operations"

@dataclass
class ToolDefinition:
    """Definition of a tool with its capabilities and metadata"""
    name: str
    category: ToolCategory
    description: str
    function: Callable
    parameters: Dict[str, Any]
    keywords: List[str]
    confidence_threshold: float = 0.7
    requires_confirmation: bool = False
    can_chain: bool = True

class ToolRegistry:
    """Registry for all available tools in the WarpAI system"""

    def __init__(self, api_key: str = None, model: str = None):
        """Initialize the tool registry"""
        self.tools: Dict[str, ToolDefinition] = {}
        self.category_tools: Dict[ToolCategory, List[str]] = {}
        self.keyword_map: Dict[str, List[str]] = {}

        # Initialize tool implementations
        self.implementations = ToolImplementations(api_key=api_key, model=model)

        # Initialize all tool categories
        for category in ToolCategory:
            self.category_tools[category] = []

        # Register all built-in tools
        self._register_builtin_tools()

        logger.info(f"Tool registry initialized with {len(self.tools)} tools")

    def _register_builtin_tools(self):
        """Register all built-in tools"""

        # Shell Operations
        self.register_tool(ToolDefinition(
            name="shell",
            category=ToolCategory.SHELL,
            description="Execute shell commands and scripts",
            function=self.implementations.execute_shell_command,
            parameters={"command": str, "working_dir": str},
            keywords=["run", "execute", "command", "shell", "terminal", "cmd", "powershell", "bash"]
        ))

        # File Operations
        self.register_tool(ToolDefinition(
            name="readFile",
            category=ToolCategory.FILE_OPERATIONS,
            description="Read contents of a file",
            function=self.implementations.read_file,
            parameters={"file_path": str, "encoding": str},
            keywords=["read", "open", "view", "show", "display", "content", "file"]
        ))

        self.register_tool(ToolDefinition(
            name="saveFile",
            category=ToolCategory.FILE_OPERATIONS,
            description="Save content to a file",
            function=self.implementations.save_file,
            parameters={"file_path": str, "content": str, "encoding": str},
            keywords=["save", "write", "create", "new file", "store"]
        ))

        self.register_tool(ToolDefinition(
            name="editFile",
            category=ToolCategory.FILE_OPERATIONS,
            description="Edit existing file content",
            function=self.implementations.edit_file,
            parameters={"file_path": str, "changes": dict},
            keywords=["edit", "modify", "change", "update", "alter"]
        ))

        self.register_tool(ToolDefinition(
            name="removeFiles",
            category=ToolCategory.FILE_OPERATIONS,
            description="Remove files or directories",
            function=self.implementations.remove_files,
            parameters={"paths": list},
            keywords=["delete", "remove", "rm", "del", "unlink"],
            requires_confirmation=True
        ))

        # Web Operations
        self.register_tool(ToolDefinition(
            name="webFetch",
            category=ToolCategory.WEB_OPERATIONS,
            description="Fetch content from web URLs",
            function=self.implementations.web_fetch,
            parameters={"url": str, "headers": dict},
            keywords=["fetch", "download", "web", "url", "http", "https", "get"]
        ))

        # Code Analysis Tools
        self.register_tool(ToolDefinition(
            name="analyzeCode",
            category=ToolCategory.CODE_ANALYSIS,
            description="Analyze code for issues, patterns, and improvements",
            function=self.implementations.analyze_code,
            parameters={"code": str, "language": str, "analysis_type": str},
            keywords=["analyze", "check", "review", "inspect", "audit", "quality"]
        ))

        self.register_tool(ToolDefinition(
            name="debugCode",
            category=ToolCategory.CODE_ANALYSIS,
            description="Debug code and find issues",
            function=self.implementations.debug_code,
            parameters={"code": str, "error_message": str, "language": str},
            keywords=["debug", "fix", "error", "bug", "issue", "problem", "troubleshoot"]
        ))

        # Code Generation Tools
        self.register_tool(ToolDefinition(
            name="generateCode",
            category=ToolCategory.CODE_GENERATION,
            description="Generate code based on requirements",
            function=self.implementations.generate_code,
            parameters={"requirements": str, "language": str, "style": str},
            keywords=["generate", "create", "write", "code", "implement", "build", "develop"]
        ))

        self.register_tool(ToolDefinition(
            name="generateTests",
            category=ToolCategory.CODE_GENERATION,
            description="Generate unit tests for code",
            function=self.implementations.generate_tests,
            parameters={"code": str, "language": str, "test_framework": str},
            keywords=["test", "unit test", "testing", "spec", "verify"]
        ))

        # Package Management
        self.register_tool(ToolDefinition(
            name="installPackage",
            category=ToolCategory.PACKAGE_MANAGEMENT,
            description="Install packages and dependencies",
            function=self.implementations.install_package,
            parameters={"package_name": str, "package_manager": str, "version": str},
            keywords=["install", "add", "dependency", "package", "library", "module"]
        ))

        self.register_tool(ToolDefinition(
            name="listPackages",
            category=ToolCategory.PACKAGE_MANAGEMENT,
            description="List installed packages",
            function=self.implementations.list_packages,
            parameters={"package_manager": str},
            keywords=["list", "show", "packages", "dependencies", "installed"]
        ))

        # Git Operations
        self.register_tool(ToolDefinition(
            name="gitStatus",
            category=ToolCategory.GIT_OPERATIONS,
            description="Check git repository status",
            function=self.implementations.git_status,
            parameters={"repo_path": str},
            keywords=["git", "status", "changes", "repository", "version control"]
        ))

        # System Operations
        self.register_tool(ToolDefinition(
            name="showDir",
            category=ToolCategory.SYSTEM_OPERATIONS,
            description="Show directory contents",
            function=self.implementations.show_directory,
            parameters={"path": str, "recursive": bool},
            keywords=["ls", "dir", "list", "directory", "folder", "contents"]
        ))

        self.register_tool(ToolDefinition(
            name="createFolder",
            category=ToolCategory.SYSTEM_OPERATIONS,
            description="Create new directories",
            function=self.implementations.create_folder,
            parameters={"path": str, "recursive": bool},
            keywords=["mkdir", "create", "directory", "folder", "new"]
        ))

        # AI Operations
        self.register_tool(ToolDefinition(
            name="remember",
            category=ToolCategory.AI_OPERATIONS,
            description="Store information in memory for future reference",
            function=self.implementations.remember,
            parameters={"information": str, "category": str},
            keywords=["remember", "store", "memory", "save", "note", "recall"]
        ))

    def register_tool(self, tool: ToolDefinition):
        """Register a new tool in the registry"""
        self.tools[tool.name] = tool
        self.category_tools[tool.category].append(tool.name)

        # Build keyword mapping
        for keyword in tool.keywords:
            if keyword not in self.keyword_map:
                self.keyword_map[keyword] = []
            self.keyword_map[keyword].append(tool.name)

        logger.debug(f"Registered tool: {tool.name} in category {tool.category.value}")

    def get_tool(self, name: str) -> Optional[ToolDefinition]:
        """Get a tool by name"""
        return self.tools.get(name)

    def get_tools_by_category(self, category: ToolCategory) -> List[ToolDefinition]:
        """Get all tools in a specific category"""
        tool_names = self.category_tools.get(category, [])
        return [self.tools[name] for name in tool_names if name in self.tools]

    def search_tools_by_keywords(self, keywords: List[str]) -> List[ToolDefinition]:
        """Search tools by keywords"""
        matching_tools = set()

        for keyword in keywords:
            keyword_lower = keyword.lower()
            for tool_keyword, tool_names in self.keyword_map.items():
                if keyword_lower in tool_keyword.lower():
                    matching_tools.update(tool_names)

        return [self.tools[name] for name in matching_tools if name in self.tools]

    def get_all_tools(self) -> List[ToolDefinition]:
        """Get all registered tools"""
        return list(self.tools.values())


