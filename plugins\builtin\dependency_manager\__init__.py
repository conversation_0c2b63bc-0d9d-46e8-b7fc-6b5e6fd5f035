"""
Dependency Manager plugin for WarpAI
"""
import os
import re
import sys
import subprocess
import platform
from typing import Dict, List, Any, Callable, Optional, Tuple, Set

from warpai.plugins.plugin_manager import Plugin

class DependencyManagerPlugin(Plugin):
    """Dependency Manager plugin for WarpAI"""
    
    # Plugin metadata
    name = "dependency_manager"
    description = "Automatically detects and installs missing dependencies"
    version = "1.0.0"
    author = "WarpAI Team"
    
    def __init__(self):
        """Initialize the plugin"""
        super().__init__()
        self.package_managers = {
            "python": {
                "check": ["pip", "list"],
                "install": ["pip", "install"],
                "uninstall": ["pip", "uninstall", "-y"],
                "list": ["pip", "list"],
                "pattern": r"^([a-zA-Z0-9_\-]+)\s+(\d+\.\d+.*?)$"
            },
            "node": {
                "check": ["npm", "list", "--depth=0"],
                "install": ["npm", "install"],
                "uninstall": ["npm", "uninstall"],
                "list": ["npm", "list", "--depth=0"],
                "pattern": r"[├└]─+\s([a-zA-Z0-9_\-]+)@(\d+\.\d+.*?)$"
            }
        }
        
        # Cache of installed packages
        self.installed_packages = {
            "python": set(),
            "node": set()
        }
        
        # Initialize the cache
        self._update_package_cache()
    
    def initialize(self) -> bool:
        """Initialize the plugin"""
        return True
    
    def get_commands(self) -> Dict[str, Callable]:
        """Get the commands provided by this plugin"""
        return {
            "install_dependency": self.install_dependency,
            "check_dependency": self.check_dependency,
            "list_dependencies": self.list_dependencies,
        }
    
    def get_hooks(self) -> Dict[str, Callable]:
        """Get the hooks provided by this plugin"""
        return {
            "before_execute_command": self.before_execute_command,
            "after_execute_command": self.after_execute_command,
        }
    
    def _update_package_cache(self) -> None:
        """Update the cache of installed packages"""
        for pkg_type, commands in self.package_managers.items():
            try:
                result = subprocess.run(commands["list"], 
                                      capture_output=True, check=True, text=True)
                
                # Parse the output to get installed packages
                pattern = commands["pattern"]
                matches = re.finditer(pattern, result.stdout, re.MULTILINE)
                
                # Update the cache
                self.installed_packages[pkg_type] = {match.group(1).lower() for match in matches}
            except (subprocess.SubprocessError, FileNotFoundError):
                # Package manager not available
                self.installed_packages[pkg_type] = set()
    
    def check_dependency(self, package: str, pkg_type: str = "python") -> bool:
        """
        Check if a dependency is installed
        
        Args:
            package: The package name
            pkg_type: The package type (python, node)
            
        Returns:
            True if the package is installed, False otherwise
        """
        # Update the cache if it's empty
        if not self.installed_packages[pkg_type]:
            self._update_package_cache()
            
        # Check if the package is installed
        return package.lower() in self.installed_packages[pkg_type]
    
    def install_dependency(self, package: str, pkg_type: str = "python") -> str:
        """
        Install a dependency
        
        Args:
            package: The package name
            pkg_type: The package type (python, node)
            
        Returns:
            The installation output
        """
        if pkg_type not in self.package_managers:
            return f"Unsupported package type: {pkg_type}"
            
        # Check if the package is already installed
        if self.check_dependency(package, pkg_type):
            return f"Package {package} is already installed"
            
        # Install the package
        try:
            result = subprocess.run(self.package_managers[pkg_type]["install"] + [package], 
                                  capture_output=True, check=True, text=True)
            
            # Update the cache
            self.installed_packages[pkg_type].add(package.lower())
            
            return f"Successfully installed {package}:\n{result.stdout}"
        except subprocess.SubprocessError as e:
            return f"Error installing {package}: {e}"
    
    def list_dependencies(self, pkg_type: str = "python") -> str:
        """
        List installed dependencies
        
        Args:
            pkg_type: The package type (python, node)
            
        Returns:
            The list of installed dependencies
        """
        if pkg_type not in self.package_managers:
            return f"Unsupported package type: {pkg_type}"
            
        # Update the cache
        self._update_package_cache()
        
        # Return the list of installed packages
        packages = sorted(self.installed_packages[pkg_type])
        return f"Installed {pkg_type} packages:\n" + "\n".join(packages)
    
    def before_execute_command(self, command: str) -> Optional[str]:
        """
        Hook that runs before a command is executed
        
        Args:
            command: The command to execute
            
        Returns:
            Modified command or None
        """
        # Check for Python imports
        if command.startswith("python "):
            # Extract the file path
            parts = command.split(" ")
            if len(parts) >= 2:
                file_path = parts[1]
                if os.path.exists(file_path):
                    # Check for imports in the file
                    missing_imports = self._check_python_imports(file_path)
                    if missing_imports:
                        print(f"Installing missing Python dependencies: {', '.join(missing_imports)}")
                        for package in missing_imports:
                            self.install_dependency(package, "python")
        
        # Check for Node.js requires
        elif command.startswith("node "):
            # Extract the file path
            parts = command.split(" ")
            if len(parts) >= 2:
                file_path = parts[1]
                if os.path.exists(file_path):
                    # Check for requires in the file
                    missing_requires = self._check_node_requires(file_path)
                    if missing_requires:
                        print(f"Installing missing Node.js dependencies: {', '.join(missing_requires)}")
                        for package in missing_requires:
                            self.install_dependency(package, "node")
        
        return None
    
    def after_execute_command(self, command: str, exit_code: int, output: str) -> Optional[str]:
        """
        Hook that runs after a command is executed
        
        Args:
            command: The executed command
            exit_code: The command exit code
            output: The command output
            
        Returns:
            Modified output or None
        """
        # Check for Python ModuleNotFoundError
        if exit_code != 0 and "ModuleNotFoundError: No module named" in output:
            # Extract the missing module
            match = re.search(r"ModuleNotFoundError: No module named '([^']+)'", output)
            if match:
                missing_module = match.group(1)
                print(f"Installing missing Python module: {missing_module}")
                result = self.install_dependency(missing_module, "python")
                return f"{output}\n\n{result}\n\nPlease run the command again."
        
        # Check for Node.js module not found
        elif exit_code != 0 and "Error: Cannot find module" in output:
            # Extract the missing module
            match = re.search(r"Error: Cannot find module '([^']+)'", output)
            if match:
                missing_module = match.group(1)
                print(f"Installing missing Node.js module: {missing_module}")
                result = self.install_dependency(missing_module, "node")
                return f"{output}\n\n{result}\n\nPlease run the command again."
        
        return None
    
    def _check_python_imports(self, file_path: str) -> List[str]:
        """
        Check for missing Python imports in a file
        
        Args:
            file_path: The file path
            
        Returns:
            List of missing packages
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                
            # Find import statements
            import_patterns = [
                r"^import\s+([a-zA-Z0-9_]+)",
                r"^from\s+([a-zA-Z0-9_]+)\s+import",
            ]
            
            # Extract imported modules
            imported_modules = set()
            for pattern in import_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    module = match.group(1)
                    # Skip standard library modules
                    if not self._is_stdlib_module(module):
                        imported_modules.add(module)
            
            # Check which modules are not installed
            missing_modules = []
            for module in imported_modules:
                if not self.check_dependency(module, "python"):
                    missing_modules.append(module)
            
            return missing_modules
        except Exception:
            return []
    
    def _check_node_requires(self, file_path: str) -> List[str]:
        """
        Check for missing Node.js requires in a file
        
        Args:
            file_path: The file path
            
        Returns:
            List of missing packages
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                
            # Find require statements
            require_pattern = r"require\(['\"]([^./][^'\"]+)['\"]\)"
            
            # Extract required modules
            required_modules = set()
            matches = re.finditer(require_pattern, content)
            for match in matches:
                module = match.group(1)
                # Extract the package name (before any /)
                if "/" in module:
                    module = module.split("/")[0]
                required_modules.add(module)
            
            # Check which modules are not installed
            missing_modules = []
            for module in required_modules:
                if not self.check_dependency(module, "node"):
                    missing_modules.append(module)
            
            return missing_modules
        except Exception:
            return []
    
    def _is_stdlib_module(self, module_name: str) -> bool:
        """
        Check if a module is part of the Python standard library
        
        Args:
            module_name: The module name
            
        Returns:
            True if the module is part of the standard library, False otherwise
        """
        stdlib_modules = {
            "abc", "argparse", "ast", "asyncio", "base64", "collections", "concurrent", 
            "contextlib", "copy", "csv", "datetime", "decimal", "difflib", "enum", 
            "functools", "glob", "gzip", "hashlib", "heapq", "hmac", "html", "http", 
            "importlib", "inspect", "io", "itertools", "json", "logging", "math", 
            "multiprocessing", "operator", "os", "pathlib", "pickle", "platform", 
            "pprint", "random", "re", "shutil", "signal", "socket", "sqlite3", 
            "statistics", "string", "struct", "subprocess", "sys", "tempfile", 
            "threading", "time", "timeit", "traceback", "typing", "unittest", 
            "urllib", "uuid", "warnings", "weakref", "xml", "zipfile"
        }
        
        return module_name in stdlib_modules
